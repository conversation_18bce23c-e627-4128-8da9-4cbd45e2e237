/**
 * Admin Layout Conflict Resolution Script
 * 
 * This script identifies and fixes admin layout conflicts by:
 * 1. Finding all files using the old AdminLayout
 * 2. Updating them to use EnhancedAdminLayout
 * 3. Providing a report of changes made
 */

const fs = require('fs');
const path = require('path');

// Configuration
const ADMIN_PAGES_DIR = path.join(__dirname, 'src', 'pages', 'admin');
const COMPONENTS_DIR = path.join(__dirname, 'src', 'components');

// Results tracking
const results = {
  filesScanned: 0,
  filesUpdated: 0,
  filesWithOldLayout: [],
  filesWithEnhancedLayout: [],
  filesWithBothLayouts: [],
  errors: []
};

/**
 * Check if a file contains AdminLayout import
 */
function hasOldAdminLayout(content) {
  return content.includes('import AdminLayout from') && 
         !content.includes('EnhancedAdminLayout');
}

/**
 * Check if a file contains EnhancedAdminLayout import
 */
function hasEnhancedAdminLayout(content) {
  return content.includes('import EnhancedAdminLayout from') ||
         content.includes('() => import(\'@/components/admin/EnhancedAdminLayout\')');
}

/**
 * Update file to use EnhancedAdminLayout
 */
function updateToEnhancedLayout(content) {
  // Replace import statement
  let updatedContent = content.replace(
    /import AdminLayout from ['"]@\/components\/admin\/AdminLayout['"];?/g,
    `import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);`
  );

  // Replace usage in JSX
  updatedContent = updatedContent.replace(
    /<AdminLayout([^>]*)>/g,
    '<EnhancedAdminLayout$1>'
  );
  
  updatedContent = updatedContent.replace(
    /<\/AdminLayout>/g,
    '</EnhancedAdminLayout>'
  );

  return updatedContent;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(__dirname, filePath);
    
    results.filesScanned++;

    const hasOld = hasOldAdminLayout(content);
    const hasEnhanced = hasEnhancedAdminLayout(content);

    if (hasOld && hasEnhanced) {
      results.filesWithBothLayouts.push(relativePath);
      console.log(`⚠️  CONFLICT: ${relativePath} uses both layouts`);
    } else if (hasOld) {
      results.filesWithOldLayout.push(relativePath);
      console.log(`🔄 UPDATING: ${relativePath}`);
      
      const updatedContent = updateToEnhancedLayout(content);
      fs.writeFileSync(filePath, updatedContent, 'utf8');
      results.filesUpdated++;
      
      console.log(`✅ UPDATED: ${relativePath}`);
    } else if (hasEnhanced) {
      results.filesWithEnhancedLayout.push(relativePath);
      console.log(`✅ ALREADY ENHANCED: ${relativePath}`);
    }
  } catch (error) {
    const relativePath = path.relative(__dirname, filePath);
    results.errors.push({ file: relativePath, error: error.message });
    console.error(`❌ ERROR processing ${relativePath}:`, error.message);
  }
}

/**
 * Recursively scan directory for JS files
 */
function scanDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`Directory not found: ${dirPath}`);
    return;
  }

  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      scanDirectory(itemPath);
    } else if (item.endsWith('.js') || item.endsWith('.jsx')) {
      processFile(itemPath);
    }
  }
}

/**
 * Generate report
 */
function generateReport() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 ADMIN LAYOUT CONFLICT RESOLUTION REPORT');
  console.log('='.repeat(60));
  
  console.log(`\n📁 Files Scanned: ${results.filesScanned}`);
  console.log(`🔄 Files Updated: ${results.filesUpdated}`);
  console.log(`⚠️  Files with Conflicts: ${results.filesWithBothLayouts.length}`);
  console.log(`❌ Errors: ${results.errors.length}`);

  if (results.filesWithOldLayout.length > 0) {
    console.log('\n🔄 Files Updated to EnhancedAdminLayout:');
    results.filesWithOldLayout.forEach(file => console.log(`   - ${file}`));
  }

  if (results.filesWithEnhancedLayout.length > 0) {
    console.log('\n✅ Files Already Using EnhancedAdminLayout:');
    results.filesWithEnhancedLayout.forEach(file => console.log(`   - ${file}`));
  }

  if (results.filesWithBothLayouts.length > 0) {
    console.log('\n⚠️  Files with Layout Conflicts (Manual Review Required):');
    results.filesWithBothLayouts.forEach(file => console.log(`   - ${file}`));
  }

  if (results.errors.length > 0) {
    console.log('\n❌ Errors Encountered:');
    results.errors.forEach(({ file, error }) => console.log(`   - ${file}: ${error}`));
  }

  console.log('\n' + '='.repeat(60));
  
  if (results.filesUpdated > 0) {
    console.log('🎉 SUCCESS: Admin layout conflicts have been resolved!');
    console.log('💡 NEXT STEPS:');
    console.log('   1. Test your admin pages to ensure they work correctly');
    console.log('   2. Remove the old AdminLayout.js file if no longer needed');
    console.log('   3. Update any remaining manual imports');
  } else {
    console.log('✨ No conflicts found - all admin pages are already using EnhancedAdminLayout!');
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting Admin Layout Conflict Resolution...\n');
  
  // Scan admin pages
  console.log('📁 Scanning admin pages...');
  scanDirectory(ADMIN_PAGES_DIR);
  
  // Scan components (in case there are any admin components using old layout)
  console.log('\n📁 Scanning admin components...');
  const adminComponentsDir = path.join(COMPONENTS_DIR, 'admin');
  if (fs.existsSync(adminComponentsDir)) {
    scanDirectory(adminComponentsDir);
  }
  
  // Generate report
  generateReport();
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  processFile,
  scanDirectory,
  generateReport,
  results
};
