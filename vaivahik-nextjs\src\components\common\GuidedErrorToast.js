import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  Button, 
  useTheme, 
  Paper, 
  Typography, 
  Box, 
  Collapse, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText,
  IconButton
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ErrorIcon from '@mui/icons-material/Error';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InfoIcon from '@mui/icons-material/Info';
import WarningIcon from '@mui/icons-material/Warning';
import CloseIcon from '@mui/icons-material/Close';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

/**
 * GuidedErrorToast Component
 * 
 * An enhanced toast notification component that provides guided error resolution.
 * It shows error messages with step-by-step instructions to help users resolve issues.
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the toast is visible
 * @param {string} props.message - Main message to display
 * @param {string} props.type - Type of toast: 'error', 'warning', 'info', 'success'
 * @param {string} props.errorCode - Error code (optional)
 * @param {Array} props.steps - Array of steps to resolve the error
 * @param {Function} props.onClose - Function to call when the toast is closed
 * @param {number} props.duration - Duration in milliseconds to show the toast
 * @param {Object} props.position - Position of the toast
 * @param {Function} props.onHelp - Function to call when the help button is clicked (optional)
 */
const GuidedErrorToast = ({
  open,
  message,
  type = 'error',
  errorCode,
  steps = [],
  onClose,
  duration = 10000,
  position = { vertical: 'bottom', horizontal: 'right' },
  onHelp
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);
  
  // Get the appropriate icon based on the toast type
  const getIcon = () => {
    switch (type) {
      case 'error':
        return <ErrorIcon color="error" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'info':
        return <InfoIcon color="info" />;
      case 'success':
        return <CheckCircleIcon color="success" />;
      default:
        return <InfoIcon color="info" />;
    }
  };
  
  // Get the appropriate color based on the toast type
  const getColor = () => {
    switch (type) {
      case 'error':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
      case 'success':
        return theme.palette.success.main;
      default:
        return theme.palette.info.main;
    }
  };
  
  // Toggle the expanded state
  const toggleExpanded = (e) => {
    e.stopPropagation();
    setExpanded(!expanded);
  };
  
  // Handle help button click
  const handleHelpClick = (e) => {
    e.stopPropagation();
    if (onHelp) {
      onHelp();
    }
    onClose(e, 'helpClick');
  };
  
  return (
    <Snackbar
      open={open}
      autoHideDuration={duration}
      onClose={onClose}
      anchorOrigin={position}
    >
      <Paper
        elevation={3}
        sx={{
          minWidth: 300,
          maxWidth: 500,
          borderLeft: `4px solid ${getColor()}`,
          overflow: 'hidden'
        }}
      >
        <Box sx={{ p: 2 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box sx={{ mr: 1 }}>{getIcon()}</Box>
            <Typography variant="subtitle1" sx={{ flex: 1, fontWeight: 'bold' }}>
              {message}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {steps.length > 0 && (
                <IconButton size="small" onClick={toggleExpanded} aria-label={expanded ? "Collapse" : "Expand"}>
                  {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
              <IconButton size="small" onClick={onClose} aria-label="Close">
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
          
          {/* Error Code */}
          {errorCode && (
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              Error Code: {errorCode}
            </Typography>
          )}
          
          {/* Resolution Steps */}
          {steps.length > 0 && (
            <Collapse in={expanded} timeout="auto">
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1, mb: 1 }}>
                Try these steps to resolve the issue:
              </Typography>
              <List dense disablePadding>
                {steps.map((step, index) => (
                  <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 24 }}>
                      <ArrowRightIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={step} 
                      primaryTypographyProps={{ variant: 'body2' }} 
                    />
                  </ListItem>
                ))}
              </List>
              
              {/* Help Button */}
              {onHelp && (
                <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    size="small"
                    startIcon={<HelpOutlineIcon />}
                    onClick={handleHelpClick}
                    color="primary"
                  >
                    Get More Help
                  </Button>
                </Box>
              )}
            </Collapse>
          )}
        </Box>
      </Paper>
    </Snackbar>
  );
};

export default GuidedErrorToast;
