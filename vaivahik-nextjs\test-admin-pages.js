// Test script for admin pages: verification queue, reported profiles, and premium plans
const puppeteer = require('puppeteer');

async function testAdminPages() {
  console.log('Starting test for admin pages...');
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();

  try {
    // Test Verification Queue Page
    console.log('Testing Verification Queue page...');
    await page.goto('http://localhost:3000/admin/verification-queue');
    await page.waitForSelector('.data-table', { timeout: 5000 });

    // Check if table is loaded
    const verificationTableExists = await page.evaluate(() => {
      return document.querySelector('.data-table tbody tr') !== null;
    });
    console.log('Verification table loaded:', verificationTableExists);

    // Test view details functionality
    console.log('Testing view verification details...');
    await page.click('.action-btn.view-btn');
    await page.waitForSelector('.modal-overlay', { visible: true, timeout: 5000 });

    // Check if modal is displayed
    const verificationModalVisible = await page.evaluate(() => {
      return document.querySelector('.modal-overlay').style.display !== 'none';
    });
    console.log('Verification modal visible:', verificationModalVisible);

    // Close the modal
    await page.click('.modal-close-button');
    await page.waitForTimeout(1000);

    // Test approve functionality
    console.log('Testing approve verification...');
    // Find a pending verification
    const pendingVerificationExists = await page.evaluate(() => {
      const pendingRows = Array.from(document.querySelectorAll('.data-table tbody tr')).filter(row => {
        return row.querySelector('.status-badge.pending') !== null;
      });
      if (pendingRows.length > 0) {
        const approveBtn = pendingRows[0].querySelector('.action-btn.approve-btn');
        if (approveBtn) approveBtn.click();
        return true;
      }
      return false;
    });

    if (pendingVerificationExists) {
      await page.waitForSelector('.confirmation-modal', { visible: true, timeout: 5000 });
      await page.click('.confirmation-modal .btn-success');
      console.log('Approve verification test completed');
    } else {
      console.log('No pending verifications found to test approve functionality');
    }

    await page.waitForTimeout(1000);

    // Test Reported Profiles Page
    console.log('\nTesting Reported Profiles page...');
    await page.goto('http://localhost:3000/admin/reported-profiles');
    await page.waitForSelector('.data-table', { timeout: 5000 });

    // Check if table is loaded
    const reportedTableExists = await page.evaluate(() => {
      return document.querySelector('.data-table tbody tr') !== null;
    });
    console.log('Reported profiles table loaded:', reportedTableExists);

    // Test view details functionality
    console.log('Testing view report details...');
    await page.click('.action-btn.view-btn');
    await page.waitForSelector('.modal-overlay', { visible: true, timeout: 5000 });

    // Check if modal is displayed
    const reportModalVisible = await page.evaluate(() => {
      return document.querySelector('.modal-overlay').style.display !== 'none';
    });
    console.log('Report modal visible:', reportModalVisible);

    // Check if reporter information is displayed
    const reporterInfoExists = await page.evaluate(() => {
      return document.querySelector('.tab-section:nth-child(3) .section-title').textContent.includes('Reporter');
    });
    console.log('Reporter information displayed:', reporterInfoExists);

    // Close the modal
    await page.click('.modal-close-button');
    await page.waitForTimeout(1000);

    // Test resolve functionality
    console.log('Testing resolve report...');
    // Find a pending report
    const pendingReportExists = await page.evaluate(() => {
      const pendingRows = Array.from(document.querySelectorAll('.data-table tbody tr')).filter(row => {
        const statusBadges = row.querySelectorAll('.status-badge');
        for (const badge of statusBadges) {
          if (badge.textContent.trim() === 'Pending') return true;
        }
        return false;
      });
      if (pendingRows.length > 0) {
        const resolveBtn = pendingRows[0].querySelector('.action-btn.resolve-btn');
        if (resolveBtn) resolveBtn.click();
        return true;
      }
      return false;
    });

    if (pendingReportExists) {
      await page.waitForSelector('.confirmation-modal', { visible: true, timeout: 5000 });
      await page.type('#actionTaken', 'Test action taken for resolving this report');
      await page.click('.confirmation-modal .btn-success');
      console.log('Resolve report test completed');
    } else {
      console.log('No pending reports found to test resolve functionality');
    }

    // Test Premium Plans Page
    console.log('\nTesting Premium Plans page...');
    await page.goto('http://localhost:3000/admin/premium-plans');
    await page.waitForSelector('.plans-container', { timeout: 5000 });

    // Check if plans are loaded
    const plansExist = await page.evaluate(() => {
      return document.querySelector('.plan-card') !== null;
    });
    console.log('Premium plans loaded:', plansExist);

    // Check if feature comparison table is loaded
    const featureTableExists = await page.evaluate(() => {
      return document.querySelector('#featureComparisonTable tbody tr') !== null;
    });
    console.log('Feature comparison table loaded:', featureTableExists);

    // Test add plan functionality
    console.log('Testing add plan functionality...');
    await page.click('.content-header .btn-primary');
    await page.waitForSelector('.modal-overlay', { visible: true, timeout: 5000 });

    // Check if modal is displayed
    const planModalVisible = await page.evaluate(() => {
      return document.querySelector('.modal-overlay') !== null;
    });
    console.log('Add plan modal visible:', planModalVisible);

    // Fill in the form
    await page.type('#planName', 'Test Plan');
    await page.select('#planType', 'MONTHLY');
    await page.type('#planAmount', '999');
    await page.select('#planCurrency', 'INR');
    await page.type('#planDuration', '30');
    await page.type('#planDescription', 'This is a test plan created by the automated test script');

    // Check feature checkboxes
    await page.evaluate(() => {
      const checkboxes = document.querySelectorAll('.feature-checkbox input');
      if (checkboxes.length > 0) {
        checkboxes[0].checked = true;
        if (checkboxes.length > 1) {
          checkboxes[1].checked = true;
        }
      }
    });

    // Save the plan
    await page.click('.modal-footer .btn-primary');
    await page.waitForTimeout(2000);

    // Check if new plan was added
    const newPlanAdded = await page.evaluate(() => {
      const planCards = document.querySelectorAll('.plan-card');
      for (const card of planCards) {
        if (card.querySelector('.plan-title').textContent === 'Test Plan') {
          return true;
        }
      }
      return false;
    });
    console.log('New plan added:', newPlanAdded);

    // Test edit plan functionality
    console.log('Testing edit plan functionality...');
    await page.evaluate(() => {
      const planCards = document.querySelectorAll('.plan-card');
      for (const card of planCards) {
        if (card.querySelector('.plan-title').textContent === 'Test Plan') {
          card.querySelector('.btn-outline-primary').click();
          return;
        }
      }
    });

    await page.waitForSelector('.modal-overlay', { visible: true, timeout: 5000 });

    // Modify the plan
    await page.evaluate(() => {
      document.getElementById('planName').value = 'Updated Test Plan';
      document.getElementById('planAmount').value = '1299';
    });

    // Save the changes
    await page.click('.modal-footer .btn-primary');
    await page.waitForTimeout(2000);

    // Check if plan was updated
    const planUpdated = await page.evaluate(() => {
      const planCards = document.querySelectorAll('.plan-card');
      for (const card of planCards) {
        if (card.querySelector('.plan-title').textContent === 'Updated Test Plan') {
          return true;
        }
      }
      return false;
    });
    console.log('Plan updated:', planUpdated);

    // Test delete plan functionality
    console.log('Testing delete plan functionality...');
    await page.evaluate(() => {
      const planCards = document.querySelectorAll('.plan-card');
      for (const card of planCards) {
        if (card.querySelector('.plan-title').textContent === 'Updated Test Plan') {
          card.querySelector('.btn-outline-danger').click();
          return;
        }
      }
    });

    await page.waitForSelector('.modal-overlay', { visible: true, timeout: 5000 });

    // Confirm deletion
    await page.click('.modal-footer .btn-danger');
    await page.waitForTimeout(2000);

    // Check if plan was deleted
    const planDeleted = await page.evaluate(() => {
      const planCards = document.querySelectorAll('.plan-card');
      for (const card of planCards) {
        if (card.querySelector('.plan-title').textContent === 'Updated Test Plan') {
          return false;
        }
      }
      return true;
    });
    console.log('Plan deleted:', planDeleted);

    // Test UI responsiveness
    console.log('\nTesting UI responsiveness...');
    // Test mobile view
    await page.setViewport({ width: 375, height: 667 });
    await page.waitForTimeout(1000);

    // Check verification queue in mobile view
    await page.goto('http://localhost:3000/admin/verification-queue');
    await page.waitForSelector('.data-table', { timeout: 5000 });

    const verificationMobileLayout = await page.evaluate(() => {
      // Check if sidebar is collapsed in mobile view
      const sidebar = document.querySelector('.sidebar');
      const isSidebarCollapsed = sidebar.classList.contains('collapsed');

      // Check if table is responsive
      const tableContainer = document.querySelector('.table-container');
      const isTableResponsive = window.getComputedStyle(tableContainer).overflowX === 'auto';

      return { isSidebarCollapsed, isTableResponsive };
    });
    console.log('Verification queue mobile layout:', verificationMobileLayout);

    // Check reported profiles in mobile view
    await page.goto('http://localhost:3000/admin/reported-profiles');
    await page.waitForSelector('.data-table', { timeout: 5000 });

    const reportedMobileLayout = await page.evaluate(() => {
      // Check if sidebar is collapsed in mobile view
      const sidebar = document.querySelector('.sidebar');
      const isSidebarCollapsed = sidebar.classList.contains('collapsed');

      // Check if table is responsive
      const tableContainer = document.querySelector('.table-container');
      const isTableResponsive = window.getComputedStyle(tableContainer).overflowX === 'auto';

      return { isSidebarCollapsed, isTableResponsive };
    });
    console.log('Reported profiles mobile layout:', reportedMobileLayout);

    // Check premium plans in mobile view
    await page.goto('http://localhost:3000/admin/premium-plans');
    await page.waitForSelector('.plans-container', { timeout: 5000 });

    const plansMobileLayout = await page.evaluate(() => {
      // Check if sidebar is collapsed in mobile view
      const sidebar = document.querySelector('.sidebar');
      const isSidebarCollapsed = sidebar.classList.contains('collapsed');

      // Check if plans are stacked in mobile view
      const plansContainer = document.querySelector('.plans-container');
      const computedStyle = window.getComputedStyle(plansContainer);
      const gridTemplateColumns = computedStyle.getPropertyValue('grid-template-columns');

      return {
        isSidebarCollapsed,
        gridTemplateColumns,
        isResponsive: gridTemplateColumns.includes('1fr')
      };
    });
    console.log('Premium plans mobile layout:', plansMobileLayout);

    // Reset to desktop view
    await page.setViewport({ width: 1280, height: 800 });

    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testAdminPages();
