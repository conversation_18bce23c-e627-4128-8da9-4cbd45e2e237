/**
 * Family Details Edit Page
 * 
 * This page allows users to edit their family details.
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import FamilyDetailsForm from '@/website/components/profile/FamilyDetailsForm';
import { isUsingRealBackend } from '@/utils/featureFlags';
import { api } from '@/utils/axiosConfig';
import { useAuth } from '@/contexts/AuthContext';

// Mock user data for development
const MOCK_USER_DATA = {
  id: 'user-1',
  fullName: '<PERSON><PERSON>',
  gender: 'MALE',
  dateOfBirth: '1990-05-15',
  profileCompletionPercentage: 65,
  familyDetails: {
    familyType: 'NUCLEAR',
    familyStatus: 'MIDDLE_CLASS',
    fatherName: '<PERSON><PERSON>',
    fatherOccupation: 'Government Employee',
    motherName: '<PERSON><PERSON>',
    motherOccupation: 'Teacher',
    siblings: '1 younger sister',
    totalSiblings: '1',
    marriedSiblings: '0',
    motherTongue: 'MARATHI',
    marathiProficiency: 'NATIVE',
    kul: 'Sharma',
    maharashtrianOrigin: true,
    nativePlace: 'Pune',
    nativeDistrict: 'Pune'
  }
};

export default function FamilyDetailsEditPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        
        if (isUsingRealBackend()) {
          // Call real API
          const response = await api.get('/api/users/profile');
          setUserData(response.data);
        } else {
          // Use mock data
          setTimeout(() => {
            setUserData(MOCK_USER_DATA);
            setLoading(false);
          }, 500); // Simulate API delay
        }
      } catch (err) {
        console.error('Error fetching user data:', err);
        setError('Failed to load user data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Handle save
  const handleSave = async (formData) => {
    try {
      setSaving(true);
      
      if (isUsingRealBackend()) {
        // Call real API
        await api.put('/api/users/family-details', formData);
        setSuccess('Family details saved successfully!');
        
        // Update local user data
        setUserData(prev => ({
          ...prev,
          familyDetails: formData
        }));
      } else {
        // Simulate API call
        setTimeout(() => {
          setSuccess('Family details saved successfully!');
          
          // Update local user data
          setUserData(prev => ({
            ...prev,
            familyDetails: formData,
            profileCompletionPercentage: Math.min(85, (prev?.profileCompletionPercentage || 60) + 5)
          }));
          
          setSaving(false);
        }, 1000);
        return;
      }
    } catch (err) {
      console.error('Error saving family details:', err);
      setError('Failed to save family details. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <>
      <Head>
        <title>Edit Family Details | Vaivahik</title>
        <meta name="description" content="Edit your family details on Vaivahik matrimony" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : (
          <FamilyDetailsForm 
            userData={userData} 
            onSave={handleSave} 
            isLoading={saving} 
          />
        )}
      </Container>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </>
  );
}
