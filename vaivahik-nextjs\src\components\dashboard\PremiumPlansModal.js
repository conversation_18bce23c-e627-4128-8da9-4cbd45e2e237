import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  styled
} from '@mui/material';
import {
  Close as CloseIcon,
  WorkspacePremium as PremiumIcon,
  CheckCircle as CheckIcon,
  Star as StarIcon,
  Favorite as HeartIcon,
  Message as MessageIcon,
  Visibility as ViewIcon,
  Security as SecurityIcon
} from '@mui/icons-material';

const PremiumCard = styled(Card)(({ theme, isPopular }) => ({
  position: 'relative',
  height: '100%',
  border: isPopular ? '3px solid #FFD700' : '2px solid rgba(255, 95, 109, 0.2)',
  borderRadius: 20,
  overflow: 'hidden',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(255, 95, 109, 0.2)'
  },
  ...(isPopular && {
    '&::before': {
      content: '"Most Popular"',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
      color: '#000',
      textAlign: 'center',
      padding: '8px',
      fontWeight: 700,
      fontSize: '0.875rem',
      zIndex: 1
    }
  })
}));

const FeatureItem = styled(ListItem)(({ theme }) => ({
  padding: '8px 0',
  '& .MuiListItemIcon-root': {
    minWidth: 32,
    color: '#4CAF50'
  }
}));

const PremiumPlansModal = ({ open, onClose, currentPlan = null }) => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      fetchPlans();
    }
  }, [open]);

  const fetchPlans = async () => {
    setLoading(true);
    try {
      // Mock premium plans data
      const mockPlans = [
        {
          id: 'basic',
          name: 'Basic',
          price: 499,
          duration: '1 Month',
          originalPrice: 699,
          discount: 29,
          isPopular: false,
          features: [
            'View contact details (5 per day)',
            'Send messages (10 per day)',
            'Basic search filters',
            'Profile views tracking'
          ]
        },
        {
          id: 'premium',
          name: 'Premium',
          price: 1299,
          duration: '3 Months',
          originalPrice: 1899,
          discount: 32,
          isPopular: true,
          features: [
            'Unlimited contact details',
            'Unlimited messages',
            'Advanced search filters',
            'Priority in search results',
            'Profile highlighting',
            'Horoscope matching',
            'Chat with verified users only'
          ]
        },
        {
          id: 'elite',
          name: 'Elite',
          price: 4999,
          duration: '12 Months',
          originalPrice: 7999,
          discount: 38,
          isPopular: false,
          features: [
            'All Premium features',
            'Dedicated relationship manager',
            'Profile verification priority',
            'Exclusive elite member events',
            'AI-powered match recommendations',
            'Video call feature',
            'Profile boost (weekly)',
            'Success guarantee'
          ]
        }
      ];
      setPlans(mockPlans);
    } catch (error) {
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (plan) => {
    // Handle plan selection - integrate with payment gateway
    console.log('Selected plan:', plan);
    // You can integrate with Razorpay here
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 240, 245, 0.95) 100%)'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pb: 2,
        background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
        color: 'white'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PremiumIcon sx={{ mr: 2, fontSize: 32 }} />
          <Box>
            <Typography variant="h4" fontWeight="700">
              Upgrade to Premium
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              Find your perfect match faster with premium features
            </Typography>
          </Box>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 4 }}>
        {/* Benefits Banner */}
        <Box sx={{ 
          background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
          borderRadius: 2,
          p: 3,
          mb: 4,
          textAlign: 'center'
        }}>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D" gutterBottom>
            🚀 Join 50,000+ Premium Members
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Premium members get 5x more matches and find their life partner 3x faster
          </Typography>
        </Box>

        {/* Plans Grid */}
        <Grid container spacing={3}>
          {plans.map((plan) => (
            <Grid item xs={12} md={4} key={plan.id}>
              <PremiumCard isPopular={plan.isPopular}>
                <CardContent sx={{ p: 3, pt: plan.isPopular ? 5 : 3 }}>
                  <Box sx={{ textAlign: 'center', mb: 3 }}>
                    <Typography variant="h5" fontWeight="700" gutterBottom>
                      {plan.name}
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="h3" fontWeight="700" color="#FF5F6D">
                        ₹{plan.price}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {plan.duration}
                      </Typography>
                    </Box>

                    {plan.discount && (
                      <Box sx={{ mb: 2 }}>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            textDecoration: 'line-through',
                            color: 'text.secondary'
                          }}
                        >
                          ₹{plan.originalPrice}
                        </Typography>
                        <Chip
                          label={`Save ${plan.discount}%`}
                          color="success"
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    )}
                  </Box>

                  <List dense>
                    {plan.features.map((feature, index) => (
                      <FeatureItem key={index}>
                        <ListItemIcon>
                          <CheckIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary={feature}
                          primaryTypographyProps={{
                            fontSize: '0.875rem',
                            fontWeight: 500
                          }}
                        />
                      </FeatureItem>
                    ))}
                  </List>

                  <Button
                    variant={plan.isPopular ? "contained" : "outlined"}
                    fullWidth
                    size="large"
                    onClick={() => handlePlanSelect(plan)}
                    sx={{
                      mt: 3,
                      borderRadius: 3,
                      py: 1.5,
                      fontSize: '1rem',
                      fontWeight: 600,
                      ...(plan.isPopular ? {
                        background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #FF1493, #DC143C)',
                        }
                      } : {
                        borderColor: '#FF5F6D',
                        color: '#FF5F6D',
                        '&:hover': {
                          borderColor: '#FF1493',
                          backgroundColor: 'rgba(255, 95, 109, 0.1)'
                        }
                      })
                    }}
                  >
                    {currentPlan === plan.name ? 'Current Plan' : 'Choose Plan'}
                  </Button>
                </CardContent>
              </PremiumCard>
            </Grid>
          ))}
        </Grid>

        {/* Trust Indicators */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: 4, 
          mt: 4,
          flexWrap: 'wrap'
        }}>
          <Box sx={{ textAlign: 'center' }}>
            <SecurityIcon sx={{ fontSize: 32, color: '#4CAF50', mb: 1 }} />
            <Typography variant="body2" fontWeight="600">
              Secure Payments
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <StarIcon sx={{ fontSize: 32, color: '#FFD700', mb: 1 }} />
            <Typography variant="body2" fontWeight="600">
              4.8/5 Rating
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <HeartIcon sx={{ fontSize: 32, color: '#FF5F6D', mb: 1 }} />
            <Typography variant="body2" fontWeight="600">
              50K+ Success Stories
            </Typography>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default PremiumPlansModal;
