# Canonical API Endpoints

This document lists all the canonical API endpoints used in the application. These are the endpoints that should be implemented when transitioning to live data.

## Admin Endpoints

### Dashboard

- `/api/admin/dashboard`
- `/api/admin/dashboard/recent-activity`
- `/api/admin/dashboard/recent-users`

### User Management

- `/api/admin/users`
- `/api/admin/users/:id`

### Verification

- `/api/admin/verification-queue`
- `/api/admin/verification-queue/:id`

### Reports

- `/api/admin/reported-profiles`
- `/api/admin/reported-profiles/:id`

### Referral Programs

- `/api/admin/referral-programs`
- `/api/admin/referral-programs/:id`

### Biodata Templates

- `/api/admin/biodata-templates`
- `/api/admin/biodata-templates/:id`

### Settings

- `/api/admin/settings`

### Email Templates

- `/api/admin/email-templates`
- `/api/admin/email-templates/:id`

### Success Stories

- `/api/admin/success-stories`
- `/api/admin/success-stories/:id`

### Blog Posts

- `/api/admin/blog-posts`
- `/api/admin/blog-posts/:id`

### Admin Users

- `/api/admin/admin-users`
- `/api/admin/admin-users/:id`

## User Endpoints

### Authentication

- `/api/auth/login`
- `/api/auth/register`
- `/api/auth/verify-otp`
- `/api/auth/send-otp`
- `/api/auth/refresh-token`
- `/api/auth/logout`

### User Profile

- `/api/user/profile`
- `/api/user/profile/update`
- `/api/user/photos`
- `/api/user/photos/:id`
- `/api/user/preferences`
- `/api/user/preferences/update`

### Matches

- `/api/matches`
- `/api/matches/:id`
- `/api/matches/recommended`
- `/api/matches/viewed`
- `/api/matches/liked`

### Search

- `/api/search`
- `/api/search/filters`
- `/api/search/history`

### Communication

- `/api/messages`
- `/api/messages/:id`
- `/api/messages/conversations`
- `/api/messages/conversations/:id`

### Notifications

- `/api/notifications`
- `/api/notifications/:id`
- `/api/notifications/mark-read`

### Subscriptions

- `/api/subscriptions`
- `/api/subscriptions/:id`
- `/api/subscriptions/plans`

### Payments

- `/api/payments`
- `/api/payments/:id`
- `/api/payments/methods`
- `/api/payments/verify`

### Documents

- `/api/documents`
- `/api/documents/:id`
- `/api/documents/verify`

### Reports

- `/api/reports`
- `/api/reports/:id`

## System Endpoints

- `/api/health`
- `/api/status`
- `/api/version`
- `/api/feature-flags`

## Notes

1. **Path Parameters**: Endpoints with `:id` represent paths with dynamic parameters.

2. **Query Parameters**: Most endpoints support query parameters like `page`, `limit`, `search`, etc.

3. **Authentication**: All endpoints except for public ones (like `/api/auth/login`) require authentication.

4. **Response Format**: All endpoints return responses in the following format:
   ```json
   {
     "success": true|false,
     "message": "Success or error message",
     "data": { ... } // The actual data
   }
   ```

5. **Pagination**: Endpoints that return lists support pagination with the following response format:
   ```json
   {
     "success": true,
     "data": [ ... ], // Array of items
     "pagination": {
       "page": 1,
       "limit": 10,
       "totalItems": 100,
       "totalPages": 10
     }
   }
   ```

6. **Error Handling**: All endpoints return appropriate HTTP status codes and error messages.

7. **Rate Limiting**: All endpoints are subject to rate limiting to prevent abuse.

## Implementation Status

This document will be updated as endpoints are implemented in the live backend.
