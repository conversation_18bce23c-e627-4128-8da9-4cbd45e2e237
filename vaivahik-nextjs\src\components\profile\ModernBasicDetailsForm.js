/**
 * Modern Basic Details Form
 *
 * A modern UI form for collecting basic details as part of the profile completion process.
 * Uses the shared styled components for consistent UI across the application.
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  InputAdornment,
  Typography,
  FormControl
} from '@mui/material';
import {
  Person as PersonIcon,
  Wc as GenderIcon,
  Cake as BirthdayIcon,
  Height as HeightIcon,
  Favorite as HeartIcon
} from '@mui/icons-material';
import { validateField, VALIDATION_RULES } from '@/utils/validationUtils';
import { formatError, getUserFriendlyMessage, isValidationError } from '@/utils/errorHandling';
import {
  StyledPaper,
  StyledTextField,
  StyledSelect,
  StyledButton,
  StyledFormLabel,
  FloatingElement,
  FormSection,
  FormRow,
  StyledSectionTitle,
  StyledDatePicker
} from '@/components/ui/ModernFormComponents';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

// Constants for form options
const GENDER_OPTIONS = [
  { value: 'Male', label: 'Male' },
  { value: 'Female', label: 'Female' }
];

const MARITAL_STATUS_OPTIONS = [
  { value: 'Never Married', label: 'Never Married' },
  { value: 'Divorced', label: 'Divorced' },
  { value: 'Widowed', label: 'Widowed' },
  { value: 'Separated', label: 'Separated' }
];

const RELIGION_OPTIONS = [
  { value: 'Hindu', label: 'Hindu' },
  { value: 'Muslim', label: 'Muslim' },
  { value: 'Christian', label: 'Christian' },
  { value: 'Sikh', label: 'Sikh' },
  { value: 'Buddhist', label: 'Buddhist' },
  { value: 'Jain', label: 'Jain' },
  { value: 'Other', label: 'Other' }
];

const CASTE_OPTIONS = [
  { value: 'Maratha', label: 'Maratha' },
  { value: 'Brahmin', label: 'Brahmin' },
  { value: 'Kunbi', label: 'Kunbi' },
  { value: 'Dhangar', label: 'Dhangar' },
  { value: 'Koli', label: 'Koli' },
  { value: 'Other', label: 'Other' }
];

const BLOOD_GROUP_OPTIONS = [
  { value: 'A+', label: 'A+' },
  { value: 'A-', label: 'A-' },
  { value: 'B+', label: 'B+' },
  { value: 'B-', label: 'B-' },
  { value: 'AB+', label: 'AB+' },
  { value: 'AB-', label: 'AB-' },
  { value: 'O+', label: 'O+' },
  { value: 'O-', label: 'O-' }
];

const ModernBasicDetailsForm = ({ userData, onSave, isLoading = false }) => {
  const [formData, setFormData] = useState({
    fullName: '',
    gender: '',
    dateOfBirth: null,
    maritalStatus: 'Never Married',
    height: '',
    religion: 'Hindu',
    caste: 'Maratha',
    subCaste: '',
    bloodGroup: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Initialize form with user data if available
  useEffect(() => {
    if (userData?.basicDetails) {
      setFormData({
        ...formData,
        ...userData.basicDetails,
        dateOfBirth: userData.basicDetails.dateOfBirth ? new Date(userData.basicDetails.dateOfBirth) : null
      });
    }
  }, [userData]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    const newFormData = {
      ...formData,
      [name]: value
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      [name]: true
    });

    // Validate field on change if it's been touched
    if (touched[name]) {
      const fieldError = validateSingleField(name, value);
      setErrors(prevErrors => ({
        ...prevErrors,
        [name]: fieldError || ''
      }));
    }
  };

  // Handle date change
  const handleDateChange = (date) => {
    const newFormData = {
      ...formData,
      dateOfBirth: date
    };

    setFormData(newFormData);

    // Mark field as touched
    setTouched({
      ...touched,
      dateOfBirth: true
    });

    // Validate field on change if it's been touched
    if (touched.dateOfBirth) {
      const fieldError = validateSingleField('dateOfBirth', date);
      setErrors(prevErrors => ({
        ...prevErrors,
        dateOfBirth: fieldError || ''
      }));
    }
  };

  // Validate a single field
  const validateSingleField = (name, value) => {
    let rule;

    switch (name) {
      case 'fullName':
        rule = VALIDATION_RULES.FULL_NAME;
        break;
      case 'gender':
        rule = VALIDATION_RULES.GENDER;
        break;
      case 'dateOfBirth':
        rule = VALIDATION_RULES.DATE_OF_BIRTH;
        break;
      case 'height':
        rule = VALIDATION_RULES.HEIGHT;
        break;
      default:
        return null;
    }

    return validateField(name, value, rule, formData);
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Validate each field
    Object.keys(formData).forEach(fieldName => {
      const error = validateSingleField(fieldName, formData[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Format date of birth to ISO string for API
    const formattedData = {
      ...formData,
      dateOfBirth: formData.dateOfBirth ? formData.dateOfBirth.toISOString().split('T')[0] : null
    };

    // Call the onSave function with the form data
    onSave(formattedData);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <StyledPaper>
        {/* Decorative elements */}
        <FloatingElement position="top-right" />
        <FloatingElement position="bottom-left" />

        <Box sx={{ position: 'relative', zIndex: 1 }}>
          <StyledSectionTitle>Basic Details</StyledSectionTitle>

          {/* Form content */}
          <form onSubmit={handleSubmit}>
            <FormSection title="Personal Information">
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                      Full Name*
                    </Box>
                  </StyledFormLabel>
                  <StyledTextField
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Enter your full name"
                    error={!!errors.fullName}
                    helperText={errors.fullName}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <GenderIcon fontSize="small" sx={{ mr: 1 }} />
                      Gender*
                    </Box>
                  </StyledFormLabel>
                  <StyledSelect
                    name="gender"
                    value={formData.gender}
                    onChange={handleChange}
                    fullWidth
                    error={!!errors.gender}
                  >
                    {GENDER_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </StyledSelect>
                  {errors.gender && <FormHelperText error>{errors.gender}</FormHelperText>}
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <BirthdayIcon fontSize="small" sx={{ mr: 1 }} />
                      Date of Birth*
                    </Box>
                  </StyledFormLabel>
                  <StyledDatePicker
                    value={formData.dateOfBirth}
                    onChange={handleDateChange}
                    renderInput={(params) => (
                      <StyledTextField
                        {...params}
                        fullWidth
                        error={!!errors.dateOfBirth}
                        helperText={errors.dateOfBirth}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <HeartIcon fontSize="small" sx={{ mr: 1 }} />
                      Marital Status
                    </Box>
                  </StyledFormLabel>
                  <StyledSelect
                    name="maritalStatus"
                    value={formData.maritalStatus}
                    onChange={handleChange}
                    fullWidth
                  >
                    {MARITAL_STATUS_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </StyledSelect>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <HeightIcon fontSize="small" sx={{ mr: 1 }} />
                      Height (in feet)*
                    </Box>
                  </StyledFormLabel>
                  <StyledTextField
                    name="height"
                    value={formData.height}
                    onChange={handleChange}
                    fullWidth
                    placeholder="e.g., 5.8"
                    error={!!errors.height}
                    helperText={errors.height || "Enter height in feet (e.g., 5.8)"}
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection title="Religion & Caste">
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Religion</StyledFormLabel>
                  <StyledSelect
                    name="religion"
                    value={formData.religion}
                    onChange={handleChange}
                    fullWidth
                  >
                    {RELIGION_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </StyledSelect>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Caste</StyledFormLabel>
                  <StyledSelect
                    name="caste"
                    value={formData.caste}
                    onChange={handleChange}
                    fullWidth
                  >
                    {CASTE_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </StyledSelect>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Sub-caste</StyledFormLabel>
                  <StyledTextField
                    name="subCaste"
                    value={formData.subCaste}
                    onChange={handleChange}
                    fullWidth
                    placeholder="Enter your sub-caste"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <StyledFormLabel>Blood Group</StyledFormLabel>
                  <StyledSelect
                    name="bloodGroup"
                    value={formData.bloodGroup}
                    onChange={handleChange}
                    fullWidth
                  >
                    <MenuItem value="">Select Blood Group</MenuItem>
                    {BLOOD_GROUP_OPTIONS.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </StyledSelect>
                </Grid>
              </Grid>
            </FormSection>

            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
              <StyledButton
                type="submit"
                variant="contained"
                disabled={isLoading}
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {isLoading ? 'Saving...' : 'Save Basic Details'}
              </StyledButton>
            </Box>
          </form>
        </Box>
      </StyledPaper>
    </LocalizationProvider>
  );
};

export default ModernBasicDetailsForm;
