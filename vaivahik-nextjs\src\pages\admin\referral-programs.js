import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export default function ReferralPrograms() {
  const [programs, setPrograms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentProgram, setCurrentProgram] = useState(null);
  const [showProgramModal, setShowProgramModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmAction, setConfirmAction] = useState('');
  const [stats, setStats] = useState({
    totalReferrals: 0,
    activeReferrals: 0,
    conversionRate: 0,
    totalRewards: 0
  });

  useEffect(() => {
    fetchReferralPrograms();
    fetchReferralStats();
  }, []);

  const fetchReferralPrograms = async () => {
    setLoading(true);
    try {
      // Call the API endpoint
      const response = await fetch('/api/admin/referral-programs');

      if (!response.ok) {
        throw new Error(`Failed to fetch referral programs: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setPrograms(data.programs);
      } else {
        console.error('API returned error:', data.message);
        // Fallback to mock data if API fails
        const mockData = getMockPrograms();
        setPrograms(mockData);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching referral programs:', error);
      // Fallback to mock data if API fails
      console.log('Using mock data as fallback');
      const mockData = getMockPrograms();
      setPrograms(mockData);
      setLoading(false);
    }
  };

  const fetchReferralStats = async () => {
    try {
      // Call the API endpoint
      const response = await fetch('/api/admin/referral-stats');

      if (!response.ok) {
        throw new Error(`Failed to fetch referral stats: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setStats(data.stats);
      } else {
        console.error('API returned error:', data.message);
        // Fallback to mock data
        setStats({
          totalReferrals: 124,
          activeReferrals: 78,
          conversionRate: 32.5,
          totalRewards: 12500
        });
      }
    } catch (error) {
      console.error('Error fetching referral stats:', error);
      // Fallback to mock data
      setStats({
        totalReferrals: 124,
        activeReferrals: 78,
        conversionRate: 32.5,
        totalRewards: 12500
      });
    }
  };

  // Function to handle adding a new program
  const handleAddProgram = () => {
    setCurrentProgram(null);
    setShowProgramModal(true);
    document.body.classList.add('modal-open');
  };

  // Function to handle editing a program
  const handleEditProgram = (program) => {
    setCurrentProgram(program);
    setShowProgramModal(true);
    document.body.classList.add('modal-open');
  };

  // Function to handle toggling program status
  const handleToggleStatus = (program) => {
    setCurrentProgram(program);
    setConfirmAction('toggleStatus');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
  };

  // Function to handle deleting a program
  const handleDeleteProgram = (program) => {
    setCurrentProgram(program);
    setConfirmAction('delete');
    setShowConfirmModal(true);
    document.body.classList.add('modal-open');
  };

  // Function to close the program modal
  const closeProgramModal = () => {
    setShowProgramModal(false);
    setCurrentProgram(null);
    document.body.classList.remove('modal-open');
  };

  // Function to close the confirmation modal
  const closeConfirmModal = () => {
    setShowConfirmModal(false);
    document.body.classList.remove('modal-open');
  };

  // Function to save a program
  const handleSaveProgram = async () => {
    // Get form data
    const form = document.getElementById('programForm');
    if (!form) return;

    const formData = new FormData(form);
    const programData = {
      name: formData.get('name'),
      description: formData.get('description'),
      status: formData.get('status'),
      startDate: formData.get('startDate'),
      endDate: formData.get('endDate') || null,
      referrerRewardType: formData.get('referrerRewardType'),
      referrerRewardAmount: parseFloat(formData.get('referrerRewardAmount')),
      refereeRewardType: formData.get('refereeRewardType'),
      refereeRewardAmount: parseFloat(formData.get('refereeRewardAmount')),
      maxReferralsPerUser: formData.get('maxReferralsPerUser') ? parseInt(formData.get('maxReferralsPerUser')) : null,
      conversionRequirement: formData.get('conversionRequirement'),
      termsAndConditions: formData.get('termsAndConditions')
    };

    try {
      // Call the API endpoint
      const response = await fetch('/api/admin/referral-programs', {
        method: currentProgram ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(currentProgram ? { ...programData, id: currentProgram.id } : programData)
      });

      if (!response.ok) {
        throw new Error(`Failed to ${currentProgram ? 'update' : 'create'} program: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        if (currentProgram) {
          // Update existing program
          setPrograms(programs.map(p => p.id === currentProgram.id ? data.program || { ...programData, id: currentProgram.id } : p));
          toast.success('Program updated successfully!');
        } else {
          // Add new program
          setPrograms([...programs, data.program || { ...programData, id: Date.now().toString() }]);
          toast.success('Program created successfully!');
        }

        // Close modal
        closeProgramModal();
      } else {
        console.error('API returned error:', data.message);
        toast.error(data.message || `Failed to ${currentProgram ? 'update' : 'create'} program. Please try again.`);
      }
    } catch (error) {
      console.error(`Error ${currentProgram ? 'updating' : 'creating'} program:`, error);
      toast.error(`Error ${currentProgram ? 'updating' : 'creating'} program. Please try again.`);
    }
  };

  // Function to confirm an action (like deleting a program)
  const handleConfirmAction = async () => {
    if (confirmAction === 'delete' && currentProgram) {
      try {
        // Call the API endpoint
        const response = await fetch(`/api/admin/referral-programs/${currentProgram.id}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error(`Failed to delete program: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          // Update local state
          setPrograms(programs.filter(p => p.id !== currentProgram.id));
          setShowConfirmModal(false);
          document.body.classList.remove('modal-open');
          toast.success('Program deleted successfully');
        } else {
          console.error('API returned error:', data.message);
          toast.error(data.message || 'Failed to delete program. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting program:', error);
        toast.error('Failed to delete program. Please try again.');
      }
    } else if (confirmAction === 'toggleStatus' && currentProgram) {
      try {
        const newStatus = currentProgram.status === 'active' ? 'inactive' : 'active';

        // Call the API endpoint
        const response = await fetch(`/api/admin/referral-programs/${currentProgram.id}/status`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: newStatus })
        });

        if (!response.ok) {
          throw new Error(`Failed to update program status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          // Update local state
          setPrograms(programs.map(p => p.id === currentProgram.id ? { ...p, status: newStatus } : p));
          setShowConfirmModal(false);
          document.body.classList.remove('modal-open');
          toast.success(`Program ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
        } else {
          console.error('API returned error:', data.message);
          toast.error(data.message || 'Failed to update program status. Please try again.');
        }
      } catch (error) {
        console.error('Error updating program status:', error);
        toast.error('Failed to update program status. Please try again.');
      }
    }
  };

  // Function to format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  // Function to format reward
  const formatReward = (type, amount) => {
    if (type === 'cash') {
      return `₹${amount}`;
    } else if (type === 'subscription_days') {
      return `${amount} days of premium`;
    } else if (type === 'premium_features') {
      return `${amount} premium features`;
    }
    return `${amount} (${type})`;
  };

  // Function to format conversion requirement
  const formatRequirement = (requirement) => {
    if (requirement === 'none') {
      return 'Sign up only';
    } else if (requirement === 'profile_complete') {
      return 'Complete profile';
    } else if (requirement === 'subscription') {
      return 'Purchase subscription';
    }
    return requirement;
  };

  // Mock data function
  const getMockPrograms = () => {
    return [
      {
        id: 'program-1',
        name: 'Diwali Special',
        description: 'Special referral program for the Diwali season',
        status: 'active',
        startDate: '2023-10-01',
        endDate: '2023-11-15',
        referrerRewardType: 'cash',
        referrerRewardAmount: 500,
        refereeRewardType: 'subscription_days',
        refereeRewardAmount: 30,
        maxReferralsPerUser: 5,
        conversionRequirement: 'profile_complete',
        termsAndConditions: 'Terms and conditions apply. Rewards will be processed within 7 days.',
        totalReferrals: 45,
        conversionRate: 32,
        totalRewardsGiven: 12500
      },
      {
        id: 'program-2',
        name: 'Premium Membership Drive',
        description: 'Refer friends to get premium membership benefits',
        status: 'scheduled',
        startDate: '2023-12-01',
        endDate: '2024-01-31',
        referrerRewardType: 'subscription_days',
        referrerRewardAmount: 60,
        refereeRewardType: 'subscription_days',
        refereeRewardAmount: 30,
        maxReferralsPerUser: null,
        conversionRequirement: 'subscription',
        termsAndConditions: 'Premium membership will be added automatically upon successful referral.',
        totalReferrals: 0,
        conversionRate: 0,
        totalRewardsGiven: 0
      },
      {
        id: 'program-3',
        name: 'Basic Referral Program',
        description: 'Our standard referral program',
        status: 'inactive',
        startDate: '2023-01-01',
        endDate: null,
        referrerRewardType: 'cash',
        referrerRewardAmount: 200,
        refereeRewardType: 'cash',
        refereeRewardAmount: 100,
        maxReferralsPerUser: 10,
        conversionRequirement: 'none',
        termsAndConditions: 'Cash rewards will be processed at the end of each month.',
        totalReferrals: 120,
        conversionRate: 45,
        totalRewardsGiven: 35000
      }
    ];
  };

  return (
    <EnhancedAdminLayout title="Referral Programs">
      <ToastContainer position="top-right" autoClose={5000} />

      {/* Stats Overview */}
      <div className="stats-overview">
        <div className="stats-card">
          <div className="stats-icon">👥</div>
          <div className="stats-value">{stats.totalReferrals}</div>
          <div className="stats-label">Total Referrals</div>
        </div>
        <div className="stats-card">
          <div className="stats-icon">✅</div>
          <div className="stats-value">{stats.activeReferrals}</div>
          <div className="stats-label">Active Referrals</div>
        </div>
        <div className="stats-card">
          <div className="stats-icon">📈</div>
          <div className="stats-value">{stats.conversionRate}%</div>
          <div className="stats-label">Conversion Rate</div>
        </div>
        <div className="stats-card">
          <div className="stats-icon">💰</div>
          <div className="stats-value">₹{stats.totalRewards}</div>
          <div className="stats-label">Total Rewards</div>
        </div>
      </div>

      {/* Content Header */}
      <div className="content-header">
        <h2 className="page-title">Referral Programs</h2>
        <button className="btn btn-primary" onClick={handleAddProgram}>
          <span className="btn-icon">+</span> Create New Program
        </button>
      </div>

      {/* Programs List */}
      <div className="programs-container">
        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading referral programs...</p>
          </div>
        ) : programs.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📋</div>
            <h3>No Referral Programs Found</h3>
            <p>Create your first referral program to start growing your user base.</p>
            <button className="btn btn-primary" onClick={handleAddProgram}>
              Create New Program
            </button>
          </div>
        ) : (
          programs.map(program => (
            <div className={`program-card ${program.status}`} key={program.id}>
              <div className="program-header">
                <h3>{program.name}</h3>
                <span className={`status-badge ${program.status}`}>
                  {program.status === 'active' ? 'Active' :
                   program.status === 'scheduled' ? 'Scheduled' : 'Inactive'}
                </span>
              </div>

              <div className="program-description">
                {program.description || 'No description provided.'}
              </div>

              <div className="program-details">
                <div className="detail-row">
                  <span className="detail-label">Duration:</span>
                  <span className="detail-value">
                    {formatDate(program.startDate)} - {program.endDate ? formatDate(program.endDate) : 'Ongoing'}
                  </span>
                </div>

                <div className="detail-row">
                  <span className="detail-label">Referrer Reward:</span>
                  <span className="detail-value">
                    {formatReward(program.referrerRewardType, program.referrerRewardAmount)}
                  </span>
                </div>

                <div className="detail-row">
                  <span className="detail-label">Referee Reward:</span>
                  <span className="detail-value">
                    {formatReward(program.refereeRewardType, program.refereeRewardAmount)}
                  </span>
                </div>

                <div className="detail-row">
                  <span className="detail-label">Conversion:</span>
                  <span className="detail-value">
                    {formatRequirement(program.conversionRequirement)}
                  </span>
                </div>

                {program.maxReferralsPerUser && (
                  <div className="detail-row">
                    <span className="detail-label">Max Referrals:</span>
                    <span className="detail-value">
                      {program.maxReferralsPerUser} per user
                    </span>
                  </div>
                )}
              </div>

              <div className="program-stats">
                <div className="stat-item">
                  <span className="stat-value">{program.totalReferrals || 0}</span>
                  <span className="stat-label">Referrals</span>
                </div>

                <div className="stat-item">
                  <span className="stat-value">{program.conversionRate || 0}%</span>
                  <span className="stat-label">Conversion</span>
                </div>

                <div className="stat-item">
                  <span className="stat-value">₹{program.totalRewardsGiven || 0}</span>
                  <span className="stat-label">Rewards</span>
                </div>
              </div>

              <div className="program-actions">
                <button
                  className="btn btn-sm btn-outline-primary"
                  onClick={() => handleEditProgram(program)}
                >
                  Edit
                </button>
                <button
                  className="btn btn-sm btn-outline-secondary"
                  onClick={() => handleToggleStatus(program)}
                >
                  {program.status === 'active' ? 'Deactivate' : 'Activate'}
                </button>
                <button
                  className="btn btn-sm btn-outline-danger"
                  onClick={() => handleDeleteProgram(program)}
                >
                  Delete
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Program Modal */}
      {showProgramModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3 className="modal-title">
                {currentProgram ? 'Edit Referral Program' : 'Create New Referral Program'}
              </h3>
              <button className="modal-close-button" onClick={closeProgramModal}>&times;</button>
            </div>
            <div className="modal-body">
              <form id="programForm">
                {/* Basic Information */}
                <div className="form-section">
                  <h4 className="section-title">Basic Information</h4>

                  <div className="form-group">
                    <label htmlFor="programName">Program Name</label>
                    <input
                      type="text"
                      id="programName"
                      name="name"
                      required
                      placeholder="e.g., Diwali Special"
                      defaultValue={currentProgram?.name || ''}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="programDescription">Description</label>
                    <textarea
                      id="programDescription"
                      name="description"
                      rows="3"
                      placeholder="Describe the referral program"
                      defaultValue={currentProgram?.description || ''}
                    ></textarea>
                  </div>

                  <div className="form-group">
                    <label htmlFor="programStatus">Status</label>
                    <select
                      id="programStatus"
                      name="status"
                      required
                      defaultValue={currentProgram?.status || 'active'}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="scheduled">Scheduled</option>
                    </select>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="programStartDate">Start Date</label>
                      <input
                        type="date"
                        id="programStartDate"
                        name="startDate"
                        required
                        defaultValue={currentProgram?.startDate || new Date().toISOString().split('T')[0]}
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="programEndDate">End Date (Optional)</label>
                      <input
                        type="date"
                        id="programEndDate"
                        name="endDate"
                        defaultValue={currentProgram?.endDate || ''}
                      />
                      <div className="form-hint">Leave empty for ongoing programs</div>
                    </div>
                  </div>
                </div>

                {/* Reward Configuration */}
                <div className="form-section">
                  <h4 className="section-title">Reward Configuration</h4>

                  <div className="reward-config">
                    <h5>Referrer (Person who invites)</h5>
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="referrerRewardType">Reward Type</label>
                        <select
                          id="referrerRewardType"
                          name="referrerRewardType"
                          required
                          defaultValue={currentProgram?.referrerRewardType || 'cash'}
                        >
                          <option value="cash">Cash</option>
                          <option value="subscription_days">Subscription Days</option>
                          <option value="premium_features">Premium Features</option>
                        </select>
                      </div>

                      <div className="form-group">
                        <label htmlFor="referrerRewardAmount">Reward Amount</label>
                        <input
                          type="number"
                          id="referrerRewardAmount"
                          name="referrerRewardAmount"
                          required
                          placeholder="e.g., 500"
                          defaultValue={currentProgram?.referrerRewardAmount || ''}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="reward-config">
                    <h5>Referee (Person who joins)</h5>
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="refereeRewardType">Reward Type</label>
                        <select
                          id="refereeRewardType"
                          name="refereeRewardType"
                          required
                          defaultValue={currentProgram?.refereeRewardType || 'subscription_days'}
                        >
                          <option value="cash">Cash</option>
                          <option value="subscription_days">Subscription Days</option>
                          <option value="premium_features">Premium Features</option>
                        </select>
                      </div>

                      <div className="form-group">
                        <label htmlFor="refereeRewardAmount">Reward Amount</label>
                        <input
                          type="number"
                          id="refereeRewardAmount"
                          name="refereeRewardAmount"
                          required
                          placeholder="e.g., 30"
                          defaultValue={currentProgram?.refereeRewardAmount || ''}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Settings */}
                <div className="form-section">
                  <h4 className="section-title">Additional Settings</h4>

                  <div className="form-group">
                    <label htmlFor="conversionRequirement">Conversion Requirement</label>
                    <select
                      id="conversionRequirement"
                      name="conversionRequirement"
                      required
                      defaultValue={currentProgram?.conversionRequirement || 'none'}
                    >
                      <option value="none">None (Signup Only)</option>
                      <option value="profile_complete">Profile Completion</option>
                      <option value="subscription">Subscription Purchase</option>
                    </select>
                    <div className="form-hint">When is the referral considered successful?</div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="maxReferralsPerUser">Max Referrals Per User</label>
                    <input
                      type="number"
                      id="maxReferralsPerUser"
                      name="maxReferralsPerUser"
                      placeholder="Leave empty for unlimited"
                      defaultValue={currentProgram?.maxReferralsPerUser || ''}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="termsAndConditions">Terms & Conditions</label>
                    <textarea
                      id="termsAndConditions"
                      name="termsAndConditions"
                      rows="4"
                      placeholder="Enter terms and conditions for this referral program"
                      defaultValue={currentProgram?.termsAndConditions || ''}
                    ></textarea>
                  </div>
                </div>
              </form>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeProgramModal}>Cancel</button>
              <button className="btn btn-primary" onClick={handleSaveProgram}>
                {currentProgram ? 'Update Program' : 'Create Program'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="modal-overlay">
          <div className="modal confirmation-modal">
            <div className="modal-header">
              <h3 className="modal-title">
                {confirmAction === 'delete' ? 'Delete Referral Program' :
                 confirmAction === 'toggleStatus' && currentProgram?.status === 'active' ? 'Deactivate Program' : 'Activate Program'}
              </h3>
              <button className="modal-close-button" onClick={closeConfirmModal}>&times;</button>
            </div>
            <div className="modal-body">
              <div className="confirmation-message">
                <div className="confirmation-icon">
                  {confirmAction === 'delete' ? '🗑️' :
                   confirmAction === 'toggleStatus' && currentProgram?.status === 'active' ? '⏸️' : '▶️'}
                </div>
                <p>
                  {confirmAction === 'delete' && currentProgram ? (
                    <>Are you sure you want to delete the <strong>"{currentProgram.name}"</strong> program? This action cannot be undone.</>
                  ) : confirmAction === 'toggleStatus' && currentProgram?.status === 'active' ? (
                    <>Are you sure you want to deactivate the <strong>"{currentProgram.name}"</strong> program? Users will no longer be able to participate.</>
                  ) : (
                    <>Are you sure you want to activate the <strong>"{currentProgram.name}"</strong> program? It will be immediately available to users.</>
                  )}
                </p>
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={closeConfirmModal}>Cancel</button>
              <button
                className={`btn ${confirmAction === 'delete' ? 'btn-danger' : 'btn-primary'}`}
                onClick={handleConfirmAction}
              >
                {confirmAction === 'delete' ? 'Delete' :
                 confirmAction === 'toggleStatus' && currentProgram?.status === 'active' ? 'Deactivate' : 'Activate'}
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        /* Stats Overview */
        .stats-overview {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .stats-card {
          background-color: white;
          border-radius: 10px;
          padding: 20px;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          text-align: center;
          transition: transform 0.3s ease;
        }

        .stats-card:hover {
          transform: translateY(-5px);
        }

        .stats-icon {
          font-size: 2rem;
          margin-bottom: 10px;
        }

        .stats-value {
          font-size: 1.8rem;
          font-weight: 700;
          color: var(--primary);
          margin-bottom: 5px;
        }

        .stats-label {
          color: #666;
          font-size: 0.9rem;
        }

        /* Programs Container */
        .programs-container {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .program-card {
          background-color: white;
          border-radius: 10px;
          overflow: hidden;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease, box-shadow 0.3s ease;
          border-top: 5px solid var(--primary);
        }

        .program-card.active {
          border-top-color: var(--success);
        }

        .program-card.inactive {
          border-top-color: var(--danger);
        }

        .program-card.scheduled {
          border-top-color: var(--warning);
        }

        .program-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .program-header {
          padding: 15px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #eee;
        }

        .program-header h3 {
          margin: 0;
          font-size: 1.2rem;
          color: var(--text-dark);
        }

        .status-badge {
          padding: 5px 10px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 500;
        }

        .status-badge.active {
          background-color: var(--success-light);
          color: var(--success);
        }

        .status-badge.inactive {
          background-color: var(--danger-light);
          color: var(--danger);
        }

        .status-badge.scheduled {
          background-color: var(--warning-light);
          color: var(--warning);
        }

        .program-description {
          padding: 15px 20px;
          color: #666;
          font-size: 0.9rem;
          border-bottom: 1px solid #eee;
        }

        .program-details {
          padding: 15px 20px;
        }

        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 0.9rem;
        }

        .detail-label {
          color: #666;
          font-weight: 500;
        }

        .detail-value {
          color: var(--text-dark);
          font-weight: 600;
        }

        .program-stats {
          display: flex;
          justify-content: space-around;
          padding: 15px 20px;
          background-color: #f9f9f9;
          border-top: 1px solid #eee;
          border-bottom: 1px solid #eee;
        }

        .stat-item {
          text-align: center;
        }

        .stat-value {
          font-size: 1.2rem;
          font-weight: 700;
          color: var(--primary);
          display: block;
        }

        .stat-label {
          font-size: 0.8rem;
          color: #666;
        }

        .program-actions {
          padding: 15px 20px;
          display: flex;
          justify-content: space-between;
        }

        /* Empty State */
        .empty-state {
          grid-column: 1 / -1;
          text-align: center;
          padding: 50px 20px;
          background-color: white;
          border-radius: 10px;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .empty-icon {
          font-size: 3rem;
          margin-bottom: 20px;
          color: #ccc;
        }

        .empty-state h3 {
          margin-bottom: 10px;
          color: var(--text-dark);
        }

        .empty-state p {
          color: #666;
          margin-bottom: 20px;
        }

        /* Form Styles */
        .form-section {
          margin-bottom: 25px;
          padding-bottom: 20px;
          border-bottom: 1px solid #eee;
        }

        .form-section:last-child {
          border-bottom: none;
          margin-bottom: 0;
          padding-bottom: 0;
        }

        .section-title {
          font-size: 1.1rem;
          margin-bottom: 15px;
          color: var(--primary);
        }

        .reward-config {
          background-color: #f9f9ff;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 15px;
        }

        .reward-config h5 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 1rem;
          color: var(--text-dark);
        }

        .form-hint {
          font-size: 0.8rem;
          color: #666;
          margin-top: 5px;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
          .programs-container {
            grid-template-columns: 1fr;
          }

          .stats-overview {
            grid-template-columns: repeat(2, 1fr);
          }
        }
      `}</style>
    </EnhancedAdminLayout>
  );
}