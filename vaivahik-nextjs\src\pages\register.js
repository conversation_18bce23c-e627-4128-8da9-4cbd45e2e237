/**
 * Main Registration Page - Consolidated Version
 * Uses ModernRegistrationForm component for the best user experience
 */

import { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Alert,
  Snackbar,
  styled
} from '@mui/material';
import ModernRegistrationForm from '@/components/auth/ModernRegistrationForm';
import { isUsingRealBackend } from '@/utils/featureFlags';

// Styled components for modern UI
const PageContainer = styled(Container)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `
    linear-gradient(135deg,
      rgba(255, 182, 193, 0.1) 0%,
      rgba(255, 240, 245, 0.2) 25%,
      rgba(248, 249, 250, 0.3) 50%,
      rgba(255, 228, 225, 0.2) 75%,
      rgba(255, 182, 193, 0.1) 100%
    )
  `,
  padding: theme.spacing(3),
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: '-50%',
    left: '-50%',
    width: '200%',
    height: '200%',
    background: `
      radial-gradient(circle at 25% 25%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 228, 225, 0.1) 0%, transparent 50%)
    `,
    animation: 'float 20s ease-in-out infinite',
    zIndex: 0
  },
  '@keyframes float': {
    '0%, 100%': { transform: 'translate(0, 0) rotate(0deg)' },
    '33%': { transform: 'translate(30px, -30px) rotate(120deg)' },
    '66%': { transform: 'translate(-20px, 20px) rotate(240deg)' }
  }
}));

export default function Register() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Get data source for development
  const dataSource = isUsingRealBackend() ? 'real' : 'mock';

  // Handle registration submission
  const handleRegister = async (formData) => {
    setLoading(true);
    setError('');

    try {
      // Call registration API
      const response = await fetch('/api/users/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Registration failed');
      }

      // Show success message with source indicator
      const sourceText = data.source === 'mock' ? ' (using mock data)' : '';
      setSuccess(`Registration successful${sourceText}! Redirecting to login...`);

      // Redirect to login page after 2 seconds
      setTimeout(() => {
        router.push('/login?registered=true');
      }, 2000);
    } catch (err) {
      setError(err.message || 'An error occurred during registration');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Register - Vaivahik</title>
        <meta name="description" content="Create your Vaivahik matrimony profile" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <PageContainer maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'center', position: 'relative', zIndex: 1 }}>
          <ModernRegistrationForm
            onRegister={handleRegister}
            loading={loading}
            error={error}
            dataSource={dataSource}
          />
        </Box>
      </PageContainer>

      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />

      {error && (
        <Snackbar
          open={!!error}
          autoHideDuration={6000}
          onClose={() => setError('')}
        >
          <Alert severity="error" onClose={() => setError('')}>
            {error}
          </Alert>
        </Snackbar>
      )}
    </>
  );
}
