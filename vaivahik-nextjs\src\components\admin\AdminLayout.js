/**
 * @deprecated This component is deprecated. Use EnhancedAdminLayout instead.
 * This file is kept for reference only and should not be used in new code.
 * A backup of this file is available at AdminLayout.backup.js.
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { initDarkMode, toggleDarkMode as toggleDarkModeUtil, applyDarkMode, listenForSystemPreferenceChanges } from '@/utils/darkMode';
import MockDataToggle from './MockDataToggle';

const AdminLayout = ({ children, title = 'Admin Dashboard' }) => {
  console.warn('AdminLayout is deprecated. Use EnhancedAdminLayout instead.');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [adminName, setAdminName] = useState('Admin User');
  const [adminRole, setAdminRole] = useState('Super Admin');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if sidebar should be collapsed on load
    const savedSidebarState = localStorage.getItem('sidebarCollapsed');
    if (savedSidebarState === 'true') {
      setSidebarCollapsed(true);
    }

    // Initialize dark mode
    const isDarkMode = initDarkMode();
    setDarkMode(isDarkMode);
    applyDarkMode(isDarkMode);

    // Get admin info from localStorage
    const storedAdminName = localStorage.getItem('adminName');
    const storedAdminRole = localStorage.getItem('adminRole');

    if (storedAdminName) setAdminName(storedAdminName);
    if (storedAdminRole) setAdminRole(storedAdminRole);

    // Check authentication
    const token = localStorage.getItem('adminAccessToken');
    if (!token && typeof window !== 'undefined') {
      // Only redirect if we're not already on the login page
      if (!router.pathname.includes('/admin/login')) {
        console.log('No authentication token found, redirecting to login page');
        router.push('/admin/login');
      }
    }

    // Listen for system preference changes
    const cleanup = listenForSystemPreferenceChanges(setDarkMode);

    return () => {
      cleanup();
    };
  }, [router]);

  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);
    localStorage.setItem('sidebarCollapsed', newState.toString());
  };

  const toggleDarkMode = () => {
    const newState = toggleDarkModeUtil(darkMode);
    setDarkMode(newState);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  };

  const handleLogout = () => {
    localStorage.removeItem('adminAccessToken');
    localStorage.removeItem('adminRole');
    localStorage.removeItem('adminName');
    router.push('/admin/login');
  };

  return (
    <>
      <Head>
        <title>{title} - Vaivahik Admin</title>
        <meta name="description" content="Vaivahik Admin Panel" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="stylesheet" href="/styles/admin-ui-components.css" />
      </Head>

      <div className={`admin-container ${darkMode ? 'dark-mode' : ''}`}>
        {/* Mobile Sidebar Overlay */}
        <div className={`sidebar-overlay ${mobileMenuOpen ? 'active' : ''}`} onClick={closeMobileMenu}></div>

        {/* Sidebar */}
        <aside className={`sidebar ${sidebarCollapsed ? 'collapsed' : ''} ${mobileMenuOpen ? 'mobile-open' : ''}`}>
          <div className="sidebar-header">
            <Link href="/admin/dashboard" className="sidebar-logo">
              <div className="logo-icon">V</div>
              <span className="logo-text">Vaivahik Admin</span>
            </Link>
            <button className="sidebar-toggle" onClick={toggleSidebar} aria-label="Toggle Sidebar">≡</button>
            <button className="mobile-sidebar-close" onClick={closeMobileMenu} aria-label="Close Mobile Menu">✕</button>
          </div>
          <div className="sidebar-user">
            <div className="user-avatar">{adminName.charAt(0).toUpperCase()}</div>
            <div className="user-info">
              <div className="user-name">{adminName}</div>
              <div className="user-role">{adminRole}</div>
            </div>
          </div>
          <nav className="sidebar-nav">
            <div className="nav-category">Main</div>
            <Link href="/admin/dashboard" className={`nav-item ${router.pathname === '/admin/dashboard' ? 'active' : ''}`}>
              <div className="nav-icon">📊</div>
              <span className="nav-text">Dashboard</span>
            </Link>

            <div className="nav-category">User Management</div>
            <Link href="/admin/users" className={`nav-item ${router.pathname === '/admin/users' ? 'active' : ''}`}>
              <div className="nav-icon">👥</div>
              <span className="nav-text">All Users</span>
            </Link>
            <Link href="/admin/verification-queue" className={`nav-item ${router.pathname === '/admin/verification-queue' ? 'active' : ''}`}>
              <div className="nav-icon">✓</div>
              <span className="nav-text">Verification Queue</span>
            </Link>
            <Link href="/admin/reported-profiles" className={`nav-item ${router.pathname === '/admin/reported-profiles' ? 'active' : ''}`}>
              <div className="nav-icon">🚫</div>
              <span className="nav-text">Reported Profiles</span>
            </Link>

            <div className="nav-category">Premium Features</div>
            <Link href="/admin/premium-plans" className={`nav-item ${router.pathname === '/admin/premium-plans' ? 'active' : ''}`}>
              <div className="nav-icon">💎</div>
              <span className="nav-text">Premium Plans</span>
            </Link>
            <Link href="/admin/feature-management" className={`nav-item ${router.pathname === '/admin/feature-management' ? 'active' : ''}`}>
              <div className="nav-icon">🔑</div>
              <span className="nav-text">Feature Management</span>
            </Link>
            <Link href="/admin/promotions" className={`nav-item ${router.pathname === '/admin/promotions' ? 'active' : ''}`}>
              <div className="nav-icon">🎁</div>
              <span className="nav-text">Promotions</span>
            </Link>
            <Link href="/admin/spotlight-features" className={`nav-item ${router.pathname === '/admin/spotlight-features' ? 'active' : ''}`}>
              <div className="nav-icon">✨</div>
              <span className="nav-text">Spotlight</span>
            </Link>

            <div className="nav-category">AI & Matching</div>
            <Link href="/admin/algorithm-settings" className={`nav-item ${router.pathname === '/admin/algorithm-settings' ? 'active' : ''}`}>
              <div className="nav-icon">🧠</div>
              <span className="nav-text">Algorithm Settings</span>
            </Link>
            <Link href="/admin/preference-config" className={`nav-item ${router.pathname === '/admin/preference-config' ? 'active' : ''}`}>
              <div className="nav-icon">⚙️</div>
              <span className="nav-text">Preference Config</span>
            </Link>
            <Link href="/admin/success-analytics" className={`nav-item ${router.pathname === '/admin/success-analytics' ? 'active' : ''}`}>
              <div className="nav-icon">📈</div>
              <span className="nav-text">Success Analytics</span>
            </Link>

            <div className="nav-category">Content</div>
            <Link href="/admin/photo-moderation" className={`nav-item ${router.pathname === '/admin/photo-moderation' ? 'active' : ''}`}>
              <div className="nav-icon">🖼️</div>
              <span className="nav-text">Photo Moderation</span>
            </Link>
            <Link href="/admin/text-moderation" className={`nav-item ${router.pathname === '/admin/text-moderation' ? 'active' : ''}`}>
              <div className="nav-icon">💬</div>
              <span className="nav-text">Text Moderation</span>
            </Link>
            <Link href="/admin/success-stories" className={`nav-item ${router.pathname === '/admin/success-stories' ? 'active' : ''}`}>
              <div className="nav-icon">💑</div>
              <span className="nav-text">Success Stories</span>
            </Link>
            <Link href="/admin/blog-posts" className={`nav-item ${router.pathname === '/admin/blog-posts' ? 'active' : ''}`}>
              <div className="nav-icon">📝</div>
              <span className="nav-text">Blog Posts</span>
            </Link>
            <Link href="/admin/biodata-templates" className={`nav-item ${router.pathname === '/admin/biodata-templates' ? 'active' : ''}`}>
              <div className="nav-icon">📄</div>
              <span className="nav-text">Biodata Templates</span>
            </Link>

            <div className="nav-category">Financial</div>
            <Link href="/admin/subscriptions" className={`nav-item ${router.pathname === '/admin/subscriptions' ? 'active' : ''}`}>
              <div className="nav-icon">💰</div>
              <span className="nav-text">Subscriptions</span>
            </Link>
            <Link href="/admin/transactions" className={`nav-item ${router.pathname === '/admin/transactions' ? 'active' : ''}`}>
              <div className="nav-icon">💳</div>
              <span className="nav-text">Transactions</span>
            </Link>
            <Link href="/admin/revenue-reports" className={`nav-item ${router.pathname === '/admin/revenue-reports' ? 'active' : ''}`}>
              <div className="nav-icon">📊</div>
              <span className="nav-text">Revenue Reports</span>
            </Link>
            <Link href="/admin/referral-programs" className={`nav-item ${router.pathname === '/admin/referral-programs' ? 'active' : ''}`}>
              <div className="nav-icon">🔄</div>
              <span className="nav-text">Refer & Earn</span>
            </Link>

            <div className="nav-category">Communication</div>
            <Link href="/admin/notifications" className={`nav-item ${router.pathname === '/admin/notifications' ? 'active' : ''}`}>
              <div className="nav-icon">🔔</div>
              <span className="nav-text">Notifications</span>
            </Link>
            <Link href="/admin/email-templates" className={`nav-item ${router.pathname === '/admin/email-templates' ? 'active' : ''}`}>
              <div className="nav-icon">✉️</div>
              <span className="nav-text">Email Templates</span>
            </Link>

            <div className="nav-category">System</div>
            <Link href="/admin/settings" className={`nav-item ${router.pathname === '/admin/settings' ? 'active' : ''}`}>
              <div className="nav-icon">⚙️</div>
              <span className="nav-text">Settings</span>
            </Link>
            <Link href="/admin/admin-users" className={`nav-item ${router.pathname === '/admin/admin-users' ? 'active' : ''}`}>
              <div className="nav-icon">👤</div>
              <span className="nav-text">Admin Users</span>
            </Link>
            <Link href="/api-viewer-enhanced" className={`nav-item ${router.pathname === '/api-viewer-enhanced' ? 'active' : ''}`}>
              <div className="nav-icon">📚</div>
              <span className="nav-text">API Documentation</span>
            </Link>
            <Link href="/admin/api-discovery" className={`nav-item ${router.pathname === '/admin/api-discovery' ? 'active' : ''}`}>
              <div className="nav-icon">🔍</div>
              <span className="nav-text">API Discovery</span>
            </Link>
            <button className="nav-item logout-btn" onClick={handleLogout}>
              <div className="nav-icon">🚪</div>
              <span className="nav-text">Logout</span>
            </button>
          </nav>
        </aside>

        {/* Main Content */}
        <div className={`main-content ${sidebarCollapsed ? 'collapsed' : ''}`}>
          {/* Top Bar */}
          <div className="topbar">
            <div className="breadcrumb">
              <button className="mobile-menu-toggle" onClick={toggleMobileMenu} aria-label="Toggle Mobile Menu">☰</button>
              <Link href="/admin/dashboard">Dashboard</Link> / <span>{title}</span>
            </div>
            <div className="topbar-actions">
              <button className="dark-mode-toggle" onClick={toggleDarkMode} aria-label="Toggle Dark Mode">
                <span className="light-icon">☀️</span>
                <span className="dark-icon">🌙</span>
              </button>
              <div className="notifications">
                <button className="notification-btn" aria-label="Notifications">
                  🔔
                  <span className="notification-badge">3</span>
                </button>
              </div>
            </div>
          </div>

          {/* Page Content */}
          <div className="page-content">
            {children}
          </div>

          {/* Mock Data Toggle - only shown in development */}
          {process.env.NODE_ENV === 'development' && <MockDataToggle />}
        </div>
      </div>
    </>
  );
};

export default AdminLayout;
