/**
 * Family Details Form
 * 
 * A comprehensive form for collecting family information including:
 * - Family type and status
 * - Parents' information
 * - Siblings details
 * - Cultural background
 */

import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  FormLabel,
  FormHelperText,
  Select,
  MenuItem,
  RadioGroup,
  Radio,
  FormControlLabel,
  Button,
  Divider,
  LinearProgress,
  Alert,
  Chip,
  Autocomplete,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  InputAdornment
} from '@mui/material';
import {
  People as FamilyIcon,
  Person as PersonIcon,
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Check as CheckIcon,
  Language as LanguageIcon,
  Home as HomeIcon,
  LocationCity as CityIcon
} from '@mui/icons-material';

// Maharashtra districts for native place selection
const MAHARASHTRA_DISTRICTS = [
  'Ahmednagar', 'Akola', 'Amravati', 'Aurangabad', 'Beed', 'B<PERSON>ara', 
  '<PERSON><PERSON><PERSON>', 'Chandrapur', 'Dhule', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Gondia', 'Hingoli', 
  'Jalgaon', 'Jalna', 'Kolhapur', 'Latur', 'Mumbai City', 'Mumbai Suburban', 
  'Nagpur', 'Nanded', 'Nandurbar', 'Nashik', 'Osmanabad', 'Palghar', 
  'Parbhani', 'Pune', 'Raigad', 'Ratnagiri', 'Sangli', 'Satara', 
  'Sindhudurg', 'Solapur', 'Thane', 'Wardha', 'Washim', 'Yavatmal'
];

const FamilyDetailsForm = ({ userData, onSave, isLoading }) => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    // Family Type
    familyType: '',
    familyStatus: '',
    
    // Parents
    fatherName: '',
    fatherOccupation: '',
    motherName: '',
    motherOccupation: '',
    
    // Extended Family
    siblings: '',
    totalSiblings: '',
    marriedSiblings: '',
    uncleName: '',
    familyContact: '',
    
    // Cultural Background
    motherTongue: '',
    marathiProficiency: '',
    kul: '',
    maharashtrianOrigin: false,
    nativePlace: '',
    nativeDistrict: ''
  });
  
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [success, setSuccess] = useState('');
  
  // Family Type Options
  const FAMILY_TYPE_OPTIONS = ['JOINT', 'NUCLEAR', 'EXTENDED'];
  const FAMILY_STATUS_OPTIONS = ['MIDDLE_CLASS', 'UPPER_MIDDLE_CLASS', 'AFFLUENT'];
  const MOTHER_TONGUE_OPTIONS = ['MARATHI', 'HINDI', 'ENGLISH', 'GUJARATI', 'KANNADA', 'TAMIL', 'TELUGU', 'OTHER'];
  const PROFICIENCY_OPTIONS = ['NATIVE', 'FLUENT', 'INTERMEDIATE', 'BASIC', 'NONE'];
  
  // Initialize form with user data if available
  useEffect(() => {
    if (userData && userData.familyDetails) {
      setFormData({
        ...formData,
        ...userData.familyDetails
      });
    }
  }, [userData]);
  
  // Calculate completion percentage
  useEffect(() => {
    const requiredFields = ['familyType', 'fatherName', 'motherName'];
    const optionalFields = [
      'familyStatus', 'fatherOccupation', 'motherOccupation', 
      'siblings', 'totalSiblings', 'marriedSiblings', 'uncleName', 
      'familyContact', 'motherTongue', 'marathiProficiency', 'kul', 
      'maharashtrianOrigin', 'nativePlace', 'nativeDistrict'
    ];
    
    const totalFields = requiredFields.length + optionalFields.length;
    let completedFields = 0;
    
    // Count required fields
    requiredFields.forEach(field => {
      if (formData[field]) completedFields++;
    });
    
    // Count optional fields
    optionalFields.forEach(field => {
      if (formData[field] !== undefined && formData[field] !== '') completedFields++;
    });
    
    // Calculate percentage
    const percentage = Math.round((completedFields / totalFields) * 100);
    setCompletionPercentage(percentage);
  }, [formData]);
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
    
    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };
  
  // Handle boolean change (for radio buttons)
  const handleBooleanChange = (e) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value === 'true'
    }));
    
    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
  };
  
  // Handle autocomplete change
  const handleAutocompleteChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    // Required fields
    if (!formData.familyType) {
      newErrors.familyType = 'Family type is required';
    }
    
    if (!formData.fatherName) {
      newErrors.fatherName = 'Father\'s name is required';
    }
    
    if (!formData.motherName) {
      newErrors.motherName = 'Mother\'s name is required';
    }
    
    // Validate number fields
    if (formData.totalSiblings && isNaN(Number(formData.totalSiblings))) {
      newErrors.totalSiblings = 'Please enter a valid number';
    }
    
    if (formData.marriedSiblings && isNaN(Number(formData.marriedSiblings))) {
      newErrors.marriedSiblings = 'Please enter a valid number';
    }
    
    // Validate that married siblings are not more than total siblings
    if (formData.totalSiblings && formData.marriedSiblings && 
        Number(formData.marriedSiblings) > Number(formData.totalSiblings)) {
      newErrors.marriedSiblings = 'Married siblings cannot be more than total siblings';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // Call the onSave function with the form data
    onSave(formData);
    setSuccess('Family details saved successfully!');
  };
  
  // Handle back button
  const handleBack = () => {
    router.push('/profile');
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Paper elevation={2} sx={{ p: 3, borderRadius: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <FamilyIcon color="primary" sx={{ fontSize: 28, mr: 1.5 }} />
          <Typography variant="h5" component="h1" fontWeight="medium">
            Family Details
          </Typography>
          
          <Box sx={{ ml: 'auto', display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
              Completion:
            </Typography>
            <Box sx={{ position: 'relative', display: 'inline-flex', alignItems: 'center' }}>
              <LinearProgress 
                variant="determinate" 
                value={completionPercentage} 
                sx={{ width: 100, height: 8, borderRadius: 4 }} 
              />
              <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                {completionPercentage}%
              </Typography>
            </Box>
          </Box>
        </Box>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          Family details help us find better matches for you. Fields marked with * are required.
        </Typography>
        
        {success && (
          <Alert 
            severity="success" 
            sx={{ mb: 3 }}
            action={
              <IconButton
                aria-label="close"
                color="inherit"
                size="small"
                onClick={() => setSuccess('')}
              >
                <CheckIcon fontSize="inherit" />
              </IconButton>
            }
          >
            {success}
          </Alert>
        )}
        
        <Divider sx={{ mb: 4 }} />
        
        <Grid container spacing={4}>
          {/* Family Type Section */}
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader 
                title="Family Type" 
                titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                avatar={<HomeIcon color="primary" />}
              />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={!!errors.familyType}>
                      <FormLabel id="familyType-label">Family Type*</FormLabel>
                      <Select
                        labelId="familyType-label"
                        id="familyType"
                        name="familyType"
                        value={formData.familyType}
                        onChange={handleInputChange}
                        size="small"
                      >
                        <MenuItem value="">Select Family Type</MenuItem>
                        <MenuItem value="JOINT">Joint Family</MenuItem>
                        <MenuItem value="NUCLEAR">Nuclear Family</MenuItem>
                        <MenuItem value="EXTENDED">Extended Family</MenuItem>
                      </Select>
                      {errors.familyType && <FormHelperText error>{errors.familyType}</FormHelperText>}
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel id="familyStatus-label">Family Status</FormLabel>
                      <Select
                        labelId="familyStatus-label"
                        id="familyStatus"
                        name="familyStatus"
                        value={formData.familyStatus}
                        onChange={handleInputChange}
                        size="small"
                      >
                        <MenuItem value="">Select Family Status</MenuItem>
                        <MenuItem value="MIDDLE_CLASS">Middle Class</MenuItem>
                        <MenuItem value="UPPER_MIDDLE_CLASS">Upper Middle Class</MenuItem>
                        <MenuItem value="AFFLUENT">Affluent</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Parents Section */}
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader 
                title="Parents Information" 
                titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                avatar={<PersonIcon color="primary" />}
              />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={!!errors.fatherName}>
                      <FormLabel htmlFor="fatherName">Father's Name*</FormLabel>
                      <TextField
                        id="fatherName"
                        name="fatherName"
                        value={formData.fatherName}
                        onChange={handleInputChange}
                        placeholder="Enter father's name"
                        error={!!errors.fatherName}
                        helperText={errors.fatherName}
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="fatherOccupation">Father's Occupation</FormLabel>
                      <TextField
                        id="fatherOccupation"
                        name="fatherOccupation"
                        value={formData.fatherOccupation}
                        onChange={handleInputChange}
                        placeholder="Enter father's occupation"
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={!!errors.motherName}>
                      <FormLabel htmlFor="motherName">Mother's Name*</FormLabel>
                      <TextField
                        id="motherName"
                        name="motherName"
                        value={formData.motherName}
                        onChange={handleInputChange}
                        placeholder="Enter mother's name"
                        error={!!errors.motherName}
                        helperText={errors.motherName}
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="motherOccupation">Mother's Occupation</FormLabel>
                      <TextField
                        id="motherOccupation"
                        name="motherOccupation"
                        value={formData.motherOccupation}
                        onChange={handleInputChange}
                        placeholder="Enter mother's occupation"
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Siblings Section */}
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader 
                title="Siblings & Extended Family" 
                titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                avatar={<FamilyIcon color="primary" />}
              />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={!!errors.totalSiblings}>
                      <FormLabel htmlFor="totalSiblings">Total Siblings</FormLabel>
                      <TextField
                        id="totalSiblings"
                        name="totalSiblings"
                        value={formData.totalSiblings}
                        onChange={handleInputChange}
                        placeholder="Enter number of siblings"
                        type="number"
                        InputProps={{ inputProps: { min: 0 } }}
                        error={!!errors.totalSiblings}
                        helperText={errors.totalSiblings}
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth error={!!errors.marriedSiblings}>
                      <FormLabel htmlFor="marriedSiblings">Married Siblings</FormLabel>
                      <TextField
                        id="marriedSiblings"
                        name="marriedSiblings"
                        value={formData.marriedSiblings}
                        onChange={handleInputChange}
                        placeholder="Enter number of married siblings"
                        type="number"
                        InputProps={{ inputProps: { min: 0 } }}
                        error={!!errors.marriedSiblings}
                        helperText={errors.marriedSiblings}
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="siblings">Siblings Description</FormLabel>
                      <TextField
                        id="siblings"
                        name="siblings"
                        value={formData.siblings}
                        onChange={handleInputChange}
                        placeholder="Describe your siblings (e.g., 1 elder brother, 1 younger sister)"
                        size="small"
                        multiline
                        rows={2}
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="uncleName">Uncle's Name (Optional)</FormLabel>
                      <TextField
                        id="uncleName"
                        name="uncleName"
                        value={formData.uncleName}
                        onChange={handleInputChange}
                        placeholder="Enter uncle's name if applicable"
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="familyContact">Family Contact Number</FormLabel>
                      <TextField
                        id="familyContact"
                        name="familyContact"
                        value={formData.familyContact}
                        onChange={handleInputChange}
                        placeholder="Enter family contact number"
                        size="small"
                        InputProps={{
                          startAdornment: <InputAdornment position="start">+91</InputAdornment>,
                        }}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Cultural Background Section */}
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardHeader 
                title="Cultural Background" 
                titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                avatar={<LanguageIcon color="primary" />}
              />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel id="motherTongue-label">Mother Tongue</FormLabel>
                      <Select
                        labelId="motherTongue-label"
                        id="motherTongue"
                        name="motherTongue"
                        value={formData.motherTongue}
                        onChange={handleInputChange}
                        size="small"
                      >
                        <MenuItem value="">Select Mother Tongue</MenuItem>
                        {MOTHER_TONGUE_OPTIONS.map(option => (
                          <MenuItem key={option} value={option}>
                            {option.charAt(0) + option.slice(1).toLowerCase()}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel id="marathiProficiency-label">Marathi Proficiency</FormLabel>
                      <Select
                        labelId="marathiProficiency-label"
                        id="marathiProficiency"
                        name="marathiProficiency"
                        value={formData.marathiProficiency}
                        onChange={handleInputChange}
                        size="small"
                      >
                        <MenuItem value="">Select Proficiency</MenuItem>
                        {PROFICIENCY_OPTIONS.map(option => (
                          <MenuItem key={option} value={option}>
                            {option.charAt(0) + option.slice(1).toLowerCase()}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="kul">Kul (Family/Clan Name)</FormLabel>
                      <TextField
                        id="kul"
                        name="kul"
                        value={formData.kul}
                        onChange={handleInputChange}
                        placeholder="Enter your kul/clan name"
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel id="maharashtrianOrigin-label">Maharashtrian Origin</FormLabel>
                      <RadioGroup
                        aria-labelledby="maharashtrianOrigin-label"
                        name="maharashtrianOrigin"
                        value={formData.maharashtrianOrigin.toString()}
                        onChange={handleBooleanChange}
                        row
                      >
                        <FormControlLabel value="true" control={<Radio />} label="Yes" />
                        <FormControlLabel value="false" control={<Radio />} label="No" />
                      </RadioGroup>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="nativePlace">Native Place</FormLabel>
                      <TextField
                        id="nativePlace"
                        name="nativePlace"
                        value={formData.nativePlace}
                        onChange={handleInputChange}
                        placeholder="Enter your native place/village"
                        size="small"
                      />
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <FormLabel htmlFor="nativeDistrict">Native District</FormLabel>
                      <Autocomplete
                        id="nativeDistrict"
                        options={MAHARASHTRA_DISTRICTS}
                        value={formData.nativeDistrict}
                        onChange={(event, newValue) => {
                          handleAutocompleteChange('nativeDistrict', newValue);
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            placeholder="Select district"
                            size="small"
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Back to Profile
          </Button>
          
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Family Details'}
          </Button>
        </Box>
      </Paper>
    </form>
  );
};

export default FamilyDetailsForm;
