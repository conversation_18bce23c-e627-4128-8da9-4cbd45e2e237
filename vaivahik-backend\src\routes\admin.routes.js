// src/routes/admin.routes.js

const express = require('express');
const router = express.Router();
const { body } = require('express-validator');

// Import controllers and middleware
const adminController = require('../controllers/admin.controller.js');
const adminVerificationController = require('../controllers/admin.verification.controller.js');
const adminMockController = require('../controllers/admin.mock.controller.js');
const dashboardController = require('../controllers/admin/dashboard.controller.new.js');
const authenticateAdmin = require('../middleware/adminAuth.middleware.js');
const mockAuthenticateAdmin = require('../middleware/mockAuth.middleware.js');
const { authenticateToken, isAdmin } = require('../middleware/auth.middleware.js');
// Assuming validate function handles validation errors and passes control if valid
const { validate } = require('../validators/user.validator.js'); // Adjust path if needed

// Define allowed statuses for validation
const ALLOWED_USER_STATUSES = ['ACTIVE', 'SUSPENDED', 'INACTIVE', 'PENDING_APPROVAL', 'INCOMPLETE'];
const ALLOWED_PHOTO_STATUSES = ['APPROVED', 'REJECTED'];

// --- Admin Authentication Routes ---
router.post('/login', adminController.login);
// TODO: Add refresh token route? router.post('/refresh-token', adminController.refreshToken);
// TODO: Add logout route? router.post('/logout', authenticateAdmin, adminController.logout);


// --- Dashboard Routes (Protected) ---
// Import dashboard routes
const dashboardRoutes = require('./admin/dashboard.routes.new');
// Use the dashboard routes
router.use('/dashboard', dashboardRoutes);


// --- Admin User Management Routes (Protected) ---

// Import user management routes
const userManagementRoutes = require('./admin/userManagement.routes');
// Use the user management routes
router.use('/user-management', userManagementRoutes);

// GET /api/admin/users - Fetch all registered users
router.get('/users', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getAllUsers(req, res, next);
    });
});

// GET /api/admin/users/:id - Fetch details for a specific user
router.get('/users/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getUserDetails(req, res, next);
    });
});

// PATCH /api/admin/users/:id - Update user details
router.patch('/users/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        // Validation middleware
        const validations = [
            body('fullName').optional().trim().notEmpty().withMessage('Full name cannot be empty if provided.'),
            body('email').optional().isEmail().normalizeEmail().withMessage('Invalid email format if provided.'),
            body('phone').optional().trim().notEmpty().withMessage('Phone cannot be empty if provided.'),
            body('profileStatus').optional().isIn(ALLOWED_USER_STATUSES).withMessage('Invalid profile status.'),
            body('isVerified').optional().isBoolean().withMessage('Verified must be true or false.')
        ];

        // Run validations
        Promise.all(validations.map(validation => validation.run(req)))
            .then(() => {
                // Check for validation errors
                validate(req, res, function() {
                    // If validation passes, call the controller
                    adminController.updateUserDetails(req, res, next);
                });
            })
            .catch(err => next(err));
    });
});

// PUT /api/admin/users/:id/verify - Approve a user's profile (specific action)
router.put('/users/:id/verify', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminVerificationController.verifyUser(req, res, next);
    });
});

// PUT /api/admin/users/:id/reject-verification - Reject a user's verification request
router.put('/users/:id/reject-verification', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        // Validation middleware
        const validations = [
            body('reason').optional().trim()
        ];

        // Run validations
        Promise.all(validations.map(validation => validation.run(req)))
            .then(() => {
                // Check for validation errors
                validate(req, res, function() {
                    // If validation passes, call the controller
                    adminVerificationController.rejectVerification(req, res, next);
                });
            })
            .catch(err => next(err));
    });
});

// PUT /api/admin/users/:id/status - Update a user's account status (specific action)
router.put('/users/:id/status', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        // Validation middleware
        const validations = [
            body('status')
                .trim()
                .notEmpty().withMessage('New status is required.')
                .isIn(ALLOWED_USER_STATUSES).withMessage(`Status must be one of: ${ALLOWED_USER_STATUSES.join(', ')}`)
        ];

        // Run validations
        Promise.all(validations.map(validation => validation.run(req)))
            .then(() => {
                // Check for validation errors
                validate(req, res, function() {
                    // If validation passes, call the controller
                    adminController.updateUserStatus(req, res, next);
                });
            })
            .catch(err => next(err));
    });
});

// DELETE /api/admin/users/:id - Delete a user account
router.delete('/users/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.deleteUser(req, res, next);
    });
});

// GET /api/admin/users/verification-queue - Fetch users needing verification
router.get('/users/verification-queue', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getVerificationQueue(req, res, next);
    });
});

// GET /api/admin/users/reported - Fetch reported profiles
router.get('/users/reported', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getReportedProfiles(req, res, next);
    });
});

// GET /api/admin/users/reported/:id - Get details for a specific report
router.get('/users/reported/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getReportDetails(req, res, next);
    });
});

// PUT /api/admin/users/reported/:id/status - Update report status
router.put('/users/reported/:id/status', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        // Validation middleware
        const validations = [
            body('status')
                .trim()
                .notEmpty().withMessage('New report status is required.')
                .isIn(['PENDING', 'REVIEWED', 'DISMISSED', 'ACTIONED']).withMessage('Status must be one of: PENDING, REVIEWED, DISMISSED, ACTIONED')
        ];

        // Run validations
        Promise.all(validations.map(validation => validation.run(req)))
            .then(() => {
                // Check for validation errors
                validate(req, res, function() {
                    // If validation passes, call the controller
                    adminController.updateReportStatus(req, res, next);
                });
            })
            .catch(err => next(err));
    });
});

// GET /api/admin/users/reported/export/csv - Export reported profiles as CSV
router.get('/users/reported/export/csv', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.exportReportedProfilesCsv(req, res, next);
    });
});

// GET /api/admin/users/reported/export/xlsx - Export reported profiles as Excel
router.get('/users/reported/export/xlsx', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.exportReportedProfilesXlsx(req, res, next);
    });
});


// --- Admin Photo Moderation Routes (Protected) ---

// GET /api/admin/photos/pending - Fetch photos awaiting moderation
router.get('/photos/pending', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getPendingPhotos(req, res, next);
    });
});

// PUT /api/admin/photos/:photoId/status - Approve or Reject a photo
router.put('/photos/:photoId/status', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        // Validation middleware
        const validations = [
            body('status')
                .trim()
                .notEmpty().withMessage('New photo status is required.')
                .isIn(ALLOWED_PHOTO_STATUSES).withMessage(`Status must be one of: ${ALLOWED_PHOTO_STATUSES.join(', ')}`)
        ];

        // Run validations
        Promise.all(validations.map(validation => validation.run(req)))
            .then(() => {
                // Check for validation errors
                validate(req, res, function() {
                    // If validation passes, call the controller
                    adminController.updatePhotoStatus(req, res, next);
                });
            })
            .catch(err => next(err));
    });
});

// --- Admin Verification Document Routes (Protected) ---

// PUT /api/admin/verification/documents/:documentId/review - Review a verification document
router.put('/verification/documents/:documentId/review', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        // Validation middleware
        const validations = [
            body('status')
                .trim()
                .notEmpty().withMessage('Status is required.')
                .isIn(['APPROVED', 'REJECTED']).withMessage('Status must be either APPROVED or REJECTED'),
            body('adminNotes').optional().trim()
        ];

        // Run validations
        Promise.all(validations.map(validation => validation.run(req)))
            .then(() => {
                // Check for validation errors
                validate(req, res, function() {
                    // If validation passes, call the controller
                    adminVerificationController.reviewVerificationDocument(req, res, next);
                });
            })
            .catch(err => next(err));
    });
});


// --- AI & Matching Routes ---
// Import AI routes
const aiRoutes = require('./admin/ai.routes');
// Use the AI routes
router.use('/ai', aiRoutes);


// --- Content Management Routes ---
// Success Stories Routes (Placeholders)
router.get('/content/success-stories', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getSuccessStories(req, res, next);
    });
});

router.post('/content/success-stories', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.createSuccessStory(req, res, next);
    });
});

router.put('/content/success-stories/:storyId', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.updateSuccessStory(req, res, next);
    });
});

router.delete('/content/success-stories/:storyId', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.deleteSuccessStory(req, res, next);
    });
});

// Import blog routes
const blogRoutes = require('./admin/blog.routes');
// Use the blog routes
router.use('/content/blog', blogRoutes);


// --- Financial Routes ---
// Import financial routes
const financialRoutes = require('./admin/financial.routes');
// Use the financial routes
router.use('/financial', financialRoutes);


// --- System Routes (Placeholders) ---
router.get('/system/settings', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getSystemSettings(req, res, next);
    });
});

router.put('/system/settings', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.updateSystemSettings(req, res, next);
    });
});

router.get('/system/admins', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getAdminUsers(req, res, next);
    });
});

router.post('/system/admins', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.createAdminUser(req, res, next);
    });
});

router.put('/system/admins/:adminId', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.updateAdminUser(req, res, next);
    });
});

router.delete('/system/admins/:adminId', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.deleteAdminUser(req, res, next);
    });
});

router.get('/system/logs/security', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getSecurityLogs(req, res, next);
    });
});


// --- Feature Management Routes ---
// Import feature management routes
const featureManagementRoutes = require('./admin/featureManagement.routes');
// Use the feature management routes
router.use('/feature-management', featureManagementRoutes);

// --- Error Monitoring Routes ---
// Import error monitoring routes
const errorMonitoringRoutes = require('./admin/errorMonitoring.routes');
// Use the error monitoring routes
router.use('/error-monitoring', errorMonitoringRoutes);

// --- Premium Plans Routes ---
// GET /api/admin/premium-plans - Get all premium plans
router.get('/premium-plans', function(req, res, next) {
    authenticateAdmin(req, res, function() {
    // Return mock premium plans data for testing
    res.status(200).json({
        success: true,
        message: "Premium plans fetched successfully.",
        plans: [
            {
                id: "plan1",
                name: "Basic",
                price: 999,
                currency: "INR",
                duration: 30,
                features: ["View up to 10 contacts", "Basic matching", "Limited chat"],
                isActive: true
            },
            {
                id: "plan2",
                name: "Premium",
                price: 2999,
                currency: "INR",
                duration: 90,
                features: ["Unlimited contacts", "Advanced matching", "Unlimited chat", "Priority support"],
                isActive: true
            },
            {
                id: "plan3",
                name: "Gold",
                price: 4999,
                currency: "INR",
                duration: 180,
                features: ["Unlimited contacts", "AI-powered matching", "Unlimited chat", "Priority support", "Spotlight feature", "Personalized assistance"],
                isActive: true
            }
        ]
    });
    });
});

// --- Success Stories Routes ---
// GET /api/admin/success-stories - Get all success stories
router.get('/success-stories', function(req, res, next) {
    authenticateAdmin(req, res, function() {
    // Return mock success stories data for testing
    res.status(200).json({
        success: true,
        message: "Success stories fetched successfully.",
        stories: [
            {
                id: "story1",
                userId1: "user1",
                userId2: "user2",
                status: "MARRIED",
                storyText: "We met through Vaivahik and got married within 6 months!",
                testimonyText: "The AI matching was spot on!",
                rating: 5,
                marriageDate: "2023-11-15T00:00:00Z",
                photos: ["https://placehold.co/600x400/e91e63/ffffff?text=Wedding+Photo"],
                isPublic: true
            },
            {
                id: "story2",
                userId1: "user3",
                userId2: "user4",
                status: "ENGAGED",
                storyText: "We are so happy we found each other on this platform.",
                testimonyText: "The verification process made us feel safe.",
                rating: 4.5,
                engagementDate: "2023-12-25T00:00:00Z",
                photos: ["https://placehold.co/600x400/9c27b0/ffffff?text=Engagement+Photo"],
                isPublic: true
            }
        ]
    });
    });
});

// --- Reported Profiles Routes ---
// GET /api/admin/reported-profiles - Get all reported profiles
router.get('/reported-profiles', function(req, res, next) {
    authenticateAdmin(req, res, function() {
    // Return mock reported profiles data for testing
    res.status(200).json({
        success: true,
        message: "Reported profiles fetched successfully.",
        reports: [
            {
                id: "report1",
                reportedUser: {
                    id: "user5",
                    name: "Fake User",
                    email: "<EMAIL>",
                    profilePicture: "https://placehold.co/400x400/f44336/ffffff?text=FU"
                },
                reportedBy: {
                    id: "user6",
                    name: "Genuine User",
                    email: "<EMAIL>"
                },
                reason: "Fake Profile",
                status: "PENDING",
                reportCount: 3,
                reportDate: "2023-06-20T08:30:00Z",
                additionalNotes: "This profile is using fake photos and information."
            },
            {
                id: "report2",
                reportedUser: {
                    id: "user7",
                    name: "Inappropriate User",
                    email: "<EMAIL>",
                    profilePicture: "https://placehold.co/400x400/ff9800/ffffff?text=IU"
                },
                reportedBy: {
                    id: "user8",
                    name: "Concerned User",
                    email: "<EMAIL>"
                },
                reason: "Inappropriate Messages",
                status: "PENDING",
                reportCount: 2,
                reportDate: "2023-06-22T14:45:00Z",
                additionalNotes: "This user is sending inappropriate messages."
            }
        ]
    });
    });
});

// --- Preference Configuration Routes (Protected) ---

// GET /api/admin/preference-config - Get all preference configuration
router.get('/preference-config', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.getPreferenceConfiguration(req, res, next);
    });
});

// PUT /api/admin/preference-config - Update preference configuration
router.put('/preference-config', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.updatePreferenceConfiguration(req, res, next);
    });
});

// DELETE /api/admin/preference-config - Delete preference configuration item
router.delete('/preference-config', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        adminController.deletePreferenceConfiguration(req, res, next);
    });
});

module.exports = router;
