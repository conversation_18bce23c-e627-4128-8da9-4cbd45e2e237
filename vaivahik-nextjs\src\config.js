// API Configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
export const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:5000/api';

// Feature Flags System
export const FEATURE_FLAGS = {
  // Data Source Flags
  useRealBackend: process.env.NEXT_PUBLIC_USE_REAL_BACKEND === 'true' || false,
  useRealPaymentGateway: process.env.NEXT_PUBLIC_USE_REAL_PAYMENT === 'true' || false,
  useRealNotifications: process.env.NEXT_PUBLIC_USE_REAL_NOTIFICATIONS === 'true' || false,

  // Feature Availability Flags
  enableBiodataTemplates: true,
  enableSpotlightFeatures: true,
  enablePaymentGateway: process.env.NEXT_PUBLIC_ENABLE_PAYMENT_GATEWAY === 'true' || false,
  enableNotifications: true,
  enableMatchingAlgorithm: true,
  enableAdvancedSearch: true,
  enableSimilaritySearch: true,
  enableCompatibilityScores: true,
  enableRedisCache: true,

  // UI Feature Flags
  enableDarkMode: true,
  enableAnimations: true,
  enableFilterChips: true,
  enableSearchHistory: true,

  // Development Flags
  showMockDataIndicator: true,
  enableDebugLogging: process.env.NODE_ENV === 'development'
};

// Default API Endpoints
export const API_ENDPOINTS = {
  // Auth
  login: '/auth/login',
  register: '/auth/register',
  refreshToken: '/auth/refresh-token',

  // User
  userProfile: '/user/profile',
  userSettings: '/user/settings',

  // Admin
  adminDashboard: '/admin/dashboard',
  adminUsers: '/admin/users',
  adminSettings: '/admin/settings',
  adminBiodataTemplates: '/admin/biodata/templates',
  adminSpotlightFeatures: '/admin/spotlight/features',
  adminFinancial: '/admin/financial'
};

// Privacy Settings Options
export const PRIVACY_OPTIONS = {
  ALL_USERS: 'Visible to all users',
  PREMIUM_USERS: 'Visible only to premium users',
  ACCEPTED_INTEREST: 'Visible only after interest is accepted',
  HIDDEN: 'Hidden from all users'
};

// Display Name Preference Options
export const DISPLAY_NAME_OPTIONS = {
  FULL_NAME: {
    value: 'FULL_NAME',
    label: 'Full Name',
    description: 'Show your complete name (e.g., "Rahul Sharma")',
    privacy: 'Low',
    icon: '👤'
  },
  FIRST_NAME: {
    value: 'FIRST_NAME',
    label: 'First Name Only',
    description: 'Show only your first name (e.g., "Rahul")',
    privacy: 'Medium',
    icon: '🙂',
    recommended: true
  },
  PROFILE_ID: {
    value: 'PROFILE_ID',
    label: 'Profile ID',
    description: 'Show profile ID based on gender (e.g., "Profile M1234")',
    privacy: 'High',
    icon: '🆔'
  },
  ANONYMOUS: {
    value: 'ANONYMOUS',
    label: 'Anonymous',
    description: 'Show as "Someone" for maximum privacy',
    privacy: 'Maximum',
    icon: '🕶️'
  }
};

// Default Privacy Settings
export const DEFAULT_PRIVACY_SETTINGS = {
  // Content Privacy
  photoPrivacy: 'ALL_USERS',
  phonePrivacy: 'PREMIUM_USERS',
  emailPrivacy: 'PREMIUM_USERS',
  socialMediaPrivacy: 'PREMIUM_USERS',
  educationPrivacy: 'ALL_USERS',
  careerPrivacy: 'ALL_USERS',
  familyPrivacy: 'ALL_USERS',
  birthDetailsPrivacy: 'ALL_USERS',
  onlineStatusPrivacy: 'ALL_USERS',
  lastActivePrivacy: 'ALL_USERS',

  // Display Name Privacy
  displayNamePreference: 'FIRST_NAME',
  showNameInNotifications: true,
  showNameInSearch: true,
  showNameInMatches: true,
  showNameInMessages: true,
  allowProfileViews: true,
  showOnlineStatus: false,
  showLastSeen: false,
  allowDirectMessages: true,
  showContactInfo: false
};
