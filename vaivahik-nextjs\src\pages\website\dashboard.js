/**
 * Enhanced User Dashboard - Complete Matrimony Dashboard
 * Includes all admin features adapted for user experience with modern design
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Avatar,
  Button,
  Chip,
  IconButton,
  Badge,
  LinearProgress,
  Fab,
  styled,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip
} from '@mui/material';
import {
  FavoriteRounded as HeartIcon,
  MessageRounded as MessageIcon,
  VisibilityRounded as ViewIcon,
  TrendingUpRounded as TrendingIcon,
  EmojiEventsRounded as TrophyIcon,
  LocalFireDepartmentRounded as FireIcon,
  AutoAwesomeRounded as SparkleIcon,
  SearchRounded as SearchIcon,
  Verified as VerifiedIcon,
  WorkspacePremium as PremiumIcon,
  Chat as ChatIcon,
  Description as BiodataIcon,
  FlashOn as SpotlightIcon,
  AccountCircle as ProfileIcon,
  Phone as CallIcon,
  Settings as SettingsIcon,
  FilterList as FilterIcon,
  <PERSON>ne as AdvancedFilterIcon,
  Star as StarIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Security as SecurityIcon,
  Psychology as AIIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';

// Import dashboard components
import VerificationQueueWidget from '@/components/dashboard/VerificationQueueWidget';
import ChatModuleWidget from '@/components/dashboard/ChatModuleWidget';
import BiodataTemplatesWidget from '@/components/dashboard/BiodataTemplatesWidget';
import SpotlightFeaturesWidget from '@/components/dashboard/SpotlightFeaturesWidget';
import ProfileCompletionWidget from '@/components/dashboard/ProfileCompletionWidget';
import VerificationBenefitsBanner from '@/components/dashboard/VerificationBenefitsBanner';
import PremiumPlansModal from '@/components/dashboard/PremiumPlansModal';
import SearchWidget from '@/components/dashboard/SearchWidget';
import UserProfilesWidget from '@/components/dashboard/UserProfilesWidget';

// Import admin services for enhanced functionality
import { dashboardApi } from '@/services/adminApiService';
import { fetchMockData } from '@/services/mockDataService';

// Stunning styled components with glassmorphism and animations
const DashboardContainer = styled(Box)(({ theme }) => ({
  minHeight: '100vh',
  background: `
    linear-gradient(135deg,
      rgba(255, 182, 193, 0.1) 0%,
      rgba(255, 240, 245, 0.15) 25%,
      rgba(248, 249, 250, 0.2) 50%,
      rgba(255, 228, 225, 0.15) 75%,
      rgba(255, 182, 193, 0.1) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(255, 105, 180, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 182, 193, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 25% 25%, rgba(255, 105, 180, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 182, 193, 0.05) 0%, transparent 50%)
    `,
    animation: 'float 20s ease-in-out infinite',
    zIndex: 0
  },
  '@keyframes float': {
    '0%, 100%': { transform: 'translate(0, 0) rotate(0deg)' },
    '33%': { transform: 'translate(30px, -30px) rotate(120deg)' },
    '66%': { transform: 'translate(-20px, 20px) rotate(240deg)' }
  }
}));

const GlassCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 182, 193, 0.2)
  `,
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF69B4, #FFB6C1, #FFC0CB)',
    borderRadius: '24px 24px 0 0'
  },
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: `
      0 32px 64px rgba(0, 0, 0, 0.15),
      0 16px 48px rgba(255, 182, 193, 0.3)
    `
  }
}));

const WelcomeCard = styled(GlassCard)(({ theme }) => ({
  background: `
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.98) 0%,
      rgba(255, 240, 245, 0.95) 100%
    )
  `,
  '&::before': {
    background: 'linear-gradient(90deg, #FF1493, #FF69B4, #FFB6C1)'
  }
}));

const StatsCard = styled(GlassCard)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(3),
  '&:hover .stats-icon': {
    transform: 'scale(1.2) rotate(10deg)',
    filter: 'drop-shadow(0 8px 16px rgba(255, 105, 180, 0.3))'
  }
}));

const MatchCard = styled(GlassCard)(({ theme }) => ({
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-12px) scale(1.02)',
    '& .match-avatar': {
      transform: 'scale(1.1)',
      filter: 'brightness(1.1)'
    }
  }
}));

const FloatingActionButton = styled(Fab)(({ theme }) => ({
  position: 'fixed',
  bottom: 24,
  right: 24,
  background: 'linear-gradient(135deg, #FF69B4 0%, #FF1493 100%)',
  color: 'white',
  boxShadow: '0 8px 32px rgba(255, 105, 180, 0.4)',
  '&:hover': {
    background: 'linear-gradient(135deg, #FF1493 0%, #DC143C 100%)',
    transform: 'scale(1.1)',
    boxShadow: '0 12px 48px rgba(255, 105, 180, 0.6)'
  }
}));

export default function UserDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  // Dashboard state
  const [activeTab, setActiveTab] = useState(0);
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const [showSearchFilters, setShowSearchFilters] = useState(false);
  const [stats, setStats] = useState({
    profileViews: 0,
    interests: 0,
    messages: 0,
    matches: 0
  });
  const [recentMatches, setRecentMatches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userData, setUserData] = useState({});
  const [isPremium, setIsPremium] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Fetch real dashboard data using admin API services
      const [dashboardStats, recentUsers, userActivity] = await Promise.all([
        dashboardApi.getDashboardStats().catch(() => null),
        fetchMockData('/admin/dashboard/recent-users', { limit: 3 }).catch(() => null),
        fetchMockData('/admin/dashboard/recent-activity', { limit: 5 }).catch(() => null)
      ]);

      // Process dashboard stats for user view
      if (dashboardStats?.success) {
        setStats({
          profileViews: dashboardStats.stats?.totalUsers || 127,
          interests: Math.floor((dashboardStats.stats?.totalUsers || 127) * 0.18), // ~18% interest rate
          messages: Math.floor((dashboardStats.stats?.totalUsers || 127) * 0.06), // ~6% message rate
          matches: dashboardStats.stats?.successfulMatches || 45
        });
      } else {
        // Fallback to mock data
        setStats({
          profileViews: 127,
          interests: 23,
          messages: 8,
          matches: 45
        });
      }

      // Process recent users as potential matches
      if (recentUsers?.success && recentUsers.users) {
        const processedMatches = recentUsers.users.slice(0, 3).map(user => ({
          id: user.id,
          name: user.name,
          age: user.age,
          location: user.location,
          photo: user.photo || '/api/placeholder/150/150',
          compatibility: Math.floor(Math.random() * 20) + 80, // Random compatibility 80-100%
          isOnline: Math.random() > 0.5,
          verified: user.verified,
          premium: user.premium
        }));
        setRecentMatches(processedMatches);
      } else {
        // Fallback matches
        setRecentMatches([
          {
            id: 1,
            name: 'Priya Sharma',
            age: 26,
            location: 'Mumbai',
            photo: '/api/placeholder/150/150',
            compatibility: 94,
            isOnline: true,
            verified: true,
            premium: false
          },
          {
            id: 2,
            name: 'Anita Patil',
            age: 24,
            location: 'Pune',
            photo: '/api/placeholder/150/150',
            compatibility: 89,
            isOnline: false,
            verified: true,
            premium: true
          },
          {
            id: 3,
            name: 'Kavya Desai',
            age: 27,
            location: 'Nashik',
            photo: '/api/placeholder/150/150',
            compatibility: 87,
            isOnline: true,
            verified: false,
            premium: false
          }
        ]);
      }

      // Enhanced user data
      setUserData({
        name: user?.name || 'Beautiful Soul',
        profilePhoto: user?.profilePhoto,
        gender: user?.gender || 'male',
        age: user?.age || 28,
        education: user?.education || 'Engineering',
        occupation: user?.occupation || 'Software Engineer',
        location: user?.location || 'Mumbai',
        profileCompletion: user?.profileCompletion || 75
      });

      setIsPremium(user?.isPremium || false);
      setIsVerified(user?.isVerified || false);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      // Set fallback data
      setStats({
        profileViews: 127,
        interests: 23,
        messages: 8,
        matches: 45
      });
      setRecentMatches([
        {
          id: 1,
          name: 'Priya Sharma',
          age: 26,
          location: 'Mumbai',
          photo: '/api/placeholder/150/150',
          compatibility: 94,
          isOnline: true
        }
      ]);
      setUserData({
        name: user?.name || 'Beautiful Soul',
        profilePhoto: user?.profilePhoto,
        gender: 'male',
        age: 28,
        education: 'Engineering',
        occupation: 'Software Engineer'
      });
      setLoading(false);
    }
  };

  const handleViewProfile = (matchId) => {
    router.push(`/profile/${matchId}`);
  };

  const handleBrowseMatches = () => {
    router.push('/matches');
  };

  const handleMessages = () => {
    if (!isPremium) {
      setShowPremiumModal(true);
      return;
    }
    router.push('/messages');
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handlePremiumFeatureClick = (feature) => {
    if (!isPremium) {
      setShowPremiumModal(true);
      return;
    }
    // Handle premium feature access
    console.log('Accessing premium feature:', feature);
  };

  return (
    <>
      <Head>
        <title>Dashboard - Find Your Perfect Match | Vaivahik</title>
        <meta name="description" content="Your personalized matrimony dashboard with matches, messages, and profile insights" />
      </Head>

      <DashboardContainer>
        <Container maxWidth="xl" sx={{ position: 'relative', zIndex: 1, py: 4 }}>
          {/* Welcome Section */}
          <WelcomeCard sx={{ mb: 4, p: 4 }}>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={8}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <SparkleIcon sx={{ color: '#FF69B4', mr: 1, fontSize: 32 }} />
                  <Typography variant="h3" fontWeight="700"
                    sx={{
                      background: 'linear-gradient(135deg, #FF1493, #FF69B4)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}>
                    Welcome back, {userData.name}! ✨
                  </Typography>
                  {isVerified && (
                    <VerifiedIcon sx={{ color: '#4CAF50', ml: 1, fontSize: 28 }} />
                  )}
                  {isPremium && (
                    <PremiumIcon sx={{ color: '#FFD700', ml: 1, fontSize: 28 }} />
                  )}
                </Box>
                <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
                  Your perfect match is waiting to meet you. Let's make magic happen! 💕
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<SearchIcon />}
                    onClick={handleBrowseMatches}
                    sx={{
                      background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
                      borderRadius: 3,
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      boxShadow: '0 8px 24px rgba(255, 105, 180, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #FF1493, #DC143C)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 12px 32px rgba(255, 105, 180, 0.4)'
                      }
                    }}
                  >
                    Discover Matches
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<MessageIcon />}
                    onClick={handleMessages}
                    sx={{
                      borderColor: '#FF69B4',
                      color: '#FF69B4',
                      borderRadius: 3,
                      px: 4,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      '&:hover': {
                        borderColor: '#FF1493',
                        backgroundColor: 'rgba(255, 105, 180, 0.1)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    {isPremium ? 'Messages' : 'Messages (Premium)'}
                  </Button>
                  {!isPremium && (
                    <Button
                      variant="contained"
                      size="large"
                      startIcon={<PremiumIcon />}
                      onClick={() => setShowPremiumModal(true)}
                      sx={{
                        background: 'linear-gradient(135deg, #FFD700, #FFA000)',
                        color: '#000',
                        borderRadius: 3,
                        px: 4,
                        py: 1.5,
                        fontSize: '1.1rem',
                        fontWeight: 600,
                        '&:hover': {
                          background: 'linear-gradient(135deg, #FFA000, #FF8F00)',
                          transform: 'translateY(-2px)'
                        }
                      }}
                    >
                      Upgrade to Premium
                    </Button>
                  )}
                </Box>
              </Grid>
              <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
                <Box sx={{ position: 'relative', display: 'inline-block' }}>
                  <Avatar
                    src={userData.profilePhoto || '/api/placeholder/120/120'}
                    sx={{
                      width: 120,
                      height: 120,
                      border: `4px solid ${isVerified ? '#4CAF50' : '#FF69B4'}`,
                      boxShadow: '0 8px 32px rgba(255, 105, 180, 0.3)'
                    }}
                  />
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                    badgeContent={
                      <Box sx={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                        border: '3px solid white',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
                      }} />
                    }
                  />
                </Box>
              </Grid>
            </Grid>
          </WelcomeCard>

          {/* Verification Benefits Banner */}
          {!isVerified && (
            <Box sx={{ mb: 4 }}>
              <VerificationBenefitsBanner
                isVerified={isVerified}
                verificationStatus="PENDING"
                onVerifyClick={() => setActiveTab(2)}
              />
            </Box>
          )}

          {/* Stats Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {[
              { icon: ViewIcon, label: 'Profile Views', value: stats.profileViews, color: '#2196F3', bgColor: 'rgba(33, 150, 243, 0.1)' },
              { icon: HeartIcon, label: 'Interests', value: stats.interests, color: '#E91E63', bgColor: 'rgba(233, 30, 99, 0.1)' },
              { icon: MessageIcon, label: 'Messages', value: stats.messages, color: '#4CAF50', bgColor: 'rgba(76, 175, 80, 0.1)' },
              { icon: TrophyIcon, label: 'Matches', value: stats.matches, color: '#FF9800', bgColor: 'rgba(255, 152, 0, 0.1)' }
            ].map((stat, index) => (
              <Grid item xs={6} md={3} key={index}>
                <StatsCard>
                  <Box sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    background: stat.bgColor,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 16px',
                    transition: 'all 0.3s ease'
                  }}>
                    <stat.icon
                      className="stats-icon"
                      sx={{
                        fontSize: 32,
                        color: stat.color,
                        transition: 'all 0.3s ease'
                      }}
                    />
                  </Box>
                  <Typography variant="h4" fontWeight="700" color={stat.color} gutterBottom>
                    {stat.value}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" fontWeight="500">
                    {stat.label}
                  </Typography>
                </StatsCard>
              </Grid>
            ))}
          </Grid>

          {/* Dashboard Tabs */}
          <Box sx={{ mb: 4 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  minWidth: 120,
                  fontWeight: 600,
                  fontSize: '1rem',
                  textTransform: 'none',
                  color: '#666',
                  '&.Mui-selected': {
                    color: '#FF5F6D'
                  }
                },
                '& .MuiTabs-indicator': {
                  background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                  height: 3,
                  borderRadius: 2
                }
              }}
            >
              <Tab label="Overview" icon={<TrendingIcon />} iconPosition="start" />
              <Tab label="Search" icon={<SearchIcon />} iconPosition="start" />
              <Tab label="Profile" icon={<ProfileIcon />} iconPosition="start" />
              <Tab label="Verification" icon={<VerifiedIcon />} iconPosition="start" />
              <Tab label="Chat" icon={<ChatIcon />} iconPosition="start" />
              <Tab label="Biodata" icon={<BiodataIcon />} iconPosition="start" />
              <Tab label="Spotlight" icon={<SpotlightIcon />} iconPosition="start" />
            </Tabs>
          </Box>

          {/* Tab Content */}
          {activeTab === 0 && (
            <Box>
              {/* Recent Matches Section */}
          <GlassCard sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <FireIcon sx={{ color: '#FF69B4', mr: 2, fontSize: 32 }} />
                <Typography variant="h5" fontWeight="700">
                  Your Perfect Matches 🔥
                </Typography>
              </Box>
              <Button
                variant="text"
                endIcon={<TrendingIcon />}
                onClick={handleBrowseMatches}
                sx={{ color: '#FF69B4', fontWeight: 600 }}
              >
                View All
              </Button>
            </Box>

            <Grid container spacing={3}>
              {recentMatches.map((match) => (
                <Grid item xs={12} sm={6} md={4} key={match.id}>
                  <MatchCard onClick={() => handleViewProfile(match.id)}>
                    <CardContent sx={{ textAlign: 'center', p: 3 }}>
                      <Box sx={{ position: 'relative', mb: 2 }}>
                        <Avatar
                          src={match.photo}
                          className="match-avatar"
                          sx={{
                            width: 100,
                            height: 100,
                            margin: '0 auto',
                            transition: 'all 0.3s ease',
                            border: '3px solid #FFB6C1'
                          }}
                        />
                        {match.isOnline && (
                          <Box sx={{
                            position: 'absolute',
                            bottom: 8,
                            right: '50%',
                            transform: 'translateX(50%)',
                            width: 16,
                            height: 16,
                            borderRadius: '50%',
                            background: '#4CAF50',
                            border: '2px solid white',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
                          }} />
                        )}
                      </Box>
                      <Typography variant="h6" fontWeight="600" gutterBottom>
                        {match.name}, {match.age}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {match.location}
                      </Typography>
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 1 }}>
                        {match.verified && (
                          <Chip
                            icon={<VerifiedIcon sx={{ fontSize: 16 }} />}
                            label="Verified"
                            size="small"
                            sx={{
                              background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
                              color: 'white',
                              fontWeight: 600,
                              fontSize: '0.75rem'
                            }}
                          />
                        )}
                        {match.premium && (
                          <Chip
                            icon={<PremiumIcon sx={{ fontSize: 16 }} />}
                            label="Premium"
                            size="small"
                            sx={{
                              background: 'linear-gradient(135deg, #FFD700, #FFA000)',
                              color: '#000',
                              fontWeight: 600,
                              fontSize: '0.75rem'
                            }}
                          />
                        )}
                      </Box>
                      <Chip
                        label={`${match.compatibility}% Match`}
                        color="primary"
                        size="small"
                        sx={{
                          background: 'linear-gradient(135deg, #FF69B4, #FF1493)',
                          color: 'white',
                          fontWeight: 600,
                          mt: 1
                        }}
                      />
                    </CardContent>
                  </MatchCard>
                </Grid>
              ))}
            </Grid>
          </GlassCard>
            </Box>
          )}

          {/* Search Tab */}
          {activeTab === 1 && (
            <Box>
              <SearchWidget
                isPremium={isPremium}
                onPremiumFeatureClick={handlePremiumFeatureClick}
              />
            </Box>
          )}

          {/* Profile Tab */}
          {activeTab === 2 && (
            <Box>
              <ProfileCompletionWidget
                userId={user?.id}
                userData={userData}
              />
            </Box>
          )}

          {/* Verification Tab */}
          {activeTab === 3 && (
            <Box>
              <VerificationQueueWidget
                userId={user?.id}
                isVerified={isVerified}
              />
            </Box>
          )}

          {/* Chat Tab */}
          {activeTab === 4 && (
            <Box>
              <ChatModuleWidget
                userId={user?.id}
                isPremium={isPremium}
                onPremiumFeatureClick={handlePremiumFeatureClick}
              />
            </Box>
          )}

          {/* Biodata Tab */}
          {activeTab === 5 && (
            <Box>
              <BiodataTemplatesWidget
                userId={user?.id}
                userGender={userData.gender}
              />
            </Box>
          )}

          {/* Spotlight Tab */}
          {activeTab === 6 && (
            <Box>
              <SpotlightFeaturesWidget
                userId={user?.id}
                currentSpotlight={null}
              />
            </Box>
          )}

        </Container>

        {/* Floating Action Buttons */}
        <Box sx={{ position: 'fixed', bottom: 24, right: 24, display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Tooltip title="Browse Matches" placement="left">
            <FloatingActionButton onClick={handleBrowseMatches}>
              <SearchIcon sx={{ fontSize: 28 }} />
            </FloatingActionButton>
          </Tooltip>
          {!isPremium && (
            <Tooltip title="Upgrade to Premium" placement="left">
              <Fab
                sx={{
                  background: 'linear-gradient(135deg, #FFD700 0%, #FFA000 100%)',
                  color: '#000',
                  boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #FFA000 0%, #FF8F00 100%)',
                    transform: 'scale(1.1)',
                    boxShadow: '0 12px 48px rgba(255, 215, 0, 0.6)'
                  }
                }}
                onClick={() => setShowPremiumModal(true)}
              >
                <PremiumIcon sx={{ fontSize: 28 }} />
              </Fab>
            </Tooltip>
          )}
        </Box>

        {/* Premium Plans Modal */}
        <PremiumPlansModal
          open={showPremiumModal}
          onClose={() => setShowPremiumModal(false)}
          currentPlan={isPremium ? 'Premium' : null}
        />
      </DashboardContainer>
    </>
  );
}
