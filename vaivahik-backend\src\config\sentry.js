/**
 * Sentry Configuration
 *
 * This module initializes and configures Sentry for error monitoring.
 * It sets up integrations, sampling rates, and environment-specific settings.
 */

const Sentry = require('@sentry/node');
const { RewriteFrames } = require('@sentry/integrations');
const path = require('path');

/**
 * Initialize Sentry with the provided DSN and options
 * @param {Object} options - Configuration options
 * @param {string} options.dsn - Sentry DSN (required in production)
 * @param {string} options.environment - Environment name (development, staging, production)
 * @param {number} options.tracesSampleRate - Percentage of transactions to sample (0.0 to 1.0)
 * @param {boolean} options.debug - Enable debug mode for Sentry
 */
const initSentry = (options = {}) => {
  const {
    dsn = process.env.SENTRY_DSN,
    environment = process.env.NODE_ENV || 'development',
    tracesSampleRate = environment === 'production' ? 0.2 : 1.0,
    debug = environment !== 'production'
  } = options;

  // Don't initialize if no DSN is provided in production
  if (!dsn && environment === 'production') {
    console.warn('Sentry DSN not provided. Error monitoring will not be enabled in production.');
    return false;
  }

  // Initialize Sentry
  Sentry.init({
    dsn,
    environment,
    debug,
    tracesSampleRate,
    integrations: [
      // Rewrite stack traces to make them more readable
      new RewriteFrames({
        root: path.dirname(require.main.filename)
      })
    ],
    // Set the release version if available
    release: process.env.npm_package_version || '1.0.0',
    // Capture 100% of errors in development, but sample in production
    sampleRate: environment === 'production' ? 0.5 : 1.0,
    // Don't send PII data by default
    sendDefaultPii: false,
    // Normalize URLs to avoid duplicate issues
    normalizeDepth: 5,
    // Capture breadcrumbs for better debugging
    maxBreadcrumbs: 50,
    // Customize the beforeSend function to filter sensitive information
    beforeSend: (event, hint) => {
      // Filter out sensitive information from the event
      if (event.request && event.request.headers) {
        // Remove authorization headers
        delete event.request.headers.authorization;
        delete event.request.headers.Authorization;

        // Remove cookies
        delete event.request.headers.cookie;
        delete event.request.headers.Cookie;
      }

      // Filter out sensitive information from the request body
      if (event.request && event.request.data) {
        const sensitiveFields = ['password', 'token', 'secret', 'apiKey', 'api_key', 'key'];

        try {
          const data = typeof event.request.data === 'string'
            ? JSON.parse(event.request.data)
            : event.request.data;

          sensitiveFields.forEach(field => {
            if (data[field]) {
              data[field] = '[FILTERED]';
            }
          });

          event.request.data = JSON.stringify(data);
        } catch (e) {
          // If we can't parse the data, just leave it as is
        }
      }

      return event;
    }
  });

  console.log(`Sentry initialized in ${environment} environment`);
  return true;
};

/**
 * Express middleware to capture errors and send them to Sentry
 * @param {Error} err - The error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const sentryErrorHandler = (err, req, res, next) => {
  // Capture the error with the user context if available
  if (req.user) {
    Sentry.setUser({
      id: req.user.userId,
      email: req.user.email,
      ip_address: req.ip
    });
  }

  // Add request information to the error
  Sentry.setContext('request', {
    url: req.url,
    method: req.method,
    headers: req.headers,
    query: req.query,
    ip: req.ip
  });

  // Capture the exception
  Sentry.captureException(err);

  // Continue to the next error handler
  next(err);
};

/**
 * Express middleware to track requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const sentryRequestHandler = (req, res, next) => {
  // Capture basic request data
  Sentry.setTag('url', req.url);
  Sentry.setTag('method', req.method);
  if (req.ip) {
    Sentry.setTag('ip', req.ip);
  }
  next();
};

/**
 * Express middleware to track errors
 */
const sentryTracingHandler = (req, res, next) => {
  // Disable Sentry tracing completely in development
  next();
};

module.exports = {
  Sentry,
  initSentry,
  sentryErrorHandler,
  sentryRequestHandler,
  sentryTracingHandler
};
