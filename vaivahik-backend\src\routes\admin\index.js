// src/routes/admin/index.js

const express = require('express');
const router = express.Router();

// Import admin route modules
const photoModerationRoutes = require('./photoModeration.routes');
const promotionsRoutes = require('./promotions.routes');
const textModerationRoutes = require('./textModeration.routes');
const chatSettingsRoutes = require('./chatSettings.routes');
const biodataRoutes = require('./biodata.routes');
const spotlightRoutes = require('./spotlight.routes');
const phasesRoutes = require('./phases');
const dataAnalyticsRoutes = require('./data-analytics');
// Import other admin routes as needed

// Mount admin route modules
router.use('/photo-moderation', photoModerationRoutes);
router.use('/promotions', promotionsRoutes);
router.use('/text-moderation', textModerationRoutes);
router.use('/chat-settings', chatSettingsRoutes);
router.use('/biodata', biodataRoutes);
router.use('/spotlight', spotlightRoutes);
router.use('/phases', phasesRoutes);
router.use('/data-analytics', dataAnalyticsRoutes);
// Mount other admin routes as needed

module.exports = router;
