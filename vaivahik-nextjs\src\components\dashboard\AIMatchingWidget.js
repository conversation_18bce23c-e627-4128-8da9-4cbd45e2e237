/**
 * AI Matching Widget - Leverages existing ML matching service
 * Displays AI-powered matches with different algorithm phases
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Chip,
  Button,
  LinearProgress,
  IconButton,
  Tooltip,
  Divider,
  styled,
  CircularProgress,
  Alert
} from '@mui/material';

// Icons
import {
  Psychology as AIIcon,
  Favorite as HeartIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Star as StarIcon,
  TrendingUp as TrendingIcon,
  AutoAwesome as SparkleIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  ContactPhone as ContactIcon
} from '@mui/icons-material';

// Styled components
const AICard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.1)',
  transition: 'all 0.3s ease',
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 16px 48px rgba(102, 126, 234, 0.3)'
  }
}));

const MatchCard = styled(Card)(({ theme }) => ({
  borderRadius: 12,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 32px rgba(0,0,0,0.15)'
  }
}));

const ScoreChip = styled(Chip)(({ theme, score }) => ({
  fontWeight: 600,
  background: score >= 90 ? 'linear-gradient(135deg, #4CAF50, #8BC34A)' :
             score >= 80 ? 'linear-gradient(135deg, #FF9800, #FFC107)' :
             score >= 70 ? 'linear-gradient(135deg, #2196F3, #03DAC6)' :
             'linear-gradient(135deg, #9E9E9E, #BDBDBD)',
  color: 'white'
}));

// Mock AI matching phases (based on your ML service)
const matchingPhases = [
  { version: 'v1.0', name: 'Rule-Based Matching', description: 'Traditional compatibility' },
  { version: 'v1.5', name: 'Flexible Matching', description: 'Adaptive preferences' },
  { version: 'v2.0', name: 'Personalized Matching', description: 'Learning preferences' },
  { version: 'v2.5', name: 'Intelligent Matching', description: 'Advanced AI insights' },
  { version: 'v3.0', name: 'Advanced AI Matching', description: 'Deep learning model' }
];

export default function AIMatchingWidget({ userId, isPremium, onPremiumFeatureClick }) {
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPhase, setCurrentPhase] = useState(matchingPhases[0]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchAIMatches();
  }, [currentPhase]);

  const fetchAIMatches = async () => {
    setLoading(true);
    try {
      // Simulate API call to your ML matching service
      // This would call your existing /api/matches endpoint with AI parameters
      setTimeout(() => {
        const mockMatches = [
          {
            id: 1,
            name: 'Priya Sharma',
            age: 26,
            location: 'Mumbai, Maharashtra',
            photo: '/api/placeholder/150/150',
            compatibility: 94,
            education: 'MBA Finance',
            occupation: 'Investment Banker',
            isOnline: true,
            aiInsights: ['Same profession interest', 'Compatible lifestyle', 'Shared values'],
            matchReason: 'High compatibility in career goals and family values'
          },
          {
            id: 2,
            name: 'Anita Patil',
            age: 24,
            location: 'Pune, Maharashtra',
            photo: '/api/placeholder/150/150',
            compatibility: 89,
            education: 'B.Tech Computer Science',
            occupation: 'Software Engineer',
            isOnline: false,
            aiInsights: ['Tech background match', 'Similar age group', 'Regional preference'],
            matchReason: 'Strong technical compatibility and regional alignment'
          },
          {
            id: 3,
            name: 'Kavya Desai',
            age: 27,
            location: 'Nashik, Maharashtra',
            photo: '/api/placeholder/150/150',
            compatibility: 87,
            education: 'M.Sc Biotechnology',
            occupation: 'Research Scientist',
            isOnline: true,
            aiInsights: ['Academic excellence', 'Research mindset', 'Innovation focus'],
            matchReason: 'Intellectual compatibility and shared academic interests'
          },
          {
            id: 4,
            name: 'Sneha Joshi',
            age: 25,
            location: 'Kolhapur, Maharashtra',
            photo: '/api/placeholder/150/150',
            compatibility: 85,
            education: 'CA',
            occupation: 'Chartered Accountant',
            isOnline: false,
            aiInsights: ['Financial expertise', 'Professional stability', 'Traditional values'],
            matchReason: 'Professional compatibility and value alignment'
          }
        ];
        setMatches(mockMatches);
        setLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error fetching AI matches:', error);
      setLoading(false);
    }
  };

  const handleRefreshMatches = async () => {
    setRefreshing(true);
    await fetchAIMatches();
    setRefreshing(false);
  };

  const handleViewProfile = (matchId) => {
    // Navigate to profile view
    console.log('Viewing profile:', matchId);
  };

  const handleSendInterest = (matchId) => {
    if (!isPremium) {
      onPremiumFeatureClick('send-interest');
      return;
    }
    console.log('Sending interest to:', matchId);
  };

  const handleSendMessage = (matchId) => {
    if (!isPremium) {
      onPremiumFeatureClick('send-message');
      return;
    }
    console.log('Sending message to:', matchId);
  };

  const handleContactReveal = (matchId) => {
    if (!isPremium) {
      onPremiumFeatureClick('contact-reveal');
      return;
    }
    console.log('Revealing contact for:', matchId);
  };

  return (
    <Box>
      {/* AI Matching Header */}
      <AICard sx={{ mb: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AIIcon sx={{ fontSize: 40, mr: 2 }} />
              <Box>
                <Typography variant="h5" fontWeight="700">
                  AI-Powered Matching
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Advanced algorithms find your perfect match
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Matches">
                <IconButton
                  onClick={handleRefreshMatches}
                  disabled={refreshing}
                  sx={{ color: 'white' }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Current AI Phase */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" fontWeight="600" gutterBottom>
              Current Algorithm: {currentPhase.name}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, mb: 2 }}>
              {currentPhase.description}
            </Typography>
            <LinearProgress
              variant="determinate"
              value={75}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#4CAF50',
                  borderRadius: 4
                }
              }}
            />
          </Box>

          {/* AI Insights */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip
              icon={<SparkleIcon />}
              label="Smart Recommendations"
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            <Chip
              icon={<TrendingIcon />}
              label="Learning Your Preferences"
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
            <Chip
              icon={<StarIcon />}
              label="High Accuracy"
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
          </Box>
        </CardContent>
      </AICard>

      {/* Matches Grid */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress size={60} />
        </Box>
      ) : (
        <>
          <Typography variant="h6" fontWeight="600" gutterBottom>
            Your AI Matches ({matches.length})
          </Typography>
          
          <Grid container spacing={3}>
            {matches.map((match) => (
              <Grid item xs={12} md={6} lg={4} key={match.id}>
                <MatchCard>
                  <CardContent sx={{ p: 3 }}>
                    {/* Profile Header */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        src={match.photo}
                        sx={{ width: 60, height: 60, mr: 2 }}
                      />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="h6" fontWeight="600">
                          {match.name}, {match.age}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {match.location}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <ScoreChip
                            label={`${match.compatibility}% Match`}
                            size="small"
                            score={match.compatibility}
                          />
                          {match.isOnline && (
                            <Chip
                              label="Online"
                              size="small"
                              sx={{
                                ml: 1,
                                backgroundColor: '#4CAF50',
                                color: 'white',
                                fontSize: '0.7rem'
                              }}
                            />
                          )}
                        </Box>
                      </Box>
                    </Box>

                    {/* Professional Info */}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" fontWeight="500">
                        {match.education}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {match.occupation}
                      </Typography>
                    </Box>

                    {/* AI Insights */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" fontWeight="600" gutterBottom>
                        AI Insights:
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {match.matchReason}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                        {match.aiInsights.map((insight, index) => (
                          <Chip
                            key={index}
                            label={insight}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          />
                        ))}
                      </Box>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    {/* Action Buttons */}
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button
                        size="small"
                        startIcon={<ViewIcon />}
                        onClick={() => handleViewProfile(match.id)}
                        sx={{ flex: 1, minWidth: 'auto' }}
                      >
                        View
                      </Button>
                      <Button
                        size="small"
                        startIcon={<HeartIcon />}
                        onClick={() => handleSendInterest(match.id)}
                        color="error"
                        sx={{ flex: 1, minWidth: 'auto' }}
                      >
                        {isPremium ? 'Interest' : '💎'}
                      </Button>
                      <Button
                        size="small"
                        startIcon={<MessageIcon />}
                        onClick={() => handleSendMessage(match.id)}
                        color="primary"
                        sx={{ flex: 1, minWidth: 'auto' }}
                      >
                        {isPremium ? 'Message' : '💎'}
                      </Button>
                      <Button
                        size="small"
                        startIcon={<ContactIcon />}
                        onClick={() => handleContactReveal(match.id)}
                        color="success"
                        sx={{ flex: 1, minWidth: 'auto' }}
                      >
                        {isPremium ? 'Contact' : '💎'}
                      </Button>
                    </Box>
                  </CardContent>
                </MatchCard>
              </Grid>
            ))}
          </Grid>

          {/* Load More Button */}
          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              variant="outlined"
              size="large"
              startIcon={<RefreshIcon />}
              onClick={handleRefreshMatches}
              disabled={refreshing}
            >
              {refreshing ? 'Loading More Matches...' : 'Load More AI Matches'}
            </Button>
          </Box>
        </>
      )}

      {/* Premium Upgrade Alert */}
      {!isPremium && (
        <Alert
          severity="info"
          sx={{ mt: 3 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => onPremiumFeatureClick('upgrade')}
            >
              Upgrade
            </Button>
          }
        >
          Upgrade to Premium to unlock unlimited AI matches, messaging, and contact reveals!
        </Alert>
      )}
    </Box>
  );
}
