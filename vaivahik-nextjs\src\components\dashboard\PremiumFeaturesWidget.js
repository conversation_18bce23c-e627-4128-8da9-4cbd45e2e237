/**
 * Premium Features Widget - Showcases and manages premium subscriptions
 * Integrates with existing subscription and payment systems
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Divider,
  styled,
  CircularProgress
} from '@mui/material';

// Icons
import {
  WorkspacePremium as PremiumIcon,
  Star as StarIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Message as MessageIcon,
  ContactPhone as ContactIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  FlashOn as SpotlightIcon,
  Psychology as AIIcon,
  Diamond as DiamondIcon,
  Crown as CrownIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';

// Styled components
const PremiumCard = styled(Card)(({ theme, featured }) => ({
  borderRadius: 16,
  position: 'relative',
  transition: 'all 0.3s ease',
  border: featured ? '3px solid #FFD700' : '1px solid rgba(255,255,255,0.1)',
  background: featured 
    ? 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 160, 0, 0.1))'
    : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: featured 
      ? '0 20px 60px rgba(255, 215, 0, 0.3)'
      : '0 16px 48px rgba(0,0,0,0.12)'
  },
  ...(featured && {
    '&::before': {
      content: '"MOST POPULAR"',
      position: 'absolute',
      top: -12,
      left: '50%',
      transform: 'translateX(-50%)',
      background: 'linear-gradient(135deg, #FFD700, #FFA000)',
      color: '#000',
      padding: '4px 16px',
      borderRadius: 12,
      fontSize: '0.75rem',
      fontWeight: 700,
      zIndex: 1
    }
  })
}));

const FeatureItem = styled(ListItem)(({ theme }) => ({
  padding: '8px 0',
  '& .MuiListItemIcon-root': {
    minWidth: 32
  }
}));

const CurrentPlanCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white'
}));

// Premium plans data (based on your existing plans)
const premiumPlans = [
  {
    id: 'basic',
    name: 'Basic Premium',
    price: 999,
    duration: 30,
    currency: 'INR',
    featured: false,
    features: [
      'View up to 50 contacts per month',
      'Send unlimited interests',
      'Basic chat functionality',
      'Profile boost once per week',
      'Basic search filters',
      'Email support'
    ],
    restrictions: [
      'Limited contact reveals',
      'Basic matching algorithm',
      'No advanced analytics'
    ]
  },
  {
    id: 'premium',
    name: 'Premium Plus',
    price: 2999,
    duration: 90,
    currency: 'INR',
    featured: true,
    features: [
      'Unlimited contact reveals',
      'Advanced AI matching',
      'Priority chat support',
      'Profile spotlight features',
      'Advanced search filters',
      'Detailed analytics',
      'Biodata templates',
      'WhatsApp integration',
      'Phone support'
    ],
    restrictions: []
  },
  {
    id: 'elite',
    name: 'Elite Matrimony',
    price: 4999,
    duration: 180,
    currency: 'INR',
    featured: false,
    features: [
      'Everything in Premium Plus',
      'Dedicated relationship manager',
      'Priority profile verification',
      'Exclusive elite matches',
      'Video call features',
      'Background verification',
      'Personalized matchmaking',
      '24/7 premium support'
    ],
    restrictions: []
  }
];

export default function PremiumFeaturesWidget({ userId, currentPlan, onUpgrade }) {
  const [plans, setPlans] = useState(premiumPlans);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [upgradeDialog, setUpgradeDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState(null);

  useEffect(() => {
    fetchCurrentSubscription();
  }, []);

  const fetchCurrentSubscription = async () => {
    try {
      // Simulate API call to fetch current subscription
      setTimeout(() => {
        setCurrentSubscription({
          planId: 'basic',
          planName: 'Basic Premium',
          expiresAt: '2024-02-15',
          daysRemaining: 25,
          autoRenew: true,
          features: ['Contact reveals: 35/50', 'Profile boosts: 2/4', 'Chat messages: Unlimited']
        });
      }, 1000);
    } catch (error) {
      console.error('Error fetching subscription:', error);
    }
  };

  const handleUpgrade = (plan) => {
    setSelectedPlan(plan);
    setUpgradeDialog(true);
  };

  const confirmUpgrade = async () => {
    setLoading(true);
    try {
      // Integrate with your Razorpay payment system
      const paymentData = {
        planId: selectedPlan.id,
        amount: selectedPlan.price,
        currency: selectedPlan.currency,
        userId: userId
      };

      // This would call your payment API
      console.log('Processing payment:', paymentData);
      
      // Simulate payment processing
      setTimeout(() => {
        setLoading(false);
        setUpgradeDialog(false);
        onUpgrade?.(selectedPlan);
        
        // Update current subscription
        setCurrentSubscription({
          planId: selectedPlan.id,
          planName: selectedPlan.name,
          expiresAt: new Date(Date.now() + selectedPlan.duration * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          daysRemaining: selectedPlan.duration,
          autoRenew: true,
          features: selectedPlan.features.slice(0, 3).map(f => f + ': Active')
        });
      }, 2000);
    } catch (error) {
      console.error('Error processing upgrade:', error);
      setLoading(false);
    }
  };

  const renderCurrentPlan = () => {
    if (!currentSubscription) return null;

    return (
      <CurrentPlanCard sx={{ mb: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <CrownIcon sx={{ fontSize: 40, mr: 2 }} />
            <Box>
              <Typography variant="h5" fontWeight="700">
                Current Plan: {currentSubscription.planName}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                {currentSubscription.daysRemaining} days remaining
              </Typography>
            </Box>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
              Plan Usage:
            </Typography>
            <LinearProgress
              variant="determinate"
              value={(currentSubscription.daysRemaining / 30) * 100}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#4CAF50',
                  borderRadius: 4
                }
              }}
            />
          </Box>

          <Grid container spacing={2}>
            {currentSubscription.features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckIcon sx={{ fontSize: 16, mr: 1 }} />
                  <Typography variant="body2">{feature}</Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </CurrentPlanCard>
    );
  };

  const renderPlanCard = (plan) => (
    <PremiumCard key={plan.id} featured={plan.featured}>
      <CardContent sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Plan Header */}
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <Box sx={{ mb: 2 }}>
            {plan.id === 'elite' ? (
              <DiamondIcon sx={{ fontSize: 48, color: '#9C27B0' }} />
            ) : plan.featured ? (
              <CrownIcon sx={{ fontSize: 48, color: '#FFD700' }} />
            ) : (
              <PremiumIcon sx={{ fontSize: 48, color: '#667eea' }} />
            )}
          </Box>
          <Typography variant="h5" fontWeight="700" gutterBottom>
            {plan.name}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'baseline', justifyContent: 'center', mb: 1 }}>
            <Typography variant="h3" fontWeight="700" color="primary">
              ₹{plan.price.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
              /{plan.duration} days
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            ₹{Math.round(plan.price / plan.duration)} per day
          </Typography>
        </Box>

        {/* Features List */}
        <Box sx={{ flexGrow: 1, mb: 3 }}>
          <Typography variant="subtitle2" fontWeight="600" gutterBottom>
            Features Included:
          </Typography>
          <List dense>
            {plan.features.map((feature, index) => (
              <FeatureItem key={index}>
                <ListItemIcon>
                  <CheckIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                </ListItemIcon>
                <ListItemText
                  primary={feature}
                  primaryTypographyProps={{ variant: 'body2' }}
                />
              </FeatureItem>
            ))}
          </List>

          {plan.restrictions.length > 0 && (
            <>
              <Typography variant="subtitle2" fontWeight="600" gutterBottom sx={{ mt: 2 }}>
                Limitations:
              </Typography>
              <List dense>
                {plan.restrictions.map((restriction, index) => (
                  <FeatureItem key={index}>
                    <ListItemIcon>
                      <CloseIcon sx={{ color: '#F44336', fontSize: 20 }} />
                    </ListItemIcon>
                    <ListItemText
                      primary={restriction}
                      primaryTypographyProps={{ variant: 'body2', color: 'text.secondary' }}
                    />
                  </FeatureItem>
                ))}
              </List>
            </>
          )}
        </Box>

        {/* Action Button */}
        <Button
          fullWidth
          variant="contained"
          size="large"
          onClick={() => handleUpgrade(plan)}
          disabled={currentSubscription?.planId === plan.id}
          sx={{
            background: plan.featured 
              ? 'linear-gradient(135deg, #FFD700, #FFA000)'
              : 'linear-gradient(135deg, #667eea, #764ba2)',
            color: plan.featured ? '#000' : 'white',
            fontWeight: 600,
            py: 1.5,
            '&:hover': {
              background: plan.featured
                ? 'linear-gradient(135deg, #FFA000, #FF8F00)'
                : 'linear-gradient(135deg, #5a67d8, #6b46c1)'
            }
          }}
        >
          {currentSubscription?.planId === plan.id ? 'Current Plan' : 'Upgrade Now'}
        </Button>
      </CardContent>
    </PremiumCard>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <PremiumIcon sx={{ fontSize: 32, color: '#FFD700', mr: 2 }} />
        <Typography variant="h5" fontWeight="700">
          Premium Features
        </Typography>
      </Box>

      {/* Current Plan */}
      {renderCurrentPlan()}

      {/* Premium Benefits Overview */}
      <Card sx={{ mb: 4, borderRadius: 2 }}>
        <CardContent sx={{ p: 4 }}>
          <Typography variant="h6" fontWeight="600" gutterBottom>
            Why Choose Premium?
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <AIIcon sx={{ fontSize: 48, color: '#667eea', mb: 2 }} />
                <Typography variant="subtitle1" fontWeight="600">
                  AI-Powered Matching
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Advanced algorithms find your perfect match
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <SecurityIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 2 }} />
                <Typography variant="subtitle1" fontWeight="600">
                  Secure Contact Reveal
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Safe and verified contact information
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <AnalyticsIcon sx={{ fontSize: 48, color: '#FF9800', mb: 2 }} />
                <Typography variant="subtitle1" fontWeight="600">
                  Detailed Analytics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Track your profile performance
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Premium Plans */}
      <Typography variant="h6" fontWeight="600" gutterBottom>
        Choose Your Premium Plan
      </Typography>
      <Grid container spacing={3}>
        {plans.map((plan) => (
          <Grid item xs={12} md={4} key={plan.id}>
            {renderPlanCard(plan)}
          </Grid>
        ))}
      </Grid>

      {/* Upgrade Dialog */}
      <Dialog open={upgradeDialog} onClose={() => setUpgradeDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PaymentIcon sx={{ mr: 2, color: '#4CAF50' }} />
            Upgrade to {selectedPlan?.name}
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedPlan && (
            <Box>
              <Alert severity="info" sx={{ mb: 3 }}>
                You're upgrading to <strong>{selectedPlan.name}</strong> for ₹{selectedPlan.price.toLocaleString()} 
                valid for {selectedPlan.duration} days.
              </Alert>

              <Typography variant="h6" fontWeight="600" gutterBottom>
                Payment Summary:
              </Typography>
              <Box sx={{ p: 2, backgroundColor: '#f5f5f5', borderRadius: 2, mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Plan:</Typography>
                  <Typography fontWeight="600">{selectedPlan.name}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Duration:</Typography>
                  <Typography>{selectedPlan.duration} days</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Amount:</Typography>
                  <Typography fontWeight="600">₹{selectedPlan.price.toLocaleString()}</Typography>
                </Box>
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6">Total:</Typography>
                  <Typography variant="h6" fontWeight="700">₹{selectedPlan.price.toLocaleString()}</Typography>
                </Box>
              </Box>

              <Typography variant="body2" color="text.secondary">
                Payment will be processed securely through Razorpay. You will receive a confirmation email after successful payment.
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpgradeDialog(false)} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={confirmUpgrade}
            variant="contained"
            disabled={loading}
            sx={{
              background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
              minWidth: 120
            }}
          >
            {loading ? <CircularProgress size={20} color="inherit" /> : 'Pay Now'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
