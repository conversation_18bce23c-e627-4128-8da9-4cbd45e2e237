/**
 * Feature Flags Utility
 * 
 * This utility provides functions to manage feature flags and toggle between
 * real and mock data sources. It syncs the feature flag state between
 * localStorage and the application state.
 */

import { FEATURE_FLAGS } from '@/config';

// Initialize feature flags from localStorage or defaults
const initializeFeatureFlags = () => {
  if (typeof window === 'undefined') {
    return { ...FEATURE_FLAGS };
  }

  const localFlags = {};
  
  // Load flags from localStorage if available
  try {
    // Check for useRealBackend flag in localStorage
    const useRealBackend = localStorage.getItem('useRealBackend');
    if (useRealBackend !== null) {
      localFlags.useRealBackend = useRealBackend === 'true';
    }
    
    // Check for other feature flags in localStorage
    Object.keys(FEATURE_FLAGS).forEach(key => {
      const value = localStorage.getItem(`featureFlag_${key}`);
      if (value !== null) {
        // Convert string to appropriate type
        if (value === 'true' || value === 'false') {
          localFlags[key] = value === 'true';
        } else if (!isNaN(value)) {
          localFlags[key] = Number(value);
        } else {
          localFlags[key] = value;
        }
      }
    });
  } catch (error) {
    console.error('Error loading feature flags from localStorage:', error);
  }
  
  // Merge with defaults
  return { ...FEATURE_FLAGS, ...localFlags };
};

// Current feature flags state
let currentFlags = initializeFeatureFlags();

/**
 * Get the current value of a feature flag
 * @param {string} flagName - The name of the feature flag
 * @returns {any} The value of the feature flag
 */
export const getFeatureFlag = (flagName) => {
  return currentFlags[flagName];
};

/**
 * Get all feature flags
 * @returns {Object} All feature flags
 */
export const getAllFeatureFlags = () => {
  return { ...currentFlags };
};

/**
 * Set a feature flag value
 * @param {string} flagName - The name of the feature flag
 * @param {any} value - The value to set
 * @returns {boolean} Success status
 */
export const setFeatureFlag = (flagName, value) => {
  if (!(flagName in currentFlags)) {
    console.warn(`Feature flag "${flagName}" does not exist`);
    return false;
  }
  
  try {
    // Update in-memory state
    currentFlags = {
      ...currentFlags,
      [flagName]: value
    };
    
    // Persist to localStorage if available
    if (typeof window !== 'undefined') {
      localStorage.setItem(`featureFlag_${flagName}`, value.toString());
      
      // Special case for useRealBackend for backward compatibility
      if (flagName === 'useRealBackend') {
        localStorage.setItem('useRealBackend', value.toString());
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Error setting feature flag "${flagName}":`, error);
    return false;
  }
};

/**
 * Toggle a boolean feature flag
 * @param {string} flagName - The name of the feature flag
 * @returns {boolean} The new value of the feature flag
 */
export const toggleFeatureFlag = (flagName) => {
  const currentValue = getFeatureFlag(flagName);
  
  if (typeof currentValue !== 'boolean') {
    console.warn(`Feature flag "${flagName}" is not a boolean and cannot be toggled`);
    return currentValue;
  }
  
  const newValue = !currentValue;
  setFeatureFlag(flagName, newValue);
  return newValue;
};

/**
 * Toggle between real and mock backend
 * @returns {boolean} The new value of useRealBackend
 */
export const toggleBackendMode = () => {
  return toggleFeatureFlag('useRealBackend');
};

/**
 * Check if using real backend
 * @returns {boolean} True if using real backend
 */
export const isUsingRealBackend = () => {
  return getFeatureFlag('useRealBackend');
};

/**
 * Reset all feature flags to their default values
 */
export const resetFeatureFlags = () => {
  if (typeof window === 'undefined') {
    return;
  }
  
  try {
    // Clear all feature flags from localStorage
    Object.keys(FEATURE_FLAGS).forEach(key => {
      localStorage.removeItem(`featureFlag_${key}`);
    });
    
    // Also clear the legacy key
    localStorage.removeItem('useRealBackend');
    
    // Reset in-memory state
    currentFlags = { ...FEATURE_FLAGS };
  } catch (error) {
    console.error('Error resetting feature flags:', error);
  }
};

/**
 * Get appropriate API endpoint based on current backend mode
 * @param {string} endpoint - The API endpoint
 * @param {boolean} isMock - Whether to force mock endpoint
 * @returns {string} The full API endpoint URL
 */
export const getApiEndpoint = (endpoint, isMock = false) => {
  const useReal = !isMock && isUsingRealBackend();
  
  if (useReal) {
    return `${process.env.NEXT_PUBLIC_BACKEND_API_URL || '/api'}${endpoint}`;
  } else {
    return `/api${endpoint}`;
  }
};

// Export a backend connector object for backward compatibility
export const backendConnector = {
  isUsingRealBackend,
  toggleBackendMode,
  getApiEndpoint
};

export default {
  getFeatureFlag,
  getAllFeatureFlags,
  setFeatureFlag,
  toggleFeatureFlag,
  toggleBackendMode,
  isUsingRealBackend,
  resetFeatureFlags,
  getApiEndpoint,
  backendConnector
};
