/**
 * Dashboard API Service - Integrates with existing backend functionality
 * Provides unified API calls for the professional dashboard
 */

import axios from 'axios';
import { API_BASE_URL } from '@/config/apiConfig';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('userToken') || sessionStorage.getItem('userToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('userToken');
      sessionStorage.removeItem('userToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Dashboard API Service
export const dashboardApiService = {
  // User Dashboard Data
  async getDashboardStats(userId) {
    try {
      const response = await apiClient.get(`/user/dashboard/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  },

  // AI Matching Service (leverages your ML service)
  async getAIMatches(userId, params = {}) {
    try {
      const response = await apiClient.get(`/matches/ai/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching AI matches:', error);
      throw error;
    }
  },

  // Advanced Search (leverages admin search functionality)
  async searchProfiles(searchParams) {
    try {
      const response = await apiClient.post('/search/advanced', searchParams);
      return response.data;
    } catch (error) {
      console.error('Error searching profiles:', error);
      throw error;
    }
  },

  // Contact Reveal (with security checks)
  async revealContact(userId, targetUserId, reason) {
    try {
      const response = await apiClient.post('/contact/reveal', {
        userId,
        targetUserId,
        reason,
        timestamp: new Date().toISOString()
      });
      return response.data;
    } catch (error) {
      console.error('Error revealing contact:', error);
      throw error;
    }
  },

  // Get contacted users list
  async getContactedUsers(userId) {
    try {
      const response = await apiClient.get(`/contact/history/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching contacted users:', error);
      throw error;
    }
  },

  // Security check for contact reveal
  async performSecurityCheck(userId, targetUserId) {
    try {
      const response = await apiClient.post('/contact/security-check', {
        userId,
        targetUserId
      });
      return response.data;
    } catch (error) {
      console.error('Error performing security check:', error);
      throw error;
    }
  },

  // User Analytics
  async getUserAnalytics(userId, timeRange = '7d') {
    try {
      const response = await apiClient.get(`/analytics/user/${userId}`, {
        params: { timeRange }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      throw error;
    }
  },

  // Premium Plans
  async getPremiumPlans() {
    try {
      const response = await apiClient.get('/subscription/plans');
      return response.data;
    } catch (error) {
      console.error('Error fetching premium plans:', error);
      throw error;
    }
  },

  // Current Subscription
  async getCurrentSubscription(userId) {
    try {
      const response = await apiClient.get(`/subscription/current/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching current subscription:', error);
      throw error;
    }
  },

  // Upgrade Subscription (Razorpay integration)
  async upgradeSubscription(userId, planId) {
    try {
      const response = await apiClient.post('/subscription/upgrade', {
        userId,
        planId
      });
      return response.data;
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      throw error;
    }
  },

  // Verification Queue
  async getVerificationStatus(userId) {
    try {
      const response = await apiClient.get(`/verification/status/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching verification status:', error);
      throw error;
    }
  },

  // Submit verification documents
  async submitVerificationDocuments(userId, documents) {
    try {
      const formData = new FormData();
      formData.append('userId', userId);
      
      documents.forEach((doc, index) => {
        formData.append(`document_${index}`, doc.file);
        formData.append(`type_${index}`, doc.type);
      });

      const response = await apiClient.post('/verification/submit', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error submitting verification documents:', error);
      throw error;
    }
  },

  // Biodata Templates
  async getBiodataTemplates(gender) {
    try {
      const response = await apiClient.get(`/biodata/templates`, {
        params: { gender }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching biodata templates:', error);
      throw error;
    }
  },

  // Generate Biodata
  async generateBiodata(userId, templateId, customData) {
    try {
      const response = await apiClient.post('/biodata/generate', {
        userId,
        templateId,
        customData
      });
      return response.data;
    } catch (error) {
      console.error('Error generating biodata:', error);
      throw error;
    }
  },

  // Spotlight Features
  async getSpotlightFeatures(userId) {
    try {
      const response = await apiClient.get(`/spotlight/features/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching spotlight features:', error);
      throw error;
    }
  },

  // Activate Spotlight
  async activateSpotlight(userId, featureType, duration) {
    try {
      const response = await apiClient.post('/spotlight/activate', {
        userId,
        featureType,
        duration
      });
      return response.data;
    } catch (error) {
      console.error('Error activating spotlight:', error);
      throw error;
    }
  },

  // Chat/Messages
  async getMessages(userId, params = {}) {
    try {
      const response = await apiClient.get(`/messages/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  },

  // Send Message
  async sendMessage(fromUserId, toUserId, message) {
    try {
      const response = await apiClient.post('/messages/send', {
        fromUserId,
        toUserId,
        message,
        timestamp: new Date().toISOString()
      });
      return response.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  // Interests
  async sendInterest(fromUserId, toUserId, message = '') {
    try {
      const response = await apiClient.post('/interests/send', {
        fromUserId,
        toUserId,
        message
      });
      return response.data;
    } catch (error) {
      console.error('Error sending interest:', error);
      throw error;
    }
  },

  // Get Interests
  async getInterests(userId, type = 'all') {
    try {
      const response = await apiClient.get(`/interests/${userId}`, {
        params: { type }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching interests:', error);
      throw error;
    }
  },

  // Profile Views
  async getProfileViews(userId, params = {}) {
    try {
      const response = await apiClient.get(`/profile/views/${userId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching profile views:', error);
      throw error;
    }
  },

  // Update User Preferences
  async updatePreferences(userId, preferences) {
    try {
      const response = await apiClient.put(`/user/preferences/${userId}`, preferences);
      return response.data;
    } catch (error) {
      console.error('Error updating preferences:', error);
      throw error;
    }
  },

  // Privacy Settings
  async updatePrivacySettings(userId, settings) {
    try {
      const response = await apiClient.put(`/user/privacy/${userId}`, settings);
      return response.data;
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      throw error;
    }
  }
};

export default dashboardApiService;
