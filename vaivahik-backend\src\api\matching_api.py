"""
Flask API for the matrimony matching service
"""

import os
import json
import time
from flask import Flask, request, jsonify
from flask_cors import CORS
import sys

# Add the services directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'services'))

# Import the matching model
from two_tower_model_pytorch import MatrimonyMatchingModel

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize the model
model = None

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'success': True,
        'message': 'ML Matching Service is running',
        'model_initialized': model is not None
    })

def initialize_model():
    """Initialize the model"""
    global model

    # Check if model is already initialized
    if model is not None:
        return

    # Initialize model
    model = MatrimonyMatchingModel()

    # Check if a saved model exists
    model_path = os.path.join('models', 'matrimony_model')
    if os.path.exists(model_path):
        try:
            # Load the saved model
            model.load(model_path)
            print(f"Loaded model from {model_path}")
        except Exception as e:
            print(f"Error loading model: {str(e)}")
            # Build a new model
            model.build_model()
            print("Built new model")
    else:
        # Build a new model
        model.build_model()
        print("Built new model")

@app.route('/api/match', methods=['POST'])
def match():
    """
    Match a user with potential matches

    Request body:
    {
        "user": {
            "id": "user123",
            "profile": { ... user profile data ... }
        },
        "preferences": { ... user preferences ... },
        "potentialMatches": [
            {
                "id": "match1",
                "profile": { ... match profile data ... }
            },
            ...
        ]
    }

    Response:
    {
        "success": true,
        "matches": [
            {
                "userId": "match1",
                "score": 0.85
            },
            ...
        ]
    }
    """
    # Initialize model if not already initialized
    initialize_model()

    try:
        # Get request data
        data = request.json

        # Validate request
        if not data or 'user' not in data or 'potentialMatches' not in data:
            return jsonify({
                'success': False,
                'message': 'Invalid request data'
            }), 400

        # Extract data
        user = data['user']
        preferences = data.get('preferences', {})
        potential_matches = data['potentialMatches']

        # Get predictions
        print(f"Number of potential matches: {len(potential_matches)}")
        match_profiles = [m['profile'] for m in potential_matches]
        print(f"Number of match profiles: {len(match_profiles)}")

        scores = model.predict(
            [user['profile']],
            [preferences],
            match_profiles
        )

        print(f"Number of scores: {len(scores)}")
        print(f"Scores: {scores}")

        # Create response
        matches = []
        for i, score in enumerate(scores):
            # Convert score to percentage (0-100)
            percentage_score = int(round(score * 100))

            matches.append({
                'userId': potential_matches[i]['id'],
                'score': percentage_score
            })

        print(f"Number of matches: {len(matches)}")

        # Sort matches by score (descending)
        matches.sort(key=lambda x: x['score'], reverse=True)

        print(f"Matches after sorting: {matches}")

        return jsonify({
            'success': True,
            'matches': matches
        })

    except Exception as e:
        print(f"Error in match API: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'success': False,
            'message': f'Error processing request: {str(e)}'
        }), 500

@app.route('/api/train', methods=['POST'])
def train():
    """
    Train the model with labeled data

    Request body:
    {
        "trainingData": [
            {
                "user": { ... user profile data ... },
                "preferences": { ... user preferences ... },
                "match": { ... match profile data ... },
                "label": 1.0,  // 1.0 for match, 0.0 for non-match
                "feedback": {
                    "type": "LIKE" | "DISLIKE" | "CONTACT" | "IGNORE" | "REPORT",
                    "timestamp": "2023-06-15T10:30:00Z",
                    "reason": "string (optional)"
                }
            },
            ...
        ],
        "modelType": "TWO_TOWER",
        "modelConfig": {
            "learningRate": 0.001,
            "batchSize": 64,
            "epochs": 10,
            "embeddingSize": 128,
            "similarityMetric": "cosine"
        }
    }

    Response:
    {
        "success": true,
        "message": "Model trained successfully",
        "metrics": {
            "trainLoss": 0.2,
            "valLoss": 0.3,
            "trainAcc": 0.9,
            "valAcc": 0.85
        },
        "modelId": "string"
    }
    """
    # Initialize model if not already initialized
    initialize_model()

    try:
        # Get request data
        data = request.json

        # Validate request
        if not data or 'trainingData' not in data:
            return jsonify({
                'success': False,
                'message': 'Invalid request data'
            }), 400

        # Extract training data
        training_data = data['trainingData']

        if len(training_data) < 10:
            return jsonify({
                'success': False,
                'message': 'Not enough training data (minimum 10 samples required)'
            }), 400

        # Extract model configuration
        model_type = data.get('modelType', 'TWO_TOWER')
        model_config = data.get('modelConfig', {})

        # Update model configuration if provided
        if model_config:
            # Create a new configuration dictionary
            config = {
                'user_tower_layers': model_config.get('userTowerLayers', [128, 64]),
                'match_tower_layers': model_config.get('matchTowerLayers', [128, 64]),
                'embedding_size': model_config.get('embeddingSize', 128),
                'dropout_rate': model_config.get('dropoutRate', 0.2),
                'learning_rate': model_config.get('learningRate', 0.001),
                'similarity_metric': model_config.get('similarityMetric', 'cosine'),
                'batch_size': model_config.get('batchSize', 64),
                'epochs': model_config.get('epochs', 10)
            }

            # Update model configuration
            model.config = config

        # Process training data
        # This includes extracting user feedback to adjust feature weights
        processed_data = []
        for item in training_data:
            # Extract basic data
            user = item['user']
            preferences = item.get('preferences', {})
            match = item['match']
            label = item['label']

            # Extract feedback if available
            feedback = item.get('feedback', {})
            feedback_type = feedback.get('type', '')
            feedback_reason = feedback.get('reason', '')

            # Adjust label based on feedback
            if feedback_type == 'LIKE' or feedback_type == 'CONTACT':
                # Increase label for positive feedback
                label = min(1.0, label * 1.2)
            elif feedback_type == 'DISLIKE' or feedback_type == 'REPORT':
                # Decrease label for negative feedback
                label = max(0.0, label * 0.5)

            # Add to processed data
            processed_data.append({
                'user': user,
                'preferences': preferences,
                'match': match,
                'label': label,
                'feedback': feedback
            })

        # Prepare data for training
        users = [item['user'] for item in processed_data]
        preferences = [item['preferences'] for item in processed_data]
        matches = [item['match'] for item in processed_data]
        labels = [item['label'] for item in processed_data]

        # Train model
        history = model.train(users, preferences, matches, labels)

        # Save model
        os.makedirs('models', exist_ok=True)
        model_id = f"matrimony_model_{int(time.time())}"
        model.save(f"models/{model_id}")

        # Get final metrics
        metrics = {
            'trainLoss': history['train_loss'][-1],
            'valLoss': history['val_loss'][-1],
            'trainAcc': history['train_acc'][-1],
            'valAcc': history['val_acc'][-1]
        }

        return jsonify({
            'success': True,
            'message': 'Model trained successfully',
            'metrics': metrics,
            'modelId': model_id
        })

    except Exception as e:
        print(f"Error in train API: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'success': False,
            'message': f'Error training model: {str(e)}'
        }), 500

@app.route('/api/ab-test', methods=['POST'])
def ab_test():
    """
    A/B test different scoring algorithms

    Request body:
    {
        "user": {
            "id": "user123",
            "profile": { ... user profile data ... }
        },
        "preferences": { ... user preferences ... },
        "potentialMatches": [
            {
                "id": "match1",
                "profile": { ... match profile data ... }
            },
            ...
        ],
        "algorithms": [
            {
                "id": "algorithm1",
                "type": "TWO_TOWER",
                "config": {
                    "similarityMetric": "cosine",
                    "weights": {
                        "age": 0.1,
                        "religion": 0.2,
                        ...
                    }
                }
            },
            {
                "id": "algorithm2",
                "type": "TWO_TOWER",
                "config": {
                    "similarityMetric": "dot",
                    "weights": {
                        "age": 0.15,
                        "religion": 0.25,
                        ...
                    }
                }
            }
        ]
    }

    Response:
    {
        "success": true,
        "results": [
            {
                "algorithmId": "algorithm1",
                "matches": [
                    {
                        "userId": "match1",
                        "score": 0.85
                    },
                    ...
                ]
            },
            {
                "algorithmId": "algorithm2",
                "matches": [
                    {
                        "userId": "match1",
                        "score": 0.78
                    },
                    ...
                ]
            }
        ]
    }
    """
    # Initialize model if not already initialized
    initialize_model()

    try:
        # Get request data
        data = request.json

        # Validate request
        if not data or 'user' not in data or 'potentialMatches' not in data or 'algorithms' not in data:
            return jsonify({
                'success': False,
                'message': 'Invalid request data'
            }), 400

        # Extract data
        user = data['user']
        preferences = data.get('preferences', {})
        potential_matches = data['potentialMatches']
        algorithms = data['algorithms']

        # Run each algorithm
        results = []
        for algorithm in algorithms:
            algorithm_id = algorithm.get('id', 'unknown')
            algorithm_type = algorithm.get('type', 'TWO_TOWER')
            algorithm_config = algorithm.get('config', {})

            # Create a temporary model with the algorithm configuration
            temp_model = MatrimonyMatchingModel(algorithm_config)
            temp_model.build_model()

            # Get predictions
            scores = temp_model.predict(
                [user['profile']],
                [preferences],
                [m['profile'] for m in potential_matches]
            )

            # Create matches
            matches = []
            for i, score in enumerate(scores):
                # Convert score to percentage (0-100)
                percentage_score = int(round(score * 100))

                matches.append({
                    'userId': potential_matches[i]['id'],
                    'score': percentage_score
                })

            # Sort matches by score (descending)
            matches.sort(key=lambda x: x['score'], reverse=True)

            # Add to results
            results.append({
                'algorithmId': algorithm_id,
                'matches': matches
            })

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        print(f"Error in A/B test API: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'success': False,
            'message': f'Error processing request: {str(e)}'
        }), 500

@app.route('/api/match-analysis', methods=['POST'])
def match_analysis():
    """
    Provide detailed analysis of a match score

    Request body:
    {
        "user": {
            "id": "user123",
            "profile": { ... user profile data ... }
        },
        "preferences": { ... user preferences ... },
        "match": {
            "id": "match1",
            "profile": { ... match profile data ... }
        }
    }

    Response:
    {
        "success": true,
        "matchId": "match1",
        "overallScore": 85,
        "factors": [
            {
                "name": "Age",
                "score": 90,
                "description": "The age difference is within your preferred range.",
                "weight": 10
            },
            {
                "name": "Height",
                "score": 100,
                "description": "The height difference is ideal (you are 10cm taller).",
                "weight": 12
            },
            ...
        ],
        "compatibility": {
            "excellent": ["Height", "Religion", "Caste", "SubCaste"],
            "good": ["Education", "Location", "Income"],
            "average": ["Age"],
            "poor": []
        },
        "suggestions": [
            "You both share similar family values which is a strong foundation for a relationship.",
            "You have complementary career paths which often leads to successful partnerships."
        ]
    }
    """
    # Initialize model if not already initialized
    initialize_model()

    try:
        # Get request data
        data = request.json

        # Validate request
        if not data or 'user' not in data or 'match' not in data:
            return jsonify({
                'success': False,
                'message': 'Invalid request data'
            }), 400

        # Extract data
        user = data['user']
        preferences = data.get('preferences', {})
        match = data['match']

        # Get detailed analysis
        analysis = model.get_match_analysis(
            user['profile'],
            preferences,
            match['profile']
        )

        # Convert scores to percentages
        overall_score = int(round(analysis['overallScore'] * 100))

        factors = []
        for factor in analysis['factors']:
            factor_score = int(round(factor['score'] * 100))
            factors.append({
                'name': factor['name'],
                'score': factor_score,
                'description': factor['description'],
                'weight': int(round(factor['weight'] * 100))
            })

        # Categorize factors by score
        compatibility = {
            'excellent': [],
            'good': [],
            'average': [],
            'poor': []
        }

        for factor in factors:
            if factor['score'] >= 90:
                compatibility['excellent'].append(factor['name'])
            elif factor['score'] >= 75:
                compatibility['good'].append(factor['name'])
            elif factor['score'] >= 50:
                compatibility['average'].append(factor['name'])
            else:
                compatibility['poor'].append(factor['name'])

        # Create response
        response = {
            'success': True,
            'matchId': match['id'],
            'overallScore': overall_score,
            'factors': factors,
            'compatibility': compatibility,
            'suggestions': analysis['suggestions']
        }

        return jsonify(response)

    except Exception as e:
        print(f"Error in match analysis API: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'success': False,
            'message': f'Error processing request: {str(e)}'
        }), 500



if __name__ == '__main__':
    # Initialize model
    initialize_model()

    # Run the app
    # Use debug=False for production, debug=True for development
    import os
    debug_mode = os.getenv('NODE_ENV', 'development') != 'production'
    app.run(host='0.0.0.0', port=5000, debug=debug_mode)
