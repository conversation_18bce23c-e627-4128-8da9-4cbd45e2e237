# Directory Structure Policy

## Overview

This project uses a dual directory structure for Next.js pages:
- `/pages` - Contains all page components that are directly accessible via routes
- `/src/pages` - Contains the original page components (kept for backward compatibility)

## Rules

1. **New Pages**: All new pages should be added to the `/pages` directory.

2. **Existing Pages**: Do not modify pages in `/src/pages` unless absolutely necessary. If you need to modify a page, create a new version in `/pages` and update any imports.

3. **Imports**: When importing pages, always import from `/pages` rather than `/src/pages`.

4. **API Routes**: All API routes should be in `/pages/api`.

5. **Components**: All components should be in `/src/components`.

## Directory Structure

```
vaivahik-nextjs/
├── pages/                  # Primary pages directory (use this for new pages)
│   ├── admin/              # Admin pages
│   ├── api/                # API routes
│   └── ...                 # Other pages
├── src/
│   ├── pages/              # Legacy pages directory (do not add new pages here)
│   ├── components/         # React components
│   ├── styles/             # CSS styles
│   ├── utils/              # Utility functions
│   └── ...                 # Other source files
└── ...
```

## Rationale

This dual structure is maintained to ensure backward compatibility while transitioning to a more standard Next.js project structure. Eventually, we plan to consolidate all pages into the `/pages` directory.

## Validation

A pre-commit hook will validate that new pages are added to the correct directory. See `validate-directory-structure.js` for details.
