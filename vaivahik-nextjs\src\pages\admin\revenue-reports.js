import { useState, useEffect, useRef } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import { toast } from 'react-toastify';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Tabs,
  Tab
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import DownloadIcon from '@mui/icons-material/Download';
import RefreshIcon from '@mui/icons-material/Refresh';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import InfoIcon from '@mui/icons-material/Info';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import { adminGet } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

export default function RevenueReports() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [revenueData, setRevenueData] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [filters, setFilters] = useState({
    period: 'monthly',
    startDate: subMonths(new Date(), 12),
    endDate: new Date()
  });
  const [exportLoading, setExportLoading] = useState(false);
  const lineChartRef = useRef(null);
  const barChartRef = useRef(null);
  const pieChartRef = useRef(null);

  useEffect(() => {
    fetchRevenueData();
  }, []);

  const fetchRevenueData = async () => {
    setLoading(true);
    setError(null);
    try {
      // Use the admin API service which handles mock/real data toggle
      const data = await adminGet(`${ADMIN_ENDPOINTS.FINANCIAL}/reports/revenue`, {
        period: filters.period,
        startDate: format(filters.startDate, 'yyyy-MM-dd'),
        endDate: format(filters.endDate, 'yyyy-MM-dd')
      });

      if (data.success) {
        setRevenueData(data.revenue);
      } else {
        throw new Error(data.message || 'Failed to fetch revenue data');
      }
    } catch (err) {
      console.error('Error fetching revenue data:', err);
      setError('Failed to load revenue data. Please try again.');
      toast.error('Error fetching revenue data: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const applyFilters = () => {
    fetchRevenueData();
  };

  const resetFilters = () => {
    setFilters({
      period: 'monthly',
      startDate: subMonths(new Date(), 12),
      endDate: new Date()
    });
  };

  const exportRevenueReport = async () => {
    setExportLoading(true);
    try {
      // Build query parameters
      const queryParams = new URLSearchParams({
        period: filters.period,
        startDate: format(filters.startDate, 'yyyy-MM-dd'),
        endDate: format(filters.endDate, 'yyyy-MM-dd')
      }).toString();

      // Open in a new tab
      window.open(`/api/admin/financial/reports/revenue/export/csv?${queryParams}`, '_blank');
    } catch (err) {
      console.error('Error exporting revenue report:', err);
      setError('Failed to export revenue report. Please try again.');
      toast.error('Error exporting revenue report: ' + err.message);
    } finally {
      setExportLoading(false);
    }
  };

  const exportChartAsImage = (chartRef, fileName) => {
    if (chartRef.current) {
      const link = document.createElement('a');
      link.download = `${fileName}-${format(new Date(), 'yyyy-MM-dd')}.png`;
      link.href = chartRef.current.toBase64Image();
      link.click();
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value.toFixed(2)}%`;
  };

  // Prepare chart data
  const prepareRevenueChartData = () => {
    if (!revenueData || !revenueData.byPeriod || revenueData.byPeriod.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            label: 'Revenue',
            data: [],
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            fill: true,
            tension: 0.4
          }
        ]
      };
    }

    return {
      labels: revenueData.byPeriod.map(item => item.period),
      datasets: [
        {
          label: 'Revenue',
          data: revenueData.byPeriod.map(item => item.revenue),
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          fill: true,
          tension: 0.4
        }
      ]
    };
  };

  const prepareProductTypeChartData = () => {
    if (!revenueData || !revenueData.byProductType || revenueData.byProductType.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            label: 'Revenue by Product Type',
            data: [],
            backgroundColor: [
              'rgba(255, 99, 132, 0.7)',
              'rgba(54, 162, 235, 0.7)',
              'rgba(255, 206, 86, 0.7)',
              'rgba(75, 192, 192, 0.7)',
              'rgba(153, 102, 255, 0.7)',
              'rgba(255, 159, 64, 0.7)'
            ],
            borderColor: [
              'rgba(255, 99, 132, 1)',
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)',
              'rgba(75, 192, 192, 1)',
              'rgba(153, 102, 255, 1)',
              'rgba(255, 159, 64, 1)'
            ],
            borderWidth: 1
          }
        ]
      };
    }

    return {
      labels: revenueData.byProductType.map(item => item.productType),
      datasets: [
        {
          label: 'Revenue by Product Type',
          data: revenueData.byProductType.map(item => item.revenue),
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  const preparePaymentMethodChartData = () => {
    if (!revenueData || !revenueData.byPaymentMethod || revenueData.byPaymentMethod.length === 0) {
      return {
        labels: [],
        datasets: [
          {
            label: 'Revenue by Payment Method',
            data: [],
            backgroundColor: [
              'rgba(54, 162, 235, 0.7)',
              'rgba(255, 206, 86, 0.7)',
              'rgba(75, 192, 192, 0.7)',
              'rgba(153, 102, 255, 0.7)',
              'rgba(255, 159, 64, 0.7)'
            ],
            borderColor: [
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)',
              'rgba(75, 192, 192, 1)',
              'rgba(153, 102, 255, 1)',
              'rgba(255, 159, 64, 1)'
            ],
            borderWidth: 1
          }
        ]
      };
    }

    return {
      labels: revenueData.byPaymentMethod.map(item => item.paymentMethod),
      datasets: [
        {
          label: 'Revenue by Payment Method',
          data: revenueData.byPaymentMethod.map(item => item.revenue),
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Chart options
  const lineChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Revenue Trend',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return '₹' + value.toLocaleString('en-IN');
          }
        }
      },
    },
  };

  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Revenue by Product Type',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return '₹' + value.toLocaleString('en-IN');
          }
        }
      },
    },
  };

  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right',
      },
      title: {
        display: true,
        text: 'Revenue by Payment Method',
      },
    },
  };

  return (
    <EnhancedAdminLayout title="Revenue Reports">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">Revenue Reports</Typography>
          <Stack direction="row" spacing={2}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchRevenueData}
              disabled={loading}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={<DownloadIcon />}
              onClick={exportRevenueReport}
              disabled={loading || exportLoading}
            >
              Export Report
            </Button>
          </Stack>
        </Box>

        {/* Filters */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <FilterAltIcon sx={{ mr: 1 }} />
            <Typography variant="h6">Filters</Typography>
          </Box>

          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Period</InputLabel>
                  <Select
                    value={filters.period}
                    label="Period"
                    onChange={(e) => handleFilterChange('period', e.target.value)}
                  >
                    <MenuItem value="daily">Daily</MenuItem>
                    <MenuItem value="weekly">Weekly</MenuItem>
                    <MenuItem value="monthly">Monthly</MenuItem>
                    <MenuItem value="yearly">Yearly</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Start Date"
                  value={filters.startDate}
                  onChange={(newValue) => handleFilterChange('startDate', newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="End Date"
                  value={filters.endDate}
                  onChange={(newValue) => handleFilterChange('endDate', newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Stack direction="row" spacing={1} sx={{ height: '100%' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={applyFilters}
                    fullWidth
                    sx={{ height: '100%' }}
                  >
                    Apply Filters
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={resetFilters}
                    sx={{ height: '100%' }}
                  >
                    Reset
                  </Button>
                </Stack>
              </Grid>
            </Grid>
          </LocalizationProvider>
        </Paper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        ) : revenueData ? (
          <>
            {/* Summary Cards */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                      Total Revenue
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(revenueData.total)}
                    </Typography>
                    {revenueData.growthMetrics && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        {revenueData.growthMetrics.revenueGrowth >= 0 ? (
                          <TrendingUpIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                        ) : (
                          <TrendingDownIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                        )}
                        <Typography
                          variant="body2"
                          color={revenueData.growthMetrics.revenueGrowth >= 0 ? 'success.main' : 'error.main'}
                        >
                          {formatPercentage(revenueData.growthMetrics.revenueGrowth)}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                      Total Transactions
                    </Typography>
                    <Typography variant="h4">
                      {revenueData.totalTransactions.toLocaleString()}
                    </Typography>
                    {revenueData.growthMetrics && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        {revenueData.growthMetrics.transactionGrowth >= 0 ? (
                          <TrendingUpIcon color="success" fontSize="small" sx={{ mr: 0.5 }} />
                        ) : (
                          <TrendingDownIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                        )}
                        <Typography
                          variant="body2"
                          color={revenueData.growthMetrics.transactionGrowth >= 0 ? 'success.main' : 'error.main'}
                        >
                          {formatPercentage(revenueData.growthMetrics.transactionGrowth)}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                      Average Transaction
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(revenueData.averageTransaction)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                      Conversion Rate
                    </Typography>
                    <Typography variant="h4">
                      {revenueData.conversionRate ? `${revenueData.conversionRate.toFixed(2)}%` : 'N/A'}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Tabs for different views */}
            <Box sx={{ mb: 3 }}>
              <Tabs value={tabValue} onChange={handleTabChange} centered>
                <Tab label="Charts" />
                <Tab label="Detailed Data" />
              </Tabs>
            </Box>

            {/* Charts View */}
            {tabValue === 0 && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ height: '100%' }}>
                    <CardHeader
                      title="Revenue Trend"
                      action={
                        <IconButton onClick={() => exportChartAsImage(lineChartRef, 'revenue-trend')}>
                          <DownloadIcon />
                        </IconButton>
                      }
                    />
                    <Divider />
                    <CardContent>
                      <Box sx={{ height: 300, position: 'relative' }}>
                        <Line
                          ref={lineChartRef}
                          data={prepareRevenueChartData()}
                          options={lineChartOptions}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Card sx={{ height: '100%' }}>
                    <CardHeader
                      title="Revenue by Payment Method"
                      action={
                        <IconButton onClick={() => exportChartAsImage(pieChartRef, 'revenue-by-payment')}>
                          <DownloadIcon />
                        </IconButton>
                      }
                    />
                    <Divider />
                    <CardContent>
                      <Box sx={{ height: 300, position: 'relative' }}>
                        <Pie
                          ref={pieChartRef}
                          data={preparePaymentMethodChartData()}
                          options={pieChartOptions}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12}>
                  <Card>
                    <CardHeader
                      title="Revenue by Product Type"
                      action={
                        <IconButton onClick={() => exportChartAsImage(barChartRef, 'revenue-by-product')}>
                          <DownloadIcon />
                        </IconButton>
                      }
                    />
                    <Divider />
                    <CardContent>
                      <Box sx={{ height: 300, position: 'relative' }}>
                        <Bar
                          ref={barChartRef}
                          data={prepareProductTypeChartData()}
                          options={barChartOptions}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}

            {/* Detailed Data View */}
            {tabValue === 1 && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Card>
                    <CardHeader title="Revenue by Period" />
                    <Divider />
                    <CardContent>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Period</TableCell>
                              <TableCell align="right">Revenue</TableCell>
                              <TableCell align="right">Transactions</TableCell>
                              <TableCell align="right">Avg. Transaction</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {revenueData.byPeriod && revenueData.byPeriod.map((item, index) => (
                              <TableRow key={index}>
                                <TableCell>{item.period}</TableCell>
                                <TableCell align="right">{formatCurrency(item.revenue)}</TableCell>
                                <TableCell align="right">{item.count}</TableCell>
                                <TableCell align="right">
                                  {formatCurrency(item.count > 0 ? item.revenue / item.count : 0)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Revenue by Product Type" />
                    <Divider />
                    <CardContent>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Product Type</TableCell>
                              <TableCell align="right">Revenue</TableCell>
                              <TableCell align="right">Transactions</TableCell>
                              <TableCell align="right">% of Total</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {revenueData.byProductType && revenueData.byProductType.map((item, index) => (
                              <TableRow key={index}>
                                <TableCell>{item.productType}</TableCell>
                                <TableCell align="right">{formatCurrency(item.revenue)}</TableCell>
                                <TableCell align="right">{item.count}</TableCell>
                                <TableCell align="right">
                                  {formatPercentage((item.revenue / revenueData.total) * 100)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardHeader title="Revenue by Payment Method" />
                    <Divider />
                    <CardContent>
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Payment Method</TableCell>
                              <TableCell align="right">Revenue</TableCell>
                              <TableCell align="right">Transactions</TableCell>
                              <TableCell align="right">% of Total</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {revenueData.byPaymentMethod && revenueData.byPaymentMethod.map((item, index) => (
                              <TableRow key={index}>
                                <TableCell>{item.paymentMethod}</TableCell>
                                <TableCell align="right">{formatCurrency(item.revenue)}</TableCell>
                                <TableCell align="right">{item.count}</TableCell>
                                <TableCell align="right">
                                  {formatPercentage((item.revenue / revenueData.total) * 100)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
          </>
        ) : (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h6" color="textSecondary" gutterBottom>
              No revenue data available
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              There is no revenue data available for the selected period.
            </Typography>
            <Button variant="contained" onClick={fetchRevenueData}>
              Try Again
            </Button>
          </Box>
        )}
      </Box>
    </EnhancedAdminLayout>
  );
}
