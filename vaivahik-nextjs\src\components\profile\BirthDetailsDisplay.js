import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Divider, 
  Chip,
  Tooltip,
  IconButton
} from '@mui/material';
import { format, parseISO } from 'date-fns';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import CakeIcon from '@mui/icons-material/Cake';

/**
 * Component to display birth details in a user-friendly way
 * Used in profile view and user details pages
 */
const BirthDetailsDisplay = ({ 
  dateOfBirth, 
  birthTime, 
  birthPlace,
  showHoroscopeInfo = true,
  compact = false
}) => {
  // Format date of birth
  const formattedDate = dateOfBirth ? 
    format(typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : dateOfBirth, 'MMMM d, yyyy') : 
    'Not provided';
  
  // Format birth time
  const formattedTime = birthTime ? 
    format(
      typeof birthTime === 'string' ? 
        parseISO(`2000-01-01T${birthTime}`) : 
        birthTime, 
      'h:mm a'
    ) : 
    'Not provided';
  
  // Calculate age
  const calculateAge = () => {
    if (!dateOfBirth) return null;
    
    const birthDate = typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : dateOfBirth;
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };
  
  const age = calculateAge();
  
  // Determine zodiac sign based on date of birth
  const getZodiacSign = () => {
    if (!dateOfBirth) return null;
    
    const birthDate = typeof dateOfBirth === 'string' ? parseISO(dateOfBirth) : dateOfBirth;
    const month = birthDate.getMonth() + 1; // JavaScript months are 0-indexed
    const day = birthDate.getDate();
    
    // Define zodiac sign date ranges
    if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'Aries';
    if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'Taurus';
    if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'Gemini';
    if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'Cancer';
    if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'Leo';
    if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'Virgo';
    if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'Libra';
    if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'Scorpio';
    if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'Sagittarius';
    if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'Capricorn';
    if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'Aquarius';
    return 'Pisces';
  };
  
  const zodiacSign = getZodiacSign();
  
  // Compact view for cards and lists
  if (compact) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CakeIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="body2">
            {age ? `${age} years` : 'Age not available'} 
            {zodiacSign && showHoroscopeInfo && (
              <Chip 
                label={zodiacSign} 
                size="small" 
                sx={{ ml: 1, height: 20, fontSize: '0.7rem' }} 
              />
            )}
          </Typography>
        </Box>
        {birthPlace && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LocationOnIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="body2" noWrap>
              {birthPlace}
            </Typography>
          </Box>
        )}
      </Box>
    );
  }
  
  // Full detailed view
  return (
    <Paper elevation={0} sx={{ p: 3, borderRadius: 2, mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Birth Details</Typography>
        {showHoroscopeInfo && (
          <Tooltip title="These details are used for horoscope matching">
            <IconButton size="small">
              <HelpOutlineIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      <Divider sx={{ mb: 3 }} />
      
      <Grid container spacing={3}>
        {/* Date of Birth & Age */}
        <Grid item xs={12} md={4}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CalendarTodayIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="subtitle2">Date of Birth</Typography>
            </Box>
            <Typography variant="body1">{formattedDate}</Typography>
            {age && (
              <Typography variant="body2" color="text.secondary">
                {age} years old
              </Typography>
            )}
          </Box>
        </Grid>
        
        {/* Birth Time */}
        <Grid item xs={12} md={4}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AccessTimeIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="subtitle2">Birth Time</Typography>
            </Box>
            <Typography variant="body1">{formattedTime}</Typography>
          </Box>
        </Grid>
        
        {/* Birth Place */}
        <Grid item xs={12} md={4}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <LocationOnIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="subtitle2">Birth Place</Typography>
            </Box>
            <Typography variant="body1">{birthPlace || 'Not provided'}</Typography>
          </Box>
        </Grid>
        
        {/* Zodiac Sign - Only show if horoscope info is enabled */}
        {showHoroscopeInfo && zodiacSign && (
          <Grid item xs={12}>
            <Box sx={{ mt: 2 }}>
              <Divider sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Typography variant="subtitle2" sx={{ mr: 1 }}>Zodiac Sign:</Typography>
                <Chip 
                  label={zodiacSign} 
                  color="primary" 
                  variant="outlined"
                />
              </Box>
            </Box>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};

export default BirthDetailsDisplay;
