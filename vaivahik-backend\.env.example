# Vaivahik Backend Environment Variables

# Server Configuration
PORT=8080
NODE_ENV=production

# Frontend URL (for CORS)
FRONTEND_URL=https://vaivahik.com

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/vaivahik?schema=public

# JWT Authentication
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRY=1h
REFRESH_TOKEN_SECRET=your_refresh_token_secret_here
REFRESH_TOKEN_EXPIRY=7d
ADMIN_JWT_SECRET=your_admin_jwt_secret_here

# Redis Configuration
REDIS_URL=redis://localhost:6379

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880 # 5MB

# Logging
LOG_LEVEL=info
LOG_DIR=logs

# Feature Flags
USE_REAL_DATA=true
ENABLE_REDIS_CACHE=true
ENABLE_RATE_LIMITING=true

# Email Configuration (if needed)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
EMAIL_FROM=<EMAIL>

# Payment Gateway (if needed)
PAYMENT_GATEWAY_API_KEY=your_payment_gateway_api_key
PAYMENT_GATEWAY_SECRET=your_payment_gateway_secret

# SMS Gateway (MSG91)
MSG91_API_KEY=your_msg91_api_key
MSG91_SENDER_ID=your_msg91_sender_id
MSG91_DLT_TEMPLATE_ID=your_msg91_dlt_template_id
MSG91_DLT_PE_ID=your_msg91_dlt_pe_id
MSG91_OTP_TEMPLATE=##OTP## is the OTP to verify your mobile number -Maratha Wedding

# AI Matching Service
AI_SERVICE_URL=http://localhost:5000
AI_SERVICE_API_KEY=your_ai_service_api_key

# Security
ENABLE_HELMET=true
ENABLE_RATE_LIMIT=true
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Error Monitoring (Sentry)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_DASHBOARD_URL=https://sentry.io/organizations/your-org/issues/
