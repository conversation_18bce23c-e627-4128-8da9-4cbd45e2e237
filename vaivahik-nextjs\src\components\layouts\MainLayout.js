import React from 'react';
import Head from 'next/head';
import Link from 'next/link';

const MainLayout = ({ children, title = 'Vaivahik' }) => {
  return (
    <>
      <Head>
        <title>{title} - Vaivahik</title>
        <meta name="description" content="Vaivahik - Matrimony for Maratha Community" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="main-container">
        <header className="main-header">
          <div className="container">
            <div className="header-content">
              <Link href="/" className="logo">
                Vaivahik
              </Link>
              <nav className="main-nav">
                <Link href="/profiles" className="nav-link">
                  Browse Profiles
                </Link>
                <Link href="/messages" className="nav-link">
                  Messages
                </Link>
                <Link href="/account" className="nav-link">
                  My Account
                </Link>
              </nav>
            </div>
          </div>
        </header>

        <main className="main-content">
          {children}
        </main>

        <footer className="main-footer">
          <div className="container">
            <div className="footer-content">
              <p>&copy; {new Date().getFullYear()} Vaivahik. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
};

export default MainLayout;
