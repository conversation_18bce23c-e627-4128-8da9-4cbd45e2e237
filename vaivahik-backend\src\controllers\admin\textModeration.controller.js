// src/controllers/admin/textModeration.controller.js

const textModerationService = require('../../services/textModeration.service');

/**
 * @description Get text moderation settings
 * @route GET /api/admin/moderation/text/settings
 */
exports.getModerationSettings = async (req, res, next) => {
    try {
        const settings = textModerationService.getModerationSettings();
        
        res.status(200).json({
            success: true,
            settings
        });
    } catch (error) {
        console.error('Error getting text moderation settings:', error);
        next(error);
    }
};

/**
 * @description Update text moderation settings
 * @route PUT /api/admin/moderation/text/settings
 */
exports.updateModerationSettings = async (req, res, next) => {
    try {
        const { settings } = req.body;
        
        if (!settings) {
            return res.status(400).json({
                success: false,
                message: 'Settings object is required'
            });
        }
        
        const updatedSettings = await textModerationService.updateModerationSettings(settings);
        
        res.status(200).json({
            success: true,
            message: 'Text moderation settings updated successfully',
            settings: updatedSettings
        });
    } catch (error) {
        console.error('Error updating text moderation settings:', error);
        next(error);
    }
};

/**
 * @description Get banned words list
 * @route GET /api/admin/moderation/text/banned-words
 */
exports.getBannedWords = async (req, res, next) => {
    try {
        const bannedWords = textModerationService.getBannedWords();
        
        res.status(200).json({
            success: true,
            bannedWords
        });
    } catch (error) {
        console.error('Error getting banned words:', error);
        next(error);
    }
};

/**
 * @description Update banned words list
 * @route PUT /api/admin/moderation/text/banned-words
 */
exports.updateBannedWords = async (req, res, next) => {
    try {
        const { words } = req.body;
        
        if (!words || !Array.isArray(words)) {
            return res.status(400).json({
                success: false,
                message: 'Words array is required'
            });
        }
        
        const updatedWords = await textModerationService.updateBannedWords(words);
        
        res.status(200).json({
            success: true,
            message: 'Banned words list updated successfully',
            bannedWords: updatedWords
        });
    } catch (error) {
        console.error('Error updating banned words:', error);
        next(error);
    }
};

/**
 * @description Get moderation logs
 * @route GET /api/admin/moderation/text/logs
 */
exports.getModerationLogs = async (req, res, next) => {
    try {
        const prisma = req.prisma;
        const { page = 1, limit = 20, contentType = 'MESSAGE' } = req.query;
        
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const take = parseInt(limit);
        
        // Get logs
        const logs = await prisma.moderationLog.findMany({
            where: {
                contentType
            },
            orderBy: {
                createdAt: 'desc'
            },
            skip,
            take,
            include: {
                photo: true
            }
        });
        
        // Count total logs
        const totalLogs = await prisma.moderationLog.count({
            where: {
                contentType
            }
        });
        
        res.status(200).json({
            success: true,
            logs,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalLogs / take),
                totalItems: totalLogs,
                itemsPerPage: take
            }
        });
    } catch (error) {
        console.error('Error getting moderation logs:', error);
        next(error);
    }
};

/**
 * @description Get flagged messages
 * @route GET /api/admin/moderation/text/flagged-messages
 */
exports.getFlaggedMessages = async (req, res, next) => {
    try {
        const prisma = req.prisma;
        const { page = 1, limit = 20, status } = req.query;
        
        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const take = parseInt(limit);
        
        // Build where clause
        const where = {
            isModerated: true
        };
        
        if (status) {
            where.moderationStatus = status;
        }
        
        // Get flagged messages
        const messages = await prisma.message.findMany({
            where,
            orderBy: {
                sentAt: 'desc'
            },
            skip,
            take,
            include: {
                sender: {
                    select: {
                        id: true,
                        phone: true,
                        email: true,
                        profile: {
                            select: {
                                fullName: true
                            }
                        }
                    }
                },
                receiver: {
                    select: {
                        id: true,
                        phone: true,
                        email: true,
                        profile: {
                            select: {
                                fullName: true
                            }
                        }
                    }
                },
                conversation: true
            }
        });
        
        // Count total flagged messages
        const totalMessages = await prisma.message.count({
            where
        });
        
        res.status(200).json({
            success: true,
            messages,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(totalMessages / take),
                totalItems: totalMessages,
                itemsPerPage: take
            }
        });
    } catch (error) {
        console.error('Error getting flagged messages:', error);
        next(error);
    }
};

/**
 * @description Review a flagged message
 * @route PUT /api/admin/moderation/text/review/:messageId
 */
exports.reviewMessage = async (req, res, next) => {
    try {
        const prisma = req.prisma;
        const { messageId } = req.params;
        const { decision, adminNotes } = req.body;
        const adminId = req.admin?.id;
        
        if (!decision || !['APPROVED', 'REJECTED'].includes(decision)) {
            return res.status(400).json({
                success: false,
                message: 'Valid decision (APPROVED or REJECTED) is required'
            });
        }
        
        // Get the message
        const message = await prisma.message.findUnique({
            where: { id: messageId }
        });
        
        if (!message) {
            return res.status(404).json({
                success: false,
                message: 'Message not found'
            });
        }
        
        // Update the message
        const updatedMessage = await prisma.message.update({
            where: { id: messageId },
            data: {
                moderationStatus: decision
            }
        });
        
        // Create a moderation log entry
        await prisma.moderationLog.create({
            data: {
                contentType: 'MESSAGE',
                contentId: messageId,
                userId: message.senderId,
                decision,
                flags: message.moderationFlags,
                reviewedBy: adminId,
                reviewedAt: new Date(),
                adminNotes
            }
        });
        
        res.status(200).json({
            success: true,
            message: `Message ${decision.toLowerCase()} successfully`,
            updatedMessage
        });
    } catch (error) {
        console.error('Error reviewing message:', error);
        next(error);
    }
};

/**
 * @description Get moderation statistics
 * @route GET /api/admin/moderation/text/stats
 */
exports.getModerationStats = async (req, res, next) => {
    try {
        const prisma = req.prisma;
        const { days = 7 } = req.query;
        
        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - parseInt(days));
        
        // Get total messages
        const totalMessages = await prisma.message.count();
        
        // Get moderated messages
        const moderatedMessages = await prisma.message.count({
            where: {
                isModerated: true
            }
        });
        
        // Get rejected messages
        const rejectedMessages = await prisma.message.count({
            where: {
                moderationStatus: 'REJECTED'
            }
        });
        
        // Get messages by flag type
        const profanityMessages = await prisma.message.count({
            where: {
                moderationFlags: {
                    contains: 'profanity'
                }
            }
        });
        
        const contactInfoMessages = await prisma.message.count({
            where: {
                moderationFlags: {
                    contains: 'contact_info'
                }
            }
        });
        
        const spamMessages = await prisma.message.count({
            where: {
                moderationFlags: {
                    contains: 'spam'
                }
            }
        });
        
        // Get recent moderation activity
        const recentActivity = await prisma.moderationLog.findMany({
            where: {
                contentType: 'MESSAGE',
                createdAt: {
                    gte: startDate,
                    lte: endDate
                }
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: 10
        });
        
        res.status(200).json({
            success: true,
            stats: {
                totalMessages,
                moderatedMessages,
                rejectedMessages,
                moderationRate: totalMessages > 0 ? (moderatedMessages / totalMessages) * 100 : 0,
                rejectionRate: moderatedMessages > 0 ? (rejectedMessages / moderatedMessages) * 100 : 0,
                flagBreakdown: {
                    profanity: profanityMessages,
                    contactInfo: contactInfoMessages,
                    spam: spamMessages
                }
            },
            recentActivity
        });
    } catch (error) {
        console.error('Error getting moderation stats:', error);
        next(error);
    }
};

/**
 * @description Test text moderation
 * @route POST /api/admin/moderation/text/test
 */
exports.testModeration = async (req, res, next) => {
    try {
        const { text } = req.body;
        
        if (!text) {
            return res.status(400).json({
                success: false,
                message: 'Text is required'
            });
        }
        
        // Moderate the text
        const result = await textModerationService.moderateText(text, {
            skipLogging: true // Don't log test moderation
        });
        
        res.status(200).json({
            success: true,
            result
        });
    } catch (error) {
        console.error('Error testing text moderation:', error);
        next(error);
    }
};
