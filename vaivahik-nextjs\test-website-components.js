/**
 * Test script for website components
 * 
 * This script helps verify that the website components are properly moved and working.
 * Run this script to check if the components can be imported and rendered correctly.
 */

const fs = require('fs');
const path = require('path');

// Define the components to test
const componentsToTest = [
  {
    name: 'FilterChips',
    path: './src/website/components/common/FilterChips.js',
    importPath: '@/website/components/common/FilterChips'
  },
  {
    name: 'InterestsDisplay',
    path: './src/website/components/profile/InterestsDisplay.js',
    importPath: '@/website/components/profile/InterestsDisplay'
  },
  {
    name: 'LifestyleForm',
    path: './src/website/components/profile/LifestyleForm.js',
    importPath: '@/website/components/profile/LifestyleForm'
  },
  {
    name: 'PartnerPreferencesForm',
    path: './src/website/components/profile/PartnerPreferencesForm.js',
    importPath: '@/website/components/profile/PartnerPreferencesForm'
  }
];

// Define the pages to test
const pagesToTest = [
  {
    name: 'InterestsDisplayExample',
    path: './src/website/pages/examples/interests-display.js',
    url: '/website/pages/examples/interests-display'
  },
  {
    name: 'EditLifestyle',
    path: './src/website/pages/profile/edit/lifestyle.js',
    url: '/website/pages/profile/edit/lifestyle'
  },
  {
    name: 'EditPartnerPreferences',
    path: './src/website/pages/profile/edit/preferences.js',
    url: '/website/pages/profile/edit/preferences'
  },
  {
    name: 'ProfilePage',
    path: './src/website/pages/profile/index.js',
    url: '/website/pages/profile'
  }
];

// Check if components exist
console.log('Checking components...');
componentsToTest.forEach(component => {
  if (fs.existsSync(path.resolve(component.path))) {
    console.log(`✅ ${component.name} exists at ${component.path}`);
  } else {
    console.log(`❌ ${component.name} does not exist at ${component.path}`);
  }
});

// Check if pages exist
console.log('\nChecking pages...');
pagesToTest.forEach(page => {
  if (fs.existsSync(path.resolve(page.path))) {
    console.log(`✅ ${page.name} exists at ${page.path}`);
  } else {
    console.log(`❌ ${page.name} does not exist at ${page.path}`);
  }
});

// Generate test page
console.log('\nGenerating test page...');
const testPageContent = `import { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Check as CheckIcon,
  Link as LinkIcon
} from '@mui/icons-material';

export default function TestWebsiteComponents() {
  return (
    <>
      <Head>
        <title>Test Website Components | Vaivahik</title>
        <meta name="description" content="Test page for website components" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" gutterBottom>
          Test Website Components
        </Typography>
        <Typography variant="body1" paragraph>
          This page helps verify that the website components are properly moved and working.
          Click on the links below to test each component.
        </Typography>
        
        <Divider sx={{ my: 3 }} />
        
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Components
              </Typography>
              <List>
                ${componentsToTest.map(component => `
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="${component.name}" 
                    secondary="${component.importPath}" 
                  />
                </ListItem>`).join('')}
              </List>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Pages
              </Typography>
              <List>
                ${pagesToTest.map(page => `
                <ListItem>
                  <ListItemIcon>
                    <LinkIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText 
                    primary={<Link href="${page.url}" style={{ textDecoration: 'none' }}>${page.name}</Link>} 
                    secondary="${page.url}" 
                  />
                </ListItem>`).join('')}
              </List>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </>
  );
}`;

fs.writeFileSync(path.resolve('./vaivahik-nextjs/src/pages/test-website-components.js'), testPageContent);
console.log('✅ Test page generated at ./vaivahik-nextjs/src/pages/test-website-components.js');

console.log('\nTo test the components:');
console.log('1. Start the Next.js development server: npm run dev');
console.log('2. Open http://localhost:3000/test-website-components in your browser');
console.log('3. Click on the links to test each component');
