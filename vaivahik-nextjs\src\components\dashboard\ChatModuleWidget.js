import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Avatar,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  Badge,
  IconButton,
  TextField,
  InputAdornment,
  styled
} from '@mui/material';
import {
  Chat as ChatIcon,
  Send as SendIcon,
  Search as SearchIcon,
  Phone as CallIcon,
  VideoCall as VideoCallIcon,
  MoreVert as MoreIcon,
  WorkspacePremium as PremiumIcon,
  Star as StarIcon,
  Circle as OnlineIcon
} from '@mui/icons-material';

const ChatCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  borderRadius: 24,
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FF5F6D, #FFC371)',
    borderRadius: '24px 24px 0 0'
  }
}));

const ConversationItem = styled(ListItem)(({ theme, isActive }) => ({
  borderRadius: 12,
  marginBottom: 8,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  backgroundColor: isActive ? 'rgba(255, 95, 109, 0.1)' : 'transparent',
  '&:hover': {
    backgroundColor: 'rgba(255, 95, 109, 0.05)',
    transform: 'translateX(4px)'
  }
}));

const PremiumFeatureCard = styled(Card)(({ theme }) => ({
  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 160, 0, 0.1) 100%)',
  border: '2px solid rgba(255, 215, 0, 0.3)',
  borderRadius: 16,
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 12px 24px rgba(255, 215, 0, 0.2)'
  }
}));

const ChatModuleWidget = ({ userId, isPremium = false, onPremiumFeatureClick }) => {
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [newMessage, setNewMessage] = useState('');

  useEffect(() => {
    fetchConversations();
  }, [userId]);

  const fetchConversations = async () => {
    try {
      // Mock conversations data
      const mockConversations = [
        {
          id: 1,
          user: {
            id: 101,
            name: 'Priya Sharma',
            avatar: '/api/placeholder/50/50',
            isOnline: true,
            isPremium: true
          },
          lastMessage: {
            text: 'Hi! I found your profile interesting. Would love to connect.',
            timestamp: '2024-01-15T10:30:00Z',
            isRead: false,
            senderId: 101
          },
          unreadCount: 2,
          compatibility: 94
        },
        {
          id: 2,
          user: {
            id: 102,
            name: 'Anita Patil',
            avatar: '/api/placeholder/50/50',
            isOnline: false,
            isPremium: false
          },
          lastMessage: {
            text: 'Thank you for showing interest in my profile.',
            timestamp: '2024-01-14T15:45:00Z',
            isRead: true,
            senderId: 102
          },
          unreadCount: 0,
          compatibility: 89
        }
      ];
      
      setConversations(mockConversations);
      if (mockConversations.length > 0) {
        setActiveConversation(mockConversations[0]);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
    }
  };

  const handleSendMessage = () => {
    if (!isPremium) {
      onPremiumFeatureClick('unlimited-messages');
      return;
    }
    
    if (newMessage.trim()) {
      console.log('Sending message:', newMessage);
      setNewMessage('');
    }
  };

  const handleCall = (type) => {
    if (!isPremium) {
      onPremiumFeatureClick('calling');
      return;
    }
    console.log('Initiating call:', type);
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <ChatIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            💬 Messages & Chat
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Connect with your matches and start meaningful conversations
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Conversations List */}
        <Grid item xs={12} md={4}>
          <ChatCard>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="600" gutterBottom>
                Recent Conversations
              </Typography>

              <List>
                {conversations.map((conversation) => (
                  <ConversationItem
                    key={conversation.id}
                    isActive={activeConversation?.id === conversation.id}
                    onClick={() => setActiveConversation(conversation)}
                  >
                    <ListItemAvatar>
                      <Badge
                        overlap="circular"
                        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                        badgeContent={
                          conversation.user.isOnline ? (
                            <OnlineIcon sx={{ color: '#4CAF50', fontSize: 12 }} />
                          ) : null
                        }
                      >
                        <Avatar src={conversation.user.avatar} />
                      </Badge>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2" fontWeight="600">
                            {conversation.user.name}
                          </Typography>
                          {conversation.user.isPremium && (
                            <PremiumIcon sx={{ color: '#FFD700', fontSize: 16 }} />
                          )}
                        </Box>
                      }
                      secondary={conversation.lastMessage.text}
                    />
                    {conversation.unreadCount > 0 && (
                      <Chip
                        label={conversation.unreadCount}
                        size="small"
                        sx={{
                          backgroundColor: '#FF5F6D',
                          color: 'white',
                          fontSize: '0.75rem'
                        }}
                      />
                    )}
                  </ConversationItem>
                ))}
              </List>
            </CardContent>
          </ChatCard>
        </Grid>

        {/* Chat Interface */}
        <Grid item xs={12} md={8}>
          {activeConversation ? (
            <ChatCard sx={{ height: 500 }}>
              <CardContent sx={{ p: 0, height: '100%', display: 'flex', flexDirection: 'column' }}>
                {/* Chat Header */}
                <Box sx={{ 
                  p: 3, 
                  borderBottom: '1px solid rgba(0,0,0,0.1)',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar src={activeConversation.user.avatar} sx={{ mr: 2 }} />
                    <Box>
                      <Typography variant="h6" fontWeight="600">
                        {activeConversation.user.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {activeConversation.user.isOnline ? 'Online' : 'Last seen recently'}
                      </Typography>
                    </Box>
                  </Box>
                  <Box>
                    <IconButton onClick={() => handleCall('voice')}>
                      <CallIcon />
                    </IconButton>
                    <IconButton onClick={() => handleCall('video')}>
                      <VideoCallIcon />
                    </IconButton>
                  </Box>
                </Box>

                {/* Messages Area */}
                <Box sx={{ flex: 1, p: 3, overflow: 'auto' }}>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Start a conversation with {activeConversation.user.name}
                  </Typography>
                </Box>

                {/* Message Input */}
                <Box sx={{ p: 3, borderTop: '1px solid rgba(0,0,0,0.1)' }}>
                  {!isPremium ? (
                    <PremiumFeatureCard onClick={() => onPremiumFeatureClick('unlimited-messages')}>
                      <CardContent sx={{ p: 2, textAlign: 'center' }}>
                        <PremiumIcon sx={{ color: '#FFD700', fontSize: 24, mb: 1 }} />
                        <Typography variant="body2" fontWeight="600">
                          Upgrade to Premium for unlimited messaging
                        </Typography>
                      </CardContent>
                    </PremiumFeatureCard>
                  ) : (
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <TextField
                        fullWidth
                        placeholder="Type your message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        sx={{ borderRadius: 3 }}
                      />
                      <Button
                        variant="contained"
                        onClick={handleSendMessage}
                        sx={{
                          background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                          borderRadius: 3,
                          minWidth: 60
                        }}
                      >
                        <SendIcon />
                      </Button>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </ChatCard>
          ) : (
            <ChatCard sx={{ height: 500 }}>
              <CardContent sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                height: '100%'
              }}>
                <Box sx={{ textAlign: 'center' }}>
                  <ChatIcon sx={{ fontSize: 64, color: '#FF5F6D', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    Select a conversation to start chatting
                  </Typography>
                </Box>
              </CardContent>
            </ChatCard>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default ChatModuleWidget;
