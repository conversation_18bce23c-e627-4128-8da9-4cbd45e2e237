import { useState, useEffect } from 'react';
import { mockDataUtils } from '@/config/apiConfig';
import { Box, Switch, FormControlLabel, Tooltip, Typography } from '@mui/material';
import { Info as InfoIcon } from '@mui/icons-material';

/**
 * A component for toggling between mock and real data
 * This is useful during development to test the application with mock data
 * before the real backend is ready.
 */
const MockDataToggle = () => {
  const [useMockData, setUseMockData] = useState(true);
  
  // Initialize state from localStorage on component mount
  useEffect(() => {
    setUseMockData(mockDataUtils.isMockDataEnabled());
  }, []);
  
  const handleToggle = () => {
    mockDataUtils.toggleMockData();
    // The page will reload, so no need to update state
  };
  
  return (
    <Box sx={{ 
      display: 'flex', 
      alignItems: 'center', 
      bgcolor: 'background.paper', 
      p: 1, 
      borderRadius: 1,
      boxShadow: 1,
      position: 'fixed',
      bottom: 16,
      right: 16,
      zIndex: 1000
    }}>
      <FormControlLabel
        control={
          <Switch
            checked={useMockData}
            onChange={handleToggle}
            color="primary"
          />
        }
        label={
          <Typography variant="body2" sx={{ mr: 1 }}>
            {useMockData ? 'Using Mock Data' : 'Using Real API'}
          </Typography>
        }
      />
      <Tooltip title="Toggle between mock data and real API. The page will reload after toggling.">
        <InfoIcon fontSize="small" color="action" />
      </Tooltip>
    </Box>
  );
};

export default MockDataToggle;
