/**
 * End-to-end tests for preference configuration
 */

describe('Preference Configuration', () => {
  beforeEach(() => {
    // Mock the authentication
    cy.intercept('GET', '/api/auth/session', {
      statusCode: 200,
      body: {
        user: {
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'ADMIN'
        },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }
    }).as('getSession');

    // Visit the preference configuration page
    cy.visit('/admin/preference-config');
    cy.wait('@getSession');
  });

  it('should load the preference configuration page', () => {
    // Check if the page title is displayed
    cy.contains('h4', 'Preference Configuration').should('be.visible');
    
    // Check if all tabs are displayed
    cy.contains('Categories').should('be.visible');
    cy.contains('Fields').should('be.visible');
    cy.contains('Importance Settings').should('be.visible');
    cy.contains('Default Values').should('be.visible');
  });

  it('should display categories in the Categories tab', () => {
    // Mock the API response for categories
    cy.intercept('GET', '/api/admin/preference-config*', {
      statusCode: 200,
      body: {
        success: true,
        categories: [
          {
            id: 'cat1',
            name: 'physical_attributes',
            displayName: 'Physical Attributes',
            description: 'Physical characteristics preferences',
            displayOrder: 1,
            icon: 'person',
            isActive: true,
            isRequired: true
          },
          {
            id: 'cat2',
            name: 'education_career',
            displayName: 'Education & Career',
            description: 'Education and career preferences',
            displayOrder: 2,
            icon: 'school',
            isActive: true,
            isRequired: false
          }
        ]
      }
    }).as('getCategories');

    // Reload the page to trigger the API call
    cy.reload();
    cy.wait('@getCategories');

    // Check if categories are displayed
    cy.contains('Physical Attributes').should('be.visible');
    cy.contains('Education & Career').should('be.visible');
  });

  it('should add a new category', () => {
    // Mock the API response for categories
    cy.intercept('GET', '/api/admin/preference-config*', {
      statusCode: 200,
      body: {
        success: true,
        categories: []
      }
    }).as('getCategories');

    // Mock the API response for adding a category
    cy.intercept('PUT', '/api/admin/preference-config', {
      statusCode: 200,
      body: {
        success: true,
        message: 'Categories updated successfully',
        data: [
          {
            id: 'cat1',
            name: 'new_category',
            displayName: 'New Category',
            description: 'New category description',
            displayOrder: 1,
            icon: 'star',
            isActive: true,
            isRequired: false
          }
        ]
      }
    }).as('addCategory');

    // Reload the page to trigger the API call
    cy.reload();
    cy.wait('@getCategories');

    // Click the Add Category button
    cy.contains('button', 'Add Category').click();

    // Fill in the form
    cy.get('input[name="name"]').type('new_category');
    cy.get('input[name="displayName"]').type('New Category');
    cy.get('textarea[name="description"]').type('New category description');
    cy.get('input[name="icon"]').type('star');

    // Save the category
    cy.contains('button', 'Save').click();
    cy.wait('@addCategory');

    // Check if success message is displayed
    cy.contains('Categories updated successfully').should('be.visible');
  });

  it('should switch to Fields tab and display fields', () => {
    // Mock the API response for all data
    cy.intercept('GET', '/api/admin/preference-config*', {
      statusCode: 200,
      body: {
        success: true,
        categories: [
          {
            id: 'cat1',
            name: 'physical_attributes',
            displayName: 'Physical Attributes',
            description: 'Physical characteristics preferences',
            displayOrder: 1,
            icon: 'person',
            isActive: true,
            isRequired: true
          }
        ],
        fields: [
          {
            id: 'field1',
            name: 'age_range',
            displayName: 'Age Range',
            description: 'Preferred age range of partner',
            fieldType: 'RANGE',
            displayOrder: 1,
            isActive: true,
            isRequired: true,
            isSearchable: true,
            isMatchCriteria: true,
            defaultValue: '{"min": 21, "max": 35}',
            validationRules: '{"minValue": 18, "maxValue": 70}',
            minValue: 18,
            maxValue: 70,
            stepValue: 1,
            categoryId: 'cat1'
          }
        ]
      }
    }).as('getData');

    // Reload the page to trigger the API call
    cy.reload();
    cy.wait('@getData');

    // Click the Fields tab
    cy.contains('Fields').click();

    // Expand the category accordion
    cy.contains('Physical Attributes').click();

    // Check if fields are displayed
    cy.contains('Age Range').should('be.visible');
  });

  it('should add a new field', () => {
    // Mock the API response for all data
    cy.intercept('GET', '/api/admin/preference-config*', {
      statusCode: 200,
      body: {
        success: true,
        categories: [
          {
            id: 'cat1',
            name: 'physical_attributes',
            displayName: 'Physical Attributes',
            description: 'Physical characteristics preferences',
            displayOrder: 1,
            icon: 'person',
            isActive: true,
            isRequired: true
          }
        ],
        fields: []
      }
    }).as('getData');

    // Mock the API response for adding a field
    cy.intercept('PUT', '/api/admin/preference-config', {
      statusCode: 200,
      body: {
        success: true,
        message: 'Fields updated successfully',
        data: [
          {
            id: 'field1',
            name: 'new_field',
            displayName: 'New Field',
            description: 'New field description',
            fieldType: 'TEXT',
            displayOrder: 1,
            isActive: true,
            isRequired: false,
            isSearchable: true,
            isMatchCriteria: true,
            defaultValue: '""',
            categoryId: 'cat1'
          }
        ]
      }
    }).as('addField');

    // Reload the page to trigger the API call
    cy.reload();
    cy.wait('@getData');

    // Click the Fields tab
    cy.contains('Fields').click();

    // Expand the category accordion
    cy.contains('Physical Attributes').click();

    // Click the Add Field button
    cy.contains('button', 'Add Field').click();

    // Fill in the form
    cy.get('input[name="name"]').type('new_field');
    cy.get('input[name="displayName"]').type('New Field');
    cy.get('textarea[name="description"]').type('New field description');
    
    // Select field type
    cy.get('[role="button"]').contains('Field Type').click();
    cy.get('[role="option"]').contains('Text').click();
    
    // Select category
    cy.get('[role="button"]').contains('Category').click();
    cy.get('[role="option"]').contains('Physical Attributes').click();

    // Save the field
    cy.contains('button', 'Save').click();
    cy.wait('@addField');

    // Check if success message is displayed
    cy.contains('Fields updated successfully').should('be.visible');
  });

  it('should delete a field', () => {
    // Mock the API response for all data
    cy.intercept('GET', '/api/admin/preference-config*', {
      statusCode: 200,
      body: {
        success: true,
        categories: [
          {
            id: 'cat1',
            name: 'physical_attributes',
            displayName: 'Physical Attributes',
            description: 'Physical characteristics preferences',
            displayOrder: 1,
            icon: 'person',
            isActive: true,
            isRequired: true
          }
        ],
        fields: [
          {
            id: 'field1',
            name: 'age_range',
            displayName: 'Age Range',
            description: 'Preferred age range of partner',
            fieldType: 'RANGE',
            displayOrder: 1,
            isActive: true,
            isRequired: true,
            isSearchable: true,
            isMatchCriteria: true,
            defaultValue: '{"min": 21, "max": 35}',
            validationRules: '{"minValue": 18, "maxValue": 70}',
            minValue: 18,
            maxValue: 70,
            stepValue: 1,
            categoryId: 'cat1'
          }
        ]
      }
    }).as('getData');

    // Mock the API response for deleting a field
    cy.intercept('DELETE', '/api/admin/preference-config?type=field&id=field1', {
      statusCode: 200,
      body: {
        success: true,
        message: 'Field deleted successfully'
      }
    }).as('deleteField');

    // Reload the page to trigger the API call
    cy.reload();
    cy.wait('@getData');

    // Click the Fields tab
    cy.contains('Fields').click();

    // Expand the category accordion
    cy.contains('Physical Attributes').click();

    // Click the delete button for the field
    cy.get('button[aria-label="delete"]').click();

    // Confirm deletion
    cy.contains('button', 'Delete').click();
    cy.wait('@deleteField');

    // Check if success message is displayed
    cy.contains('Field deleted successfully').should('be.visible');
  });
});
