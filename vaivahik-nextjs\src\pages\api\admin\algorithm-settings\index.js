// API endpoint for algorithm settings
import { withAuth } from '@/utils/authHandler';
import { handleApiError } from '@/utils/errorHandler';
import axios from 'axios';

// Backend API URL - adjust this to your actual backend URL
const BACKEND_API_URL = 'http://localhost:3001/api';

// Main handler function
async function handler(req, res) {
  // Handle different HTTP methods
  try {
    switch (req.method) {
      case 'GET':
        return await getAlgorithmSettings(req, res);
      case 'PUT':
        return await updateAlgorithmSettings(req, res);
      case 'POST':
        return await createAlgorithmModel(req, res);
      default:
        return res.status(405).json({ success: false, message: 'Method not allowed' });
    }
  } catch (error) {
    return handleApiError(error, res, 'Algorithm settings API');
  }
}

// Export the handler with authentication middleware
export default with<PERSON>uth(handler, 'ADMIN');

// GET /api/admin/algorithm-settings
async function getAlgorithmSettings(req, res) {
  try {
    // Construct the API URL with query parameters
    let apiUrl = `${BACKEND_API_URL}/admin/algorithm-settings`;

    // Add query parameters if they exist
    const queryParams = [];
    if (req.query.includeABTestResults === 'true') {
      queryParams.push('includeABTestResults=true');
    }
    if (req.query.includeMetrics === 'true') {
      queryParams.push('includeMetrics=true');
    }

    if (queryParams.length > 0) {
      apiUrl += '?' + queryParams.join('&');
    }

    try {
      // Fetch data from the backend API
      const response = await axios.get(apiUrl);

      // Return the response directly from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error fetching algorithm settings from backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch algorithm settings from backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Get algorithm settings');
  }
}

// PUT /api/admin/algorithm-settings
async function updateAlgorithmSettings(req, res) {
  try {
    const { settings, modelId } = req.body;

    // Validate the request
    if (!settings && !modelId) {
      return res.status(400).json({
        success: false,
        message: 'No settings or model provided'
      });
    }

    try {
      // Send the update to the backend API
      const response = await axios.put(`${BACKEND_API_URL}/admin/algorithm-settings`, {
        settings,
        modelId
      });

      // Return the response from the backend
      return res.status(200).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error updating algorithm settings via backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to update algorithm settings via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Update algorithm settings');
  }
}

// POST /api/admin/algorithm-settings
async function createAlgorithmModel(req, res) {
  try {
    const { model } = req.body;

    // Validate the request
    if (!model) {
      return res.status(400).json({
        success: false,
        message: 'No model data provided'
      });
    }

    // Validate required fields
    if (!model.name || !model.version || !model.type) {
      return res.status(400).json({
        success: false,
        message: 'Name, version, and type are required'
      });
    }

    try {
      // Send the create request to the backend API
      const response = await axios.post(`${BACKEND_API_URL}/admin/algorithm-settings`, {
        model
      });

      // Return the response from the backend
      return res.status(201).json(response.data);
    } catch (apiError) {
      // Log the error
      console.error('Error creating algorithm model via backend API:', apiError.message);

      // Return a meaningful error message
      return res.status(500).json({
        success: false,
        message: 'Failed to create algorithm model via backend API.',
        error: apiError.message,
        details: apiError.response?.data || 'No additional details available',
        note: "Please ensure the backend server is running and accessible."
      });
    }
  } catch (error) {
    return handleApiError(error, res, 'Create algorithm model');
  }
}
