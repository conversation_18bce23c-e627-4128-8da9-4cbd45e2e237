import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { adminGet, adminPost, adminDelete } from '@/services/apiService';
import { ADMIN_ENDPOINTS } from '@/config/apiConfig';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormHelperText,
  Alert,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  CircularProgress,
  Tooltip,
  useTheme
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  Send as SendIcon,
  Schedule as ScheduleIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Notifications as NotificationsIcon,
  Group as GroupIcon,
  Topic as TopicIcon,
  Campaign as CampaignIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

// Notification types
const notificationTypes = [
  { value: 'promotional', label: 'Promotional' },
  { value: 'newMatch', label: 'New Match' },
  { value: 'profileView', label: 'Profile View' },
  { value: 'interestReceived', label: 'Interest Received' },
  { value: 'interestAccepted', label: 'Interest Accepted' },
  { value: 'newMessage', label: 'New Message' },
  { value: 'verificationStatus', label: 'Verification Status' }
];

// Target types
const targetTypes = [
  { value: 'USER', label: 'Single User', icon: <NotificationsIcon fontSize="small" /> },
  { value: 'TOPIC', label: 'Topic', icon: <TopicIcon fontSize="small" /> },
  { value: 'ALL_USERS', label: 'All Users', icon: <GroupIcon fontSize="small" /> },
  { value: 'PREMIUM_USERS', label: 'Premium Users', icon: <GroupIcon fontSize="small" color="primary" /> },
  { value: 'FREE_USERS', label: 'Free Users', icon: <GroupIcon fontSize="small" color="action" /> },
  { value: 'VERIFIED_USERS', label: 'Verified Users', icon: <GroupIcon fontSize="small" color="success" /> }
];

export default function NotificationsPage() {
  const { user, isAuthenticated } = useAuth();
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [scheduledNotifications, setScheduledNotifications] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Form state
  const [formData, setFormData] = useState({
    notificationType: 'promotional',
    targetType: 'ALL_USERS',
    targetId: '',
    scheduledFor: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    title: '',
    body: '',
    imageUrl: '',
    actionUrl: '',
    promotionId: ''
  });

  const [formErrors, setFormErrors] = useState({});

  // Fetch scheduled notifications
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchScheduledNotifications();
    }
  }, [isAuthenticated, user]);

  // Fetch scheduled notifications
  const fetchScheduledNotifications = async () => {
    try {
      setLoading(true);
      const data = await adminGet(ADMIN_ENDPOINTS.NOTIFICATIONS);
      if (data.success) {
        setScheduledNotifications(data.notifications || []);
      } else {
        throw new Error(data.message || 'Failed to fetch notifications');
      }
    } catch (error) {
      console.error('Error fetching scheduled notifications:', error);
      setSnackbar({
        open: true,
        message: 'Failed to fetch scheduled notifications',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors({ ...formErrors, [name]: '' });
    }
  };

  // Handle scheduled date change
  const handleDateChange = (date) => {
    setFormData({ ...formData, scheduledFor: date });

    // Clear error for this field
    if (formErrors.scheduledFor) {
      setFormErrors({ ...formErrors, scheduledFor: '' });
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.notificationType) {
      errors.notificationType = 'Notification type is required';
    }

    if (!formData.targetType) {
      errors.targetType = 'Target type is required';
    }

    if (formData.targetType === 'USER' && !formData.targetId) {
      errors.targetId = 'User ID is required';
    }

    if (formData.targetType === 'TOPIC' && !formData.targetId) {
      errors.targetId = 'Topic name is required';
    }

    if (!formData.title) {
      errors.title = 'Title is required';
    }

    if (!formData.body) {
      errors.body = 'Message body is required';
    }

    if (tabValue === 1 && !formData.scheduledFor) {
      errors.scheduledFor = 'Scheduled date is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle send notification
  const handleSendNotification = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      // Prepare notification data
      const notificationData = {
        targetType: formData.targetType,
        targetId: formData.targetId,
        notificationType: formData.notificationType,
        data: {
          title: formData.title,
          body: formData.body,
          imageUrl: formData.imageUrl,
          targetUrl: formData.actionUrl,
          promotionId: formData.promotionId
        }
      };

      // Send notification
      const data = await adminPost(ADMIN_ENDPOINTS.NOTIFICATIONS, notificationData);

      setSnackbar({
        open: true,
        message: 'Notification sent successfully',
        severity: 'success'
      });

      // Reset form
      resetForm();
    } catch (error) {
      console.error('Error sending notification:', error);
      setSnackbar({
        open: true,
        message: 'Failed to send notification',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle schedule notification
  const handleScheduleNotification = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      // Prepare notification data
      const notificationData = {
        targetType: formData.targetType,
        targetId: formData.targetId,
        notificationType: formData.notificationType,
        scheduledFor: formData.scheduledFor.toISOString(),
        data: {
          title: formData.title,
          body: formData.body,
          imageUrl: formData.imageUrl,
          targetUrl: formData.actionUrl,
          promotionId: formData.promotionId
        }
      };

      // Schedule notification
      const data = await adminPost(ADMIN_ENDPOINTS.NOTIFICATIONS, notificationData);

      // Add to list if successful
      if (data.success && data.notification) {
        setScheduledNotifications([data.notification, ...scheduledNotifications]);
      }

      setSnackbar({
        open: true,
        message: 'Notification scheduled successfully',
        severity: 'success'
      });

      // Reset form
      resetForm();
    } catch (error) {
      console.error('Error scheduling notification:', error);
      setSnackbar({
        open: true,
        message: 'Failed to schedule notification',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel scheduled notification
  const handleCancelNotification = async (id) => {
    try {
      setLoading(true);

      // Cancel notification
      const data = await adminDelete(`${ADMIN_ENDPOINTS.NOTIFICATIONS}/${id}`);

      // Remove from list
      setScheduledNotifications(scheduledNotifications.filter(n => n.id !== id));

      setSnackbar({
        open: true,
        message: 'Notification cancelled successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error cancelling notification:', error);
      setSnackbar({
        open: true,
        message: 'Failed to cancel notification',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      notificationType: 'promotional',
      targetType: 'ALL_USERS',
      targetId: '',
      scheduledFor: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      title: '',
      body: '',
      imageUrl: '',
      actionUrl: '',
      promotionId: ''
    });
    setFormErrors({});
  };

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Format date
  const formatDate = (date) => {
    return format(new Date(date), 'PPpp');
  };

  return (
    <EnhancedAdminLayout>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Notification Management
        </Typography>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab icon={<SendIcon />} label="Send Now" />
            <Tab icon={<ScheduleIcon />} label="Schedule" />
            <Tab icon={<CampaignIcon />} label="Scheduled Notifications" />
          </Tabs>
        </Paper>

        {/* Send Now Tab */}
        {tabValue === 0 && (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Send Immediate Notification
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" error={!!formErrors.notificationType}>
                  <InputLabel>Notification Type</InputLabel>
                  <Select
                    name="notificationType"
                    value={formData.notificationType}
                    onChange={handleInputChange}
                    label="Notification Type"
                  >
                    {notificationTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.notificationType && (
                    <FormHelperText>{formErrors.notificationType}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" error={!!formErrors.targetType}>
                  <InputLabel>Target</InputLabel>
                  <Select
                    name="targetType"
                    value={formData.targetType}
                    onChange={handleInputChange}
                    label="Target"
                  >
                    {targetTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {type.icon}
                          <Box sx={{ ml: 1 }}>{type.label}</Box>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.targetType && (
                    <FormHelperText>{formErrors.targetType}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {(formData.targetType === 'USER' || formData.targetType === 'TOPIC') && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={formData.targetType === 'USER' ? 'User ID' : 'Topic Name'}
                    name="targetId"
                    value={formData.targetId}
                    onChange={handleInputChange}
                    error={!!formErrors.targetId}
                    helperText={formErrors.targetId}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notification Title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  error={!!formErrors.title}
                  helperText={formErrors.title}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notification Message"
                  name="body"
                  value={formData.body}
                  onChange={handleInputChange}
                  multiline
                  rows={3}
                  error={!!formErrors.body}
                  helperText={formErrors.body}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Image URL (Optional)"
                  name="imageUrl"
                  value={formData.imageUrl}
                  onChange={handleInputChange}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Action URL (Optional)"
                  name="actionUrl"
                  value={formData.actionUrl}
                  onChange={handleInputChange}
                />
              </Grid>

              {formData.notificationType === 'promotional' && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Promotion ID (Optional)"
                    name="promotionId"
                    value={formData.promotionId}
                    onChange={handleInputChange}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSendNotification}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                  >
                    Send Notification
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Schedule Tab */}
        {tabValue === 1 && (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Schedule Notification
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" error={!!formErrors.notificationType}>
                  <InputLabel>Notification Type</InputLabel>
                  <Select
                    name="notificationType"
                    value={formData.notificationType}
                    onChange={handleInputChange}
                    label="Notification Type"
                  >
                    {notificationTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.notificationType && (
                    <FormHelperText>{formErrors.notificationType}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal" error={!!formErrors.targetType}>
                  <InputLabel>Target</InputLabel>
                  <Select
                    name="targetType"
                    value={formData.targetType}
                    onChange={handleInputChange}
                    label="Target"
                  >
                    {targetTypes.map(type => (
                      <MenuItem key={type.value} value={type.value}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {type.icon}
                          <Box sx={{ ml: 1 }}>{type.label}</Box>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.targetType && (
                    <FormHelperText>{formErrors.targetType}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {(formData.targetType === 'USER' || formData.targetType === 'TOPIC') && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={formData.targetType === 'USER' ? 'User ID' : 'Topic Name'}
                    name="targetId"
                    value={formData.targetId}
                    onChange={handleInputChange}
                    error={!!formErrors.targetId}
                    helperText={formErrors.targetId}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notification Title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  error={!!formErrors.title}
                  helperText={formErrors.title}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notification Message"
                  name="body"
                  value={formData.body}
                  onChange={handleInputChange}
                  multiline
                  rows={3}
                  error={!!formErrors.body}
                  helperText={formErrors.body}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Image URL (Optional)"
                  name="imageUrl"
                  value={formData.imageUrl}
                  onChange={handleInputChange}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Action URL (Optional)"
                  name="actionUrl"
                  value={formData.actionUrl}
                  onChange={handleInputChange}
                />
              </Grid>

              {formData.notificationType === 'promotional' && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Promotion ID (Optional)"
                    name="promotionId"
                    value={formData.promotionId}
                    onChange={handleInputChange}
                  />
                </Grid>
              )}

              <Grid item xs={12} md={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DateTimePicker
                    label="Schedule For"
                    value={formData.scheduledFor}
                    onChange={handleDateChange}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        error={!!formErrors.scheduledFor}
                        helperText={formErrors.scheduledFor}
                      />
                    )}
                    minDateTime={new Date()}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleScheduleNotification}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <ScheduleIcon />}
                  >
                    Schedule Notification
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Scheduled Notifications Tab */}
        {tabValue === 2 && (
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Scheduled Notifications
            </Typography>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : scheduledNotifications.length === 0 ? (
              <Alert severity="info">No scheduled notifications found</Alert>
            ) : (
              <>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Type</TableCell>
                        <TableCell>Target</TableCell>
                        <TableCell>Content</TableCell>
                        <TableCell>Scheduled For</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {scheduledNotifications
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((notification) => (
                          <TableRow key={notification.id}>
                            <TableCell>{notification.notificationType}</TableCell>
                            <TableCell>
                              <Chip
                                icon={targetTypes.find(t => t.value === notification.targetType)?.icon}
                                label={`${notification.targetType}${notification.targetId ? `: ${notification.targetId}` : ''}`}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontWeight="bold">
                                {notification.data.title}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" noWrap>
                                {notification.data.body}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              {formatDate(notification.scheduledFor)}
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={notification.status}
                                color={
                                  notification.status === 'SCHEDULED' ? 'primary' :
                                  notification.status === 'SENT' ? 'success' :
                                  notification.status === 'FAILED' ? 'error' : 'default'
                                }
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              {notification.status === 'SCHEDULED' && (
                                <Tooltip title="Cancel">
                                  <IconButton
                                    color="error"
                                    size="small"
                                    onClick={() => handleCancelNotification(notification.id)}
                                  >
                                    <DeleteIcon />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                <TablePagination
                  component="div"
                  count={scheduledNotifications.length}
                  page={page}
                  onPageChange={handleChangePage}
                  rowsPerPage={rowsPerPage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  rowsPerPageOptions={[5, 10, 25]}
                />
              </>
            )}
          </Paper>
        )}
      </Box>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </EnhancedAdminLayout>
  );
}
