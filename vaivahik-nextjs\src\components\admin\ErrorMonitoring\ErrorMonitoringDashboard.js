import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Card, 
  CardContent, 
  CardHeader,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  Pie<PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';
import RefreshIcon from '@mui/icons-material/Refresh';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import BugReportIcon from '@mui/icons-material/BugReport';
import TimelineIcon from '@mui/icons-material/Timeline';
import { formatDistanceToNow } from 'date-fns';
import axios from 'axios';

// Colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const ErrorMonitoringDashboard = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [overview, setOverview] = useState(null);
  const [errorsByType, setErrorsByType] = useState({});
  const [errorsByEndpoint, setErrorsByEndpoint] = useState({});
  const [recentErrors, setRecentErrors] = useState([]);
  const [timeframe, setTimeframe] = useState('day');

  // Fetch error data
  const fetchErrorData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch overview data
      const overviewResponse = await axios.get('/api/admin/error-monitoring/overview', {
        params: { timeframe }
      });
      setOverview(overviewResponse.data.data);
      
      // Fetch errors by type
      const typeResponse = await axios.get('/api/admin/error-monitoring/by-type', {
        params: { timeframe }
      });
      setErrorsByType(typeResponse.data.data);
      
      // Fetch errors by endpoint
      const endpointResponse = await axios.get('/api/admin/error-monitoring/by-endpoint', {
        params: { timeframe }
      });
      setErrorsByEndpoint(endpointResponse.data.data);
      
      // Fetch recent errors
      const recentResponse = await axios.get('/api/admin/error-monitoring/recent');
      setRecentErrors(recentResponse.data.data);
      
    } catch (err) {
      console.error('Error fetching error monitoring data:', err);
      setError('Failed to fetch error monitoring data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Reset error statistics
  const resetErrorStats = async () => {
    try {
      await axios.post('/api/admin/error-monitoring/reset-stats');
      fetchErrorData(); // Refresh data after reset
    } catch (err) {
      console.error('Error resetting error statistics:', err);
      setError('Failed to reset error statistics. Please try again later.');
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle timeframe change
  const handleTimeframeChange = (newTimeframe) => {
    setTimeframe(newTimeframe);
  };
  
  // Format error type for display
  const formatErrorType = (type) => {
    return type.replace(/([A-Z])/g, ' $1').trim();
  };
  
  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };
  
  // Prepare data for error type pie chart
  const prepareErrorTypePieData = () => {
    if (!errorsByType) return [];
    
    return Object.entries(errorsByType).map(([type, count]) => ({
      name: formatErrorType(type),
      value: count
    }));
  };
  
  // Prepare data for hourly trends chart
  const prepareHourlyTrendsData = () => {
    if (!overview || !overview.hourlyTrends) return [];
    
    return overview.hourlyTrends.map((count, index) => ({
      hour: index,
      count
    }));
  };
  
  // Prepare data for endpoint bar chart
  const prepareEndpointBarData = () => {
    if (!errorsByEndpoint) return [];
    
    return Object.entries(errorsByEndpoint).map(([endpoint, count]) => ({
      name: endpoint.length > 30 ? endpoint.substring(0, 30) + '...' : endpoint,
      count: typeof count === 'object' ? count.count : count
    })).sort((a, b) => b.count - a.count).slice(0, 10);
  };
  
  // Load data on component mount and when timeframe changes
  useEffect(() => {
    fetchErrorData();
  }, [timeframe]);
  
  // Render loading state
  if (loading && !overview) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Error Monitoring Dashboard
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<RefreshIcon />} 
            onClick={fetchErrorData}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button 
            variant="outlined" 
            color="warning" 
            onClick={resetErrorStats}
          >
            Reset Statistics
          </Button>
        </Box>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Box sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} centered>
          <Tab label="Overview" icon={<TimelineIcon />} iconPosition="start" />
          <Tab label="Error Types" icon={<ErrorIcon />} iconPosition="start" />
          <Tab label="Endpoints" icon={<WarningIcon />} iconPosition="start" />
          <Tab label="Recent Errors" icon={<BugReportIcon />} iconPosition="start" />
        </Tabs>
      </Box>
      
      <Box sx={{ mb: 2 }}>
        <Chip 
          label="Today" 
          color={timeframe === 'day' ? 'primary' : 'default'} 
          onClick={() => handleTimeframeChange('day')}
          sx={{ mr: 1 }}
        />
        <Chip 
          label="This Week" 
          color={timeframe === 'week' ? 'primary' : 'default'} 
          onClick={() => handleTimeframeChange('week')}
          sx={{ mr: 1 }}
        />
        <Chip 
          label="This Month" 
          color={timeframe === 'month' ? 'primary' : 'default'} 
          onClick={() => handleTimeframeChange('month')}
        />
      </Box>
      
      {/* Overview Tab */}
      {activeTab === 0 && overview && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Total Errors" />
              <CardContent>
                <Typography variant="h3" align="center">
                  {overview.totalErrors || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary" align="center">
                  Since {formatTimestamp(overview.lastReset)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader title="Hourly Error Trends" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={prepareHourlyTrendsData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Errors by Type" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={prepareErrorTypePieData()}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {prepareErrorTypePieData().map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Top Error Endpoints" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={prepareEndpointBarData()} layout="vertical">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" width={150} />
                    <RechartsTooltip />
                    <Bar dataKey="count" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {/* Error Types Tab */}
      {activeTab === 1 && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Error Type</TableCell>
                <TableCell align="right">Count</TableCell>
                <TableCell align="right">Percentage</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(errorsByType).map(([type, count]) => (
                <TableRow key={type}>
                  <TableCell>{formatErrorType(type)}</TableCell>
                  <TableCell align="right">{count}</TableCell>
                  <TableCell align="right">
                    {overview && overview.totalErrors
                      ? `${((count / overview.totalErrors) * 100).toFixed(1)}%`
                      : '0%'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Endpoints Tab */}
      {activeTab === 2 && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Endpoint</TableCell>
                <TableCell align="right">Count</TableCell>
                <TableCell>Error Types</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(errorsByEndpoint).map(([endpoint, data]) => (
                <TableRow key={endpoint}>
                  <TableCell>{endpoint}</TableCell>
                  <TableCell align="right">{data.count}</TableCell>
                  <TableCell>
                    {data.errors && Object.entries(data.errors).map(([errorType, errorCount]) => (
                      <Chip
                        key={errorType}
                        label={`${formatErrorType(errorType)}: ${errorCount}`}
                        size="small"
                        sx={{ m: 0.5 }}
                      />
                    ))}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Recent Errors Tab */}
      {activeTab === 3 && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Error Type</TableCell>
                <TableCell>Message</TableCell>
                <TableCell>Endpoint</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Time</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {recentErrors.map((error, index) => (
                <TableRow key={index}>
                  <TableCell>{formatErrorType(error.type)}</TableCell>
                  <TableCell>{error.message}</TableCell>
                  <TableCell>{error.endpoint}</TableCell>
                  <TableCell>{error.userId}</TableCell>
                  <TableCell>{formatTimestamp(error.timestamp)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default ErrorMonitoringDashboard;
