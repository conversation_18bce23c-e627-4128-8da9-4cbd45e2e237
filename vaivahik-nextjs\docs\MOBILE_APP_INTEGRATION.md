# 📱 Mobile App Integration Guide
## Enterprise Contact Security System for Flutter/React Native

This guide shows how to integrate the enterprise-grade contact security system into your mobile apps.

---

## 🚀 **Quick Start**

### **Flutter Integration**

```dart
// 1. Add dependencies to pubspec.yaml
dependencies:
  http: ^0.13.5
  url_launcher: ^6.1.10
  permission_handler: ^10.2.0

// 2. Create ContactApiService
class ContactApiService {
  static const String baseUrl = 'http://your-api-url.com/api';
  
  static Future<Map<String, dynamic>> revealContact(String userId) async {
    final response = await http.post(
      Uri.parse('$baseUrl/contact/reveal/$userId'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await getAuthToken()}',
        'X-Platform': 'FLUTTER',
      },
      body: jsonEncode({'platform': 'FLUTTER'}),
    );
    
    return jsonDecode(response.body);
  }
  
  static Future<void> openDialer(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }
}

// 3. Create SmartCallButton Widget
class SmartCallButton extends StatefulWidget {
  final String targetUserId;
  final String targetUserName;
  final Function(Map<String, dynamic>)? onCallInitiated;
  final Function(Map<String, dynamic>)? onSecurityBlock;

  const SmartCallButton({
    Key? key,
    required this.targetUserId,
    required this.targetUserName,
    this.onCallInitiated,
    this.onSecurityBlock,
  }) : super(key: key);

  @override
  _SmartCallButtonState createState() => _SmartCallButtonState();
}

class _SmartCallButtonState extends State<SmartCallButton> {
  bool _loading = false;
  
  Future<void> _handleCall() async {
    setState(() => _loading = true);
    
    try {
      final result = await ContactApiService.revealContact(widget.targetUserId);
      
      if (result['success']) {
        // Track successful contact reveal
        widget.onCallInitiated?.call(result);
        
        // Open native dialer
        await ContactApiService.openDialer(result['contactNumber']);
        
        // Show success dialog
        _showContactDialog(result);
      } else {
        _handleError(result);
      }
    } catch (error) {
      _handleError({'message': error.toString()});
    } finally {
      setState(() => _loading = false);
    }
  }
  
  void _handleError(Map<String, dynamic> error) {
    if (error['message'].toString().contains('security') || 
        error['message'].toString().contains('risk')) {
      widget.onSecurityBlock?.call(error);
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Contact Access'),
        content: Text(error['message'] ?? 'Unable to access contact'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: _loading ? null : _handleCall,
      icon: _loading 
        ? SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
        : Icon(Icons.phone),
      label: Text(_loading ? 'Connecting...' : 'Call ${widget.targetUserName}'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }
}
```

### **React Native Integration**

```javascript
// 1. Install dependencies
npm install @react-native-async-storage/async-storage react-native-url-launcher

// 2. Create ContactApiService
import { Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

class ContactApiService {
  static baseUrl = 'http://your-api-url.com/api';
  
  static async revealContact(userId) {
    const token = await AsyncStorage.getItem('authToken');
    
    const response = await fetch(`${this.baseUrl}/contact/reveal/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'X-Platform': 'REACT_NATIVE',
      },
      body: JSON.stringify({ platform: 'REACT_NATIVE' }),
    });
    
    return response.json();
  }
  
  static async openDialer(phoneNumber) {
    const url = `tel:${phoneNumber}`;
    const supported = await Linking.canOpenURL(url);
    
    if (supported) {
      await Linking.openURL(url);
    }
  }
}

// 3. Create SmartCallButton Component
import React, { useState } from 'react';
import { TouchableOpacity, Text, ActivityIndicator, Alert } from 'react-native';

const SmartCallButton = ({ 
  targetUserId, 
  targetUserName, 
  onCallInitiated, 
  onSecurityBlock 
}) => {
  const [loading, setLoading] = useState(false);
  
  const handleCall = async () => {
    setLoading(true);
    
    try {
      const result = await ContactApiService.revealContact(targetUserId);
      
      if (result.success) {
        onCallInitiated?.(result);
        await ContactApiService.openDialer(result.contactNumber);
        
        Alert.alert(
          'Contact Revealed',
          `Calling ${targetUserName}: ${result.contactNumber}`,
          [{ text: 'OK' }]
        );
      } else {
        handleError(result);
      }
    } catch (error) {
      handleError({ message: error.message });
    } finally {
      setLoading(false);
    }
  };
  
  const handleError = (error) => {
    if (error.message.includes('security') || error.message.includes('risk')) {
      onSecurityBlock?.(error);
    }
    
    Alert.alert(
      'Contact Access',
      error.message || 'Unable to access contact',
      [{ text: 'OK' }]
    );
  };
  
  return (
    <TouchableOpacity
      onPress={handleCall}
      disabled={loading}
      style={{
        backgroundColor: '#007AFF',
        padding: 12,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {loading ? (
        <ActivityIndicator color="white" size="small" />
      ) : (
        <Text style={{ color: 'white', fontWeight: 'bold' }}>
          📞 Call {targetUserName}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default SmartCallButton;
```

---

## 🛡️ **Security Features**

### **1. Platform Detection**
```javascript
// Automatically detects platform for security logging
const platform = Platform.OS === 'ios' ? 'IOS' : 'ANDROID';
```

### **2. Permission Handling**
```dart
// Flutter - Request phone permission
Future<bool> requestPhonePermission() async {
  final status = await Permission.phone.request();
  return status.isGranted;
}

// React Native - Check if dialer is available
const canMakeCall = await Linking.canOpenURL('tel:+1234567890');
```

### **3. Security Event Tracking**
```javascript
// Track security events for analytics
const trackSecurityEvent = (eventType, data) => {
  // Firebase Analytics
  analytics().logEvent(eventType, {
    target_user_id: data.targetUserId,
    risk_score: data.riskScore,
    platform: Platform.OS.toUpperCase(),
  });
};
```

---

## 📊 **Analytics Integration**

### **Firebase Analytics Events**
```javascript
// Track contact reveals
analytics().logEvent('contact_reveal_success', {
  target_user_id: userId,
  access_reason: 'PREMIUM_ONLY',
  platform: 'FLUTTER',
});

// Track security blocks
analytics().logEvent('security_block', {
  target_user_id: userId,
  risk_score: 85,
  reason: 'HIGH_RISK_PROFILE',
  platform: 'REACT_NATIVE',
});
```

---

## 🔧 **Configuration**

### **Environment Setup**
```dart
// config.dart
class AppConfig {
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://localhost:8000/api',
  );
  
  static const bool enableSecurityLogging = bool.fromEnvironment(
    'ENABLE_SECURITY_LOGGING',
    defaultValue: true,
  );
}
```

---

## 🚀 **Production Deployment**

### **1. API Configuration**
- Update API base URL to production server
- Configure SSL certificates
- Set up proper authentication

### **2. App Store Guidelines**
- Request phone permission with clear explanation
- Implement privacy policy for contact access
- Follow platform-specific guidelines

### **3. Security Monitoring**
- Set up crash reporting (Crashlytics)
- Monitor security events
- Implement user feedback system

---

## 📱 **Platform-Specific Features**

### **iOS Specific**
```swift
// Info.plist permissions
<key>NSContactsUsageDescription</key>
<string>This app needs access to make phone calls to connect you with potential matches.</string>
```

### **Android Specific**
```xml
<!-- AndroidManifest.xml permissions -->
<uses-permission android:name="android.permission.CALL_PHONE" />
<uses-permission android:name="android.permission.INTERNET" />
```

---

## 🎯 **Next Steps**

1. **Implement basic calling functionality**
2. **Add security event tracking**
3. **Test on real devices**
4. **Submit for app store review**
5. **Monitor security metrics**

Your mobile app will have the same enterprise-grade security as your website! 🛡️📱✨
