/**
 * Professional Matrimony Website Dashboard
 * Leverages all admin functionality for comprehensive user experience
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  IconButton,
  Badge,
  Tooltip,
  Divider,
  Paper,
  styled,
  useTheme,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  AppBar,
  Toolbar,
  CssBaseline,
  useMediaQuery,
  Collapse,
  CircularProgress
} from '@mui/material';

// Icons
import {
  Dashboard as DashboardIcon,
  Search as SearchIcon,
  Person as ProfileIcon,
  Verified as VerifiedIcon,
  Chat as ChatIcon,
  Description as BiodataIcon,
  Star as SpotlightIcon,
  TrendingUp as TrendingIcon,
  Visibility as ViewIcon,
  Favorite as HeartIcon,
  Message as MessageIcon,
  EmojiEvents as TrophyIcon,
  AutoAwesome as SparkleIcon,
  WorkspacePremium as PremiumIcon,
  LocalFireDepartment as FireIcon,
  Settings as SettingsIcon,
  Notifications as NotificationIcon,
  PhotoCamera as PhotoIcon,
  Edit as EditIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  ContactPhone as ContactIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Payment as PaymentIcon,
  Report as ReportIcon,
  Help as HelpIcon,
  Psychology as AIIcon,
  FilterList as FilterIcon,
  ExpandLess,
  ExpandMore
} from '@mui/icons-material';

// Hooks and utilities
import { useAuth } from '@/contexts/AuthContext';

// Import dashboard components
import VerificationQueueWidget from '@/components/dashboard/VerificationQueueWidget';
import ChatModuleWidget from '@/components/dashboard/ChatModuleWidget';
import BiodataTemplatesWidget from '@/components/dashboard/BiodataTemplatesWidget';
import SpotlightFeaturesWidget from '@/components/dashboard/SpotlightFeaturesWidget';
import ProfileCompletionWidget from '@/components/dashboard/ProfileCompletionWidget';
import SearchWidget from '@/components/dashboard/SearchWidget';

// Import new professional components
import AIMatchingWidget from '@/components/dashboard/AIMatchingWidget';
import AdvancedSearchWidget from '@/components/dashboard/AdvancedSearchWidget';
import UserAnalyticsWidget from '@/components/dashboard/UserAnalyticsWidget';
import ContactRevealWidget from '@/components/dashboard/ContactRevealWidget';
import PremiumFeaturesWidget from '@/components/dashboard/PremiumFeaturesWidget';
import PrivacySettingsWidget from '@/components/dashboard/PrivacySettingsWidget';
import { PrivacySettingsSummary } from '@/components/dashboard/PrivacyAwareDisplay';

// Sidebar width
const drawerWidth = 280;

// Professional styled components
const MainContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  backgroundColor: '#f8fafc'
}));

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  width: drawerWidth,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: drawerWidth,
    boxSizing: 'border-box',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    borderRight: 'none',
    boxShadow: '4px 0 20px rgba(0,0,0,0.1)'
  }
}));

const ContentArea = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  marginLeft: drawerWidth,
  [theme.breakpoints.down('md')]: {
    marginLeft: 0
  }
}));

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
  zIndex: theme.zIndex.drawer + 1
}));

const ProfessionalCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 16px 48px rgba(0,0,0,0.12)'
  }
}));

const StatsCard = styled(ProfessionalCard)(({ theme }) => ({
  background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
  textAlign: 'center',
  padding: theme.spacing(3)
}));

// Navigation menu items
const navigationItems = [
  {
    text: 'Dashboard',
    icon: <DashboardIcon />,
    key: 'dashboard',
    description: 'Overview & Analytics'
  },
  {
    text: 'AI Matches',
    icon: <AIIcon />,
    key: 'ai-matches',
    description: 'Smart Recommendations',
    badge: 'NEW'
  },
  {
    text: 'Advanced Search',
    icon: <SearchIcon />,
    key: 'search',
    description: 'Find Your Perfect Match'
  },
  {
    text: 'Messages',
    icon: <MessageIcon />,
    key: 'messages',
    description: 'Chat & Communication',
    premium: true
  },
  {
    text: 'Profile Views',
    icon: <ViewIcon />,
    key: 'profile-views',
    description: 'Who Viewed Your Profile'
  },
  {
    text: 'Interests',
    icon: <HeartIcon />,
    key: 'interests',
    description: 'Sent & Received Interests'
  },
  {
    text: 'Verification',
    icon: <VerifiedIcon />,
    key: 'verification',
    description: 'Profile Verification'
  },
  {
    text: 'Contact Reveal',
    icon: <ContactIcon />,
    key: 'contact-reveal',
    description: 'Contact Information',
    premium: true
  },
  {
    text: 'Biodata Templates',
    icon: <BiodataIcon />,
    key: 'biodata',
    description: '8 Professional Templates'
  },
  {
    text: 'Spotlight Features',
    icon: <SpotlightIcon />,
    key: 'spotlight',
    description: 'Boost Your Profile',
    premium: true
  },
  {
    text: 'Premium Plans',
    icon: <PremiumIcon />,
    key: 'premium',
    description: 'Upgrade Your Experience'
  },
  {
    text: 'Analytics',
    icon: <AnalyticsIcon />,
    key: 'analytics',
    description: 'Profile Performance'
  },
  {
    text: 'Settings',
    icon: <SettingsIcon />,
    key: 'settings',
    description: 'Privacy & Preferences'
  }
];

export default function ProfessionalDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // State management
  const [activeSection, setActiveSection] = useState('dashboard');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [stats, setStats] = useState({
    profileViews: 0,
    interests: 0,
    messages: 0,
    matches: 0
  });
  const [userData, setUserData] = useState({});
  const [isPremium, setIsPremium] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Simulate API call to fetch dashboard data
      setTimeout(() => {
        setStats({
          profileViews: 127,
          interests: 23,
          messages: 8,
          matches: 45
        });

        setUserData({
          name: user?.name || 'Beautiful Soul',
          profilePhoto: user?.profilePhoto,
          gender: 'male',
          age: 28,
          education: 'Engineering',
          occupation: 'Software Engineer'
        });

        setIsPremium(user?.isPremium || false);
        setIsVerified(user?.isVerified || false);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleSectionChange = (section) => {
    setActiveSection(section);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handlePremiumFeatureClick = (feature) => {
    if (!isPremium) {
      setActiveSection('premium');
      return;
    }
    console.log('Accessing premium feature:', feature);
  };

  const renderSidebarContent = () => (
    <Box>
      {/* Logo/Brand Section */}
      <Box sx={{ p: 3, textAlign: 'center', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
        <Typography variant="h5" fontWeight="700" sx={{ color: 'white', mb: 1 }}>
          Vaivahik
        </Typography>
        <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.7)' }}>
          Professional Matrimony
        </Typography>
      </Box>

      {/* User Profile Section */}
      <Box sx={{ p: 3, textAlign: 'center', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
        <Avatar
          src={userData.profilePhoto || '/api/placeholder/80/80'}
          sx={{
            width: 80,
            height: 80,
            margin: '0 auto 16px',
            border: '3px solid rgba(255,255,255,0.2)'
          }}
        />
        <Typography variant="h6" fontWeight="600" sx={{ color: 'white', mb: 1 }}>
          {userData.name}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
          {isVerified && (
            <Chip
              icon={<VerifiedIcon />}
              label="Verified"
              size="small"
              sx={{ backgroundColor: 'rgba(76, 175, 80, 0.2)', color: '#4CAF50' }}
            />
          )}
          {isPremium && (
            <Chip
              icon={<PremiumIcon />}
              label="Premium"
              size="small"
              sx={{ backgroundColor: 'rgba(255, 215, 0, 0.2)', color: '#FFD700' }}
            />
          )}
        </Box>
      </Box>

      {/* Navigation Menu */}
      <List sx={{ px: 2, py: 1 }}>
        {navigationItems.map((item) => (
          <ListItemButton
            key={item.key}
            onClick={() => handleSectionChange(item.key)}
            selected={activeSection === item.key}
            sx={{
              borderRadius: 2,
              mb: 1,
              '&.Mui-selected': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.2)'
                }
              },
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.1)'
              }
            }}
          >
            <ListItemIcon sx={{ color: 'white', minWidth: 40 }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.text}
              secondary={item.description}
              primaryTypographyProps={{
                fontWeight: activeSection === item.key ? 600 : 400,
                color: 'white'
              }}
              secondaryTypographyProps={{
                color: 'rgba(255,255,255,0.7)',
                fontSize: '0.75rem'
              }}
            />
            {item.badge && (
              <Chip
                label={item.badge}
                size="small"
                sx={{
                  backgroundColor: '#FF4081',
                  color: 'white',
                  fontSize: '0.7rem',
                  height: 20
                }}
              />
            )}
            {item.premium && !isPremium && (
              <PremiumIcon sx={{ color: '#FFD700', fontSize: 16 }} />
            )}
          </ListItemButton>
        ))}
      </List>
    </Box>
  );

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/login');
    }
  }, [user, router]);

  if (!user) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <>
      <Head>
        <title>Professional Dashboard - Vaivahik Matrimony</title>
        <meta name="description" content="Professional matrimony dashboard with AI-powered matching and comprehensive features" />
      </Head>

      <CssBaseline />
      <MainContainer>
        {/* App Bar for Mobile */}
        {isMobile && (
          <StyledAppBar position="fixed">
            <Toolbar>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" noWrap component="div">
                Vaivahik Dashboard
              </Typography>
            </Toolbar>
          </StyledAppBar>
        )}

        {/* Sidebar Navigation */}
        <StyledDrawer
          variant={isMobile ? 'temporary' : 'permanent'}
          open={isMobile ? mobileOpen : true}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true // Better open performance on mobile
          }}
        >
          {renderSidebarContent()}
        </StyledDrawer>

        {/* Main Content Area */}
        <ContentArea>
          {isMobile && <Toolbar />} {/* Spacer for mobile app bar */}
          
          {/* Dashboard Content will be rendered here based on activeSection */}
          <Box>
            <Typography variant="h4" fontWeight="700" gutterBottom>
              {navigationItems.find(item => item.key === activeSection)?.text || 'Dashboard'}
            </Typography>
            
            {/* Stats Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {[
                { icon: ViewIcon, label: 'Profile Views', value: stats.profileViews, color: '#2196F3' },
                { icon: HeartIcon, label: 'Interests', value: stats.interests, color: '#E91E63' },
                { icon: MessageIcon, label: 'Messages', value: stats.messages, color: '#4CAF50' },
                { icon: TrophyIcon, label: 'Matches', value: stats.matches, color: '#FF9800' }
              ].map((stat, index) => (
                <Grid item xs={6} md={3} key={index}>
                  <StatsCard>
                    <stat.icon sx={{ fontSize: 40, color: stat.color, mb: 2 }} />
                    <Typography variant="h4" fontWeight="700" color={stat.color} gutterBottom>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </StatsCard>
                </Grid>
              ))}
            </Grid>

            {/* Content based on active section */}
            {activeSection === 'dashboard' && (
              <ProfessionalCard>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    Dashboard Overview
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                    Welcome to your comprehensive matrimony dashboard. Here's an overview of your profile activity.
                  </Typography>

                  {/* Quick Actions */}
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={4}>
                      <Button
                        fullWidth
                        variant="contained"
                        startIcon={<AIIcon />}
                        onClick={() => setActiveSection('ai-matches')}
                        sx={{
                          background: 'linear-gradient(135deg, #667eea, #764ba2)',
                          py: 2
                        }}
                      >
                        View AI Matches
                      </Button>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<SearchIcon />}
                        onClick={() => setActiveSection('search')}
                        sx={{ py: 2 }}
                      >
                        Advanced Search
                      </Button>
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<SecurityIcon />}
                        onClick={() => setActiveSection('settings')}
                        sx={{ py: 2 }}
                      >
                        Privacy Settings
                      </Button>
                    </Grid>
                  </Grid>

                  {/* Privacy Summary */}
                  <Box sx={{ mt: 4, p: 3, backgroundColor: '#f8fafc', borderRadius: 2 }}>
                    <PrivacySettingsSummary settings={userData.privacySettings} />
                  </Box>
                </CardContent>
              </ProfessionalCard>
            )}

            {activeSection === 'ai-matches' && (
              <AIMatchingWidget
                userId={user?.id}
                currentUser={user}
                isPremium={isPremium}
                onPremiumFeatureClick={handlePremiumFeatureClick}
              />
            )}

            {activeSection === 'search' && (
              <AdvancedSearchWidget
                isPremium={isPremium}
                onPremiumFeatureClick={handlePremiumFeatureClick}
              />
            )}

            {activeSection === 'messages' && (
              <ChatModuleWidget
                userId={user?.id}
                isPremium={isPremium}
                onPremiumFeatureClick={handlePremiumFeatureClick}
              />
            )}

            {activeSection === 'verification' && (
              <VerificationQueueWidget
                userId={user?.id}
                isVerified={isVerified}
              />
            )}

            {activeSection === 'contact-reveal' && (
              <ContactRevealWidget
                userId={user?.id}
                isPremium={isPremium}
                onPremiumFeatureClick={handlePremiumFeatureClick}
              />
            )}

            {activeSection === 'biodata' && (
              <BiodataTemplatesWidget
                userId={user?.id}
                userGender={userData.gender}
              />
            )}

            {activeSection === 'spotlight' && (
              <SpotlightFeaturesWidget
                userId={user?.id}
                currentSpotlight={null}
              />
            )}

            {activeSection === 'premium' && (
              <PremiumFeaturesWidget
                userId={user?.id}
                currentPlan={isPremium ? 'premium' : null}
                onUpgrade={(plan) => {
                  setIsPremium(true);
                  console.log('Upgraded to:', plan);
                }}
              />
            )}

            {activeSection === 'analytics' && (
              <UserAnalyticsWidget
                userId={user?.id}
              />
            )}

            {activeSection === 'settings' && (
              <PrivacySettingsWidget
                userId={user?.id}
              />
            )}

            {(activeSection === 'profile-views' || activeSection === 'interests') && (
              <ProfessionalCard>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    {activeSection === 'profile-views' && 'Profile Views'}
                    {activeSection === 'interests' && 'Interests Management'}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    This section will be implemented with your existing admin functionality.
                    {activeSection === 'profile-views' && ' View detailed analytics of who viewed your profile.'}
                    {activeSection === 'interests' && ' Manage sent and received interests with advanced filtering.'}
                  </Typography>
                </CardContent>
              </ProfessionalCard>
            )}
          </Box>
        </ContentArea>
      </MainContainer>
    </>
  );
}
