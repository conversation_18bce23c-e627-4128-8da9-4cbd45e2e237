<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaivahik - AI-Powered Matrimony for Indian Families</title>
    <!-- TODO: Add your actual favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>

    <style>
        /* Global Styles */
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

        :root {
            /* Core Colors */
            --primary-color: #FF5F6D; /* Coral Pink */
            --primary-light: #FFC371; /* Light Orange */
            --secondary-color: #8A2BE2; /* Blue Violet */
            --secondary-light: #9370DB; /* Medium Purple */
            --accent-color: #FFD700; /* Gold */

            /* Backgrounds & Text */
            --dark-color: #2D3047; /* Deep Blue/Gray */
            --light-color: #F8F9FA; /* Very Light Gray */
            --light-color-alt: #F0F2F5; /* Slightly different light gray for alternation */
            --white: #FFFFFF;
            --text-color-dark: #333;
            --text-color-medium: #555;
            --text-color-light: #F0F0F0;
            --text-color-light-muted: #B0B0B0;

            /* Gradients */
            --primary-gradient: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%);
            --secondary-gradient: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
            --subtle-white-gradient: linear-gradient(180deg, var(--white) 0%, #fcfdff 100%);
            --subtle-light-gradient: linear-gradient(180deg, var(--light-color) 0%, #f0f2f5 100%);


            /* Shadows & Effects */
            --shadow-soft: 0 5px 15px rgba(0,0,0,0.05);
            --shadow-medium: 0 10px 25px rgba(0,0,0,0.1);
            --shadow-hard: 0 15px 35px rgba(0,0,0,0.15);
            --transition-smooth: all 0.3s ease;
            --border-radius-medium: 20px;
            --border-radius-large: 25px;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { scroll-behavior: smooth; }

        body {
            font-family: 'Montserrat', sans-serif;
            color: var(--text-color-dark);
            line-height: 1.6;
            overflow-x: hidden;
            background-color: var(--white);
        }

        h1, h2, h3, h4, h5 {
            font-family: 'Playfair Display', serif;
            font-weight: 700;
            color: var(--dark-color);
        }
        p { color: var(--text-color-medium); margin-bottom: 1rem; }
        section p:last-child { margin-bottom: 0; }
        .container { width: 90%; max-width: 1400px; margin: 0 auto; }

        /* Button Styles (No changes from previous, already good) */
        .btn { display: inline-block; padding: 12px 30px; border-radius: 50px; text-decoration: none; font-weight: 600; cursor: pointer; transition: var(--transition-smooth), transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); border: none; font-size: 16px; position: relative; overflow: hidden; z-index: 1; }
        .btn-primary { background: var(--primary-gradient); color: var(--white); box-shadow: 0 5px 15px rgba(255, 95, 109, 0.4); }
        .btn-secondary { background: var(--secondary-gradient); color: var(--white); box-shadow: 0 5px 15px rgba(138, 43, 226, 0.4); }
        .btn-outline { background: transparent; color: var(--primary-color); border: 2px solid var(--primary-color); }
        .btn-outline-secondary { background: transparent; color: var(--secondary-color); border: 2px solid var(--secondary-color); }
        .btn:hover, .btn:focus { transform: translateY(-5px); box-shadow: var(--shadow-hard); filter: brightness(1.1); outline: none; }
        .btn-outline:hover, .btn-outline:focus { background: var(--primary-color); color: var(--white); }
        .btn-outline-secondary:hover, .btn-outline-secondary:focus { background: var(--secondary-color); color: var(--white); }
        .btn-primary:hover, .btn-primary:focus { box-shadow: 0 8px 25px rgba(255, 95, 109, 0.6); }
        .btn-secondary:hover, .btn-secondary:focus { box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6); }
        .btn::after { content: ''; position: absolute; width: 0; height: 0; border-radius: 50%; background: rgba(255, 255, 255, 0.2); top: 50%; left: 50%; transform: translate(-50%, -50%); transition: width 0.6s ease-out, height 0.6s ease-out; z-index: -1; opacity: 0; }
        .btn:hover::after { width: 300px; height: 300px; opacity: 1; }
        .btn-small { padding: 10px 25px; font-size: 15px; }


        /* Section Base Styles */
        section {
            padding: 100px 0;
            position: relative;
            overflow: hidden; /* Important for ::before/::after shapes */
        }
        section:first-of-type { padding-top: 150px; background-color: transparent; }
        #hero, #testimonials, .cta, footer { background-color: transparent; }

        .section-content-wrapper { /* To keep content above shape dividers */
            position: relative;
            z-index: 2;
        }

        /* Section Title Styles (No changes from previous) */
        .section-title { text-align: center; margin-bottom: 60px; position: relative; }
        .section-title h2 { font-size: 42px; margin-bottom: 20px; background: var(--primary-gradient); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; display: inline-block; }
        .section-title .subtitle { font-size: 18px; color: var(--text-color-medium); max-width: 700px; margin: 0 auto 20px; position: relative; opacity: 0.9; }
        .section-title::after { content: ''; width: 80px; height: 3px; background: var(--primary-gradient); position: absolute; bottom: 0px; left: 50%; transform: translateX(-50%); border-radius: 2px; }

        /* Header (No changes from previous) */
        header { background: rgba(255, 255, 255, 0.95); padding: 15px 0; position: fixed; width: 100%; z-index: 1000; box-shadow: 0 2px 20px rgba(0,0,0,0.05); transition: var(--transition-smooth); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); }
        header.scrolled { padding: 10px 0; background: rgba(255, 255, 255, 0.98); box-shadow: var(--shadow-medium); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); }
        .header-container { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-family: 'Playfair Display', serif; font-size: 28px; font-weight: 700; display: flex; align-items: center; text-decoration: none; transition: transform 0.3s ease; }
        .logo:hover { transform: scale(1.05); }
        .logo-text { background: var(--primary-gradient); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; }
        .logo-symbol { margin-right: 10px; font-size: 32px; color: var(--primary-color); transition: transform 0.5s ease; }
        .logo:hover .logo-symbol { transform: rotate(360deg); }
        .logo span { color: var(--secondary-color); font-weight: 800; }
        nav ul { display: flex; list-style: none; align-items: center; }
        nav ul li { margin-left: 30px; }
        nav ul li a { color: var(--dark-color); text-decoration: none; font-weight: 600; transition: var(--transition-smooth); position: relative; padding: 5px 0; letter-spacing: 0.5px; }
        nav ul li a::after { content: ''; position: absolute; width: 0; height: 2px; background: var(--primary-gradient); bottom: -2px; left: 50%; transform: translateX(-50%); transition: width 0.3s ease; border-radius: 1px; }
        nav ul li a:hover, nav ul li a:focus { color: var(--primary-color); outline: none; }
        nav ul li a:hover::after, nav ul li a:focus::after { width: 100%; }
        .hamburger { display: none; cursor: pointer; z-index: 1001; padding: 5px; background: transparent; border: none; }
        .hamburger div { width: 25px; height: 3px; background: var(--primary-color); margin: 5px; transition: all 0.4s ease; border-radius: 3px; }
        .hamburger.active .line1 { transform: rotate(-45deg) translate(-5px, 6px); }
        .hamburger.active .line2 { opacity: 0; transform: translateX(-20px); }
        .hamburger.active .line3 { transform: rotate(45deg) translate(-5px, -6px); }

        /* Hero Section */
        /* <!-- TODO: Replace with YOUR OWN high-quality, culturally relevant hero image/video --> */
        .hero { background: url('https://picsum.photos/seed/vaivahikHeroPro/1920/1080') center/cover no-repeat; min-height: 100vh; display: flex; align-items: center; position: relative; color: var(--white); padding: 0; }
        .hero::before { content: ''; position: absolute; inset: 0; background: linear-gradient(135deg, rgba(45, 48, 71, 0.88) 0%, rgba(255, 95, 109, 0.78) 100%); z-index: 1; } /* Slightly darker overlay for better text contrast */
        .hero-content { position: relative; z-index: 2; max-width: 750px; animation: fadeInUp 1s ease; }
        .hero h1 { font-size: 58px; font-weight: 700; margin-bottom: 25px; line-height: 1.25; text-shadow: 0 3px 12px rgba(0,0,0,0.3); color: var(--white); }
        .hero p { font-size: 21px; margin-bottom: 35px; text-shadow: 0 2px 7px rgba(0,0,0,0.25); opacity: 0.95; color: rgba(255, 255, 255, 0.95); }
        .hero-buttons { display: flex; flex-wrap: wrap; gap: 20px; margin-top: 30px; }
        .floating-hearts { position: absolute; inset: 0; z-index: 1; overflow: hidden; pointer-events: none; }
        .heart { position: absolute; width: 30px; height: 30px; background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF5F6D'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E") no-repeat center center; background-size: contain; opacity: 0.6; animation: floating 15s linear infinite; will-change: transform, opacity; }
        @keyframes floating { 0% { transform: translateY(100vh) scale(0.5) rotate(0deg); opacity: 0; } 10% { opacity: 0.8; } 90% { opacity: 0.8; } 100% { transform: translateY(-100px) scale(1.2) rotate(360deg); opacity: 0; } }

        /* Shape Divider Base Style */
        .shape-divider {
            position: absolute;
            left: 0;
            width: 100%;
            overflow: hidden;
            line-height: 0;
            z-index: 1; /* Below content, above section BG color if needed */
        }
        .shape-divider svg {
            position: relative;
            display: block;
            width: calc(100% + 1.3px); /* SVG responsiveness */
            height: auto;
        }
        .shape-divider.top { top: -1px; /* Overlap slightly */ }
        .shape-divider.bottom { bottom: -1px; transform: rotate(180deg); /* Flip for bottom usage */ }


        /* Features Section */
        .features {
            background: var(--subtle-white-gradient); /* White to very light */
        }
        /* Decorative blobs for features section */
        .features::before { content: ''; position: absolute; width: 300px; height: 300px; border-radius: 50%; background: linear-gradient(135deg, rgba(255, 95, 109, 0.07) 0%, rgba(255, 195, 113, 0.1) 100%); top: -120px; right: -100px; z-index: 0; transform: rotate(25deg); animation: blobAnim1 20s infinite alternate ease-in-out; }
        .features::after { content: ''; position: absolute; width: 400px; height: 400px; border-radius: 45%; background: linear-gradient(135deg, rgba(138, 43, 226, 0.06) 0%, rgba(147, 112, 219, 0.09) 100%); bottom: -180px; left: -150px; z-index: 0; transform: rotate(-35deg); animation: blobAnim2 25s infinite alternate ease-in-out; }
        @keyframes blobAnim1 { 0% { transform: scale(1) translate(0,0) rotate(25deg); } 100% { transform: scale(1.1) translate(20px, -15px) rotate(35deg); } }
        @keyframes blobAnim2 { 0% { transform: scale(1) translate(0,0) rotate(-35deg); } 100% { transform: scale(1.1) translate(-15px, 20px) rotate(-25deg); } }

        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 40px; position: relative; z-index: 2; } /* z-index higher than blobs */
        .feature-card { background: var(--white); padding: 40px 30px; border-radius: var(--border-radius-medium); box-shadow: var(--shadow-medium); transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); position: relative; overflow: hidden; border: 1px solid rgba(0,0,0,0.03); height: 100%; display: flex; flex-direction: column; text-align: center; border-bottom: 4px solid transparent; }
        .feature-card:hover { transform: translateY(-15px) scale(1.03); box-shadow: var(--shadow-hard); border-bottom-color: var(--primary-color); }
        .feature-card:nth-child(even):hover { border-bottom-color: var(--secondary-color); }
        /* <!-- TODO: Replace with YOUR OWN custom SVG icons for features --> */
        .feature-visual { width: 70px; height: 70px; margin: 0 auto 30px auto; object-fit: contain; transition: transform 0.4s ease; filter: drop-shadow(0 3px 5px rgba(0,0,0,0.1)); }
        .feature-card:hover .feature-visual { transform: scale(1.15) rotate(-5deg); filter: drop-shadow(0 5px 8px rgba(0,0,0,0.15));}
        .feature-card h3 { font-size: 24px; margin-bottom: 15px; color: var(--dark-color); }
        .feature-card p { color: var(--text-color-medium); font-size: 16px; line-height: 1.7; flex-grow: 1; margin-bottom: 20px; }
        .security-privacy-info { display: flex; justify-content: center; align-items: center; gap: 15px; margin-top: auto; font-size: 14px; color: var(--text-color-medium); }
        .security-privacy-info i { color: var(--primary-color); }


        /* How It Works Section */
        .how-it-works { background-color: var(--light-color); }
        /* Shape Divider for top of How It Works */
        .how-it-works .shape-divider.top svg path { fill: var(--white); /* Matches Features BG */ }
        /* Add a subtle pattern to How It Works background */
        .how-it-works {
            /* background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232D3047' fill-opacity='0.02'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); */
        }
        .process-timeline { display: flex; flex-wrap: wrap; justify-content: space-around; position: relative; margin-top: 80px; z-index: 2; }
        .process-timeline::before { content: ''; position: absolute; width: calc(100% - 200px); max-width: 1000px; height: 4px; background: var(--primary-gradient); opacity:0.7; top: 58px; left: 50%; transform: translateX(-50%); z-index: -1; display: none; border-radius: 2px; }
        .process-step { flex: 1; min-width: 250px; text-align: center; padding: 0 20px; position: relative; margin-bottom: 40px; }
        .step-visual { display: flex; flex-direction: column; align-items: center; margin-bottom: 25px; }
        .step-icon { font-size: 32px; color: var(--primary-color); margin-bottom: 10px; transition: transform 0.3s ease; }
        .process-step:hover .step-icon { transform: scale(1.2) rotate(-5deg); }
        .step-number { background: var(--primary-gradient); color: var(--white); width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 28px; font-weight: 700; position: relative; z-index: 2; box-shadow: 0 8px 20px rgba(255, 95, 109, 0.3); transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); border: 3px solid var(--white); }
        .process-step:hover .step-number { transform: scale(1.1) rotate(3deg); box-shadow: 0 15px 30px rgba(255, 95, 109, 0.4); }
        .process-step h3 { color: var(--dark-color); margin-bottom: 10px; }
        .process-step p { color: var(--text-color-medium); font-size: 15px; }


        /* Testimonials Section (Keeps its distinct gradient) */
        .testimonials { background: var(--secondary-gradient); color: var(--white); position: relative; padding-top: 120px; padding-bottom: 120px;} /* Added more padding */
        /* Top shape for Testimonials, fill should match previous section BG (--light-color) */
        .testimonials .shape-divider.top svg path { fill: var(--light-color); }
        /* Bottom shape for Testimonials, fill should match next section BG (--white for Success Stories) */
        .testimonials .shape-divider.bottom svg path { fill: var(--white); }

        .testimonials::before { content: ''; position: absolute; inset: 0; background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23FFFFFF' fill-opacity='0.07' fill-rule='evenodd'/%3E%3C/svg%3E"); z-index: 0; }
        .testimonials .section-title h2 { background: none; -webkit-text-fill-color: var(--white); text-shadow: 0 2px 8px rgba(0,0,0,0.2); }
        .testimonials .section-title .subtitle { color: rgba(255,255,255,0.9); }
        .testimonials .section-title::after { background: var(--white); height: 4px; bottom: 5px; }
        .testimonial-slider { position: relative; z-index: 1; padding: 40px 0; max-width: 900px; margin: 0 auto; overflow: visible; }
        .swiper-container { width: 100%; padding: 20px 0 50px; }
        .testimonial-card { background: rgba(255,255,255,0.1); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); padding: 45px; border-radius: var(--border-radius-large); border: 1px solid rgba(255,255,255,0.25); box-shadow: 0 15px 35px rgba(0,0,0,0.15); transition: var(--transition-smooth); position: relative; margin: 0 10px; height: auto; min-height: 320px; display: flex; flex-direction: column; }
        .testimonial-card::before { content: '\201C'; font-family: Georgia, serif; position: absolute; top: 25px; left: 25px; font-size: 130px; color: rgba(255,255,255,0.1); line-height: 1; z-index: 0; transition: transform 0.4s ease; }
        .swiper-slide-active .testimonial-card::before { transform: scale(1.1); }
        .testimonial-text { font-style: italic; margin-bottom: 35px; font-size: 19px; line-height: 1.85; position: relative; z-index: 1; flex-grow: 1; color: rgba(255,255,255,0.95); }
        .testimonial-author { display: flex; align-items: center; margin-top: auto; }
        /* <!-- TODO: Replace with REAL images of testimonial authors --> */
        .author-image { width: 75px; height: 75px; border-radius: 50%; margin-right: 20px; background-color: rgba(255,255,255,0.2); background-size: cover; background-position: center; border: 4px solid rgba(255,255,255,0.35); box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .author-info h4 { font-size: 21px; margin-bottom: 5px; color: var(--white); font-weight: 600; }
        .author-info p { font-size: 16px; opacity: 0.85; }
        .swiper-button-next, .swiper-button-prev { color: var(--white); background-color: rgba(0, 0, 0, 0.2); width: 50px; height: 50px; border-radius: 50%; transition: var(--transition-smooth); opacity: 0.7; top: 50%; transform: translateY(-50%); }
        .swiper-button-next:hover, .swiper-button-prev:hover { background-color: rgba(0, 0, 0, 0.4); opacity: 1; transform: translateY(-50%) scale(1.1); }
        .swiper-button-next::after, .swiper-button-prev::after { font-size: 20px; font-weight: bold; }
        .swiper-button-prev { left: -15px; } .swiper-button-next { right: -15px; }
        .swiper-pagination-bullet { background-color: rgba(255, 255, 255, 0.5); width: 12px; height: 12px; opacity: 0.8; transition: var(--transition-smooth); }
        .swiper-pagination-bullet-active { background-color: var(--white); transform: scale(1.3); opacity: 1; }
        .swiper-pagination { bottom: 10px !important; position: absolute; }
        .video-testimonial-link { display: inline-block; margin-top: 15px; color: var(--accent-color); font-weight: 600; text-decoration: underline; }
        .video-testimonial-link i { margin-right: 5px; }


        /* Success Stories Section */
        .success-stories { background-color: var(--white); } /* Contrast after dark testimonials */
        /* Shape Divider for top of Success Stories is handled by Testimonials bottom shape */
        /* Shape Divider for bottom of Success Stories */
        .success-stories .shape-divider.bottom svg path { fill: var(--light-color-alt); /* Matches As Seen On BG */ }

        .success-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 35px; position: relative; z-index: 2; }
        .success-card { background: var(--white); border-radius: var(--border-radius-medium); overflow: hidden; box-shadow: var(--shadow-medium); transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); display: flex; flex-direction: column; border: 1px solid rgba(0,0,0,0.05); }
        .success-card:hover { transform: translateY(-12px) scale(1.03); box-shadow: var(--shadow-hard); }
        .success-image { height: 260px; background-size: cover; background-position: center; position: relative; overflow: hidden; }
        .success-image::before { content: ''; position: absolute; inset: 0; background: inherit; background-size: inherit; background-position: inherit; transition: transform 0.5s ease; }
        .success-card:hover .success-image::before { transform: scale(1.1); }
        /* <!-- TODO: Replace with REAL images of couples --> */
        .success-card:nth-child(1) .success-image { background-image: url('https://picsum.photos/seed/couple1Story/400/260'); }
        .success-card:nth-child(2) .success-image { background-image: url('https://picsum.photos/seed/couple2Story/400/260'); }
        .success-card:nth-child(3) .success-image { background-image: url('https://picsum.photos/seed/couple3Story/400/260'); }
        .success-overlay { position: absolute; bottom: 0; left: 0; width: 100%; height: 50%; background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%); display: flex; align-items: flex-end; padding: 20px; box-sizing: border-box; z-index: 1; transition: height 0.4s ease; }
        .success-card:hover .success-overlay { height: 60%; }
        .success-names { color: var(--white); font-size: 24px; font-weight: 700; font-family: 'Playfair Display', serif; text-shadow: 1px 1px 3px rgba(0,0,0,0.5); }
        .success-content { padding: 30px; flex-grow: 1; display: flex; flex-direction: column; }
        .success-meta { display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px; color: #666; font-size: 14px; }
        .meta-item { display: flex; align-items: center; }
        .meta-item i { margin-right: 7px; color: var(--primary-color); width: 16px; text-align: center; }
        .success-story { font-size: 16px; line-height: 1.75; margin-bottom: 25px; color: var(--text-color-medium); flex-grow: 1; }
        .read-more { color: var(--primary-color); font-weight: 600; text-decoration: none; display: inline-flex; align-items: center; transition: var(--transition-smooth); margin-top: auto; padding: 5px 0; }
        .read-more i { margin-left: 8px; transition: transform 0.3s ease; }
        .read-more:hover, .read-more:focus { color: var(--secondary-color); letter-spacing: 0.5px; outline: none; }
        .read-more:hover i, .read-more:focus i { transform: translateX(6px); }


        /* As Seen On Section */
        #as-seen-on { background-color: var(--light-color-alt); padding: 80px 0; text-align: center; }
        /* Shape Divider for top of As Seen On is handled by Success Stories bottom shape */
        #as-seen-on h3 { font-size: 18px; color: var(--text-color-medium); margin-bottom: 40px; text-transform: uppercase; letter-spacing: 1px; font-weight: 500; }
        .media-logos { display: flex; flex-wrap: wrap; justify-content: center; align-items: center; gap: 40px 50px; /* Increased gap */ }
        /* <!-- TODO: Replace with actual media SVG logos for best quality and control --> */
        .media-logos img { max-height: 45px; opacity: 0.6; filter: grayscale(80%) contrast(0.8); transition: var(--transition-smooth); }
        .media-logos img:hover { opacity: 1; filter: grayscale(0%) contrast(1); transform: scale(1.1); }


        /* Pricing Section */
        .pricing { background: var(--subtle-light-gradient); padding-bottom: 120px; }
        .pricing .shape-divider.top svg path { fill: var(--light-color-alt); /* Matches As Seen On BG */ }
        .pricing-tabs { display: flex; justify-content: center; margin-bottom: 60px; background: var(--white); border-radius: 50px; padding: 6px; box-shadow: var(--shadow-medium); width: fit-content; margin-left: auto; margin-right: auto; position: relative; z-index: 2;}
        .pricing-tab { padding: 12px 30px; border: none; color: var(--dark-color); background-color: transparent; cursor: pointer; font-weight: 600; transition: var(--transition-smooth); user-select: none; border-radius: 50px; margin: 0 5px; }
        .pricing-tab.active { background: var(--primary-gradient); color: var(--white); box-shadow: 0 4px 12px rgba(255, 95, 109, 0.45); }
        .pricing-tab:not(.active):hover { color: var(--primary-color); }
        .pricing-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 40px; position: relative; z-index: 2; align-items: stretch; }
        .pricing-card { background: var(--white); border-radius: var(--border-radius-large); overflow: visible; box-shadow: var(--shadow-soft); transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); position: relative; border: 1px solid rgba(0,0,0,0.07); display: flex; flex-direction: column; padding: 35px; text-align: center; }
        .pricing-card:hover { transform: translateY(-12px) scale(1.03); box-shadow: var(--shadow-hard); }
        .pricing-card.recommended { border: 2px solid var(--primary-color); transform: scale(1.05); box-shadow: var(--shadow-hard); z-index: 3; background: linear-gradient(to bottom, var(--white) 85%, #fff5f7); } /* Subtle gradient at bottom */
        .pricing-card.recommended:hover { transform: scale(1.08) translateY(-12px); box-shadow: 0 20px 45px rgba(255, 95, 109, 0.3); }
        .popular-badge { display: inline-block; background: var(--accent-color); color: var(--dark-color); font-size: 13px; font-weight: 700; padding: 6px 20px; border-radius: 20px; margin: -20px auto 25px auto; position: relative; z-index: 3; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .pricing-header { margin-bottom: 30px; }
        .pricing-header h3 { font-size: 30px; margin-bottom: 10px; color: var(--dark-color); }
        .pricing-header .plan-description { font-size: 14px; color: var(--text-color-medium); margin-bottom: 15px; min-height: 40px; }
        .price { font-size: 52px; font-weight: 700; color: var(--primary-color); margin-bottom: 8px; font-family: 'Montserrat', sans-serif; }
        .price span { font-size: 17px; font-weight: 500; color: #666; margin-left: 3px; }
        .pricing-card.recommended .price { color: var(--primary-color); }
        .pricing-features { list-style: none; margin-bottom: 40px; text-align: left; flex-grow: 1; padding-left: 10px; }
        .pricing-features li { margin-bottom: 18px; display: flex; align-items: center; color: var(--text-color-medium); transition: color 0.3s ease, transform 0.3s ease; }
        .pricing-features li:hover { color: var(--primary-color); transform: translateX(5px); }
        .pricing-features li i { margin-right: 12px; width: 22px; text-align: center; transition: transform 0.3s ease; color: var(--primary-color); }
        .pricing-features li:hover i { transform: scale(1.2); }
        .pricing-features li i.fa-star { color: var(--accent-color); }
        .pricing-features li:hover i.fa-star { filter: brightness(0.9); }
        .pricing-features li.disabled { color: #aaa; text-decoration: line-through; pointer-events: none; }
        .pricing-features li.disabled i { color: #ccc; }
        .pricing-card .btn { margin-top: auto; width: 100%; }
        .pricing-card-footer-note { text-align: center; margin-top: 30px; font-size: 14px; color: var(--text-color-medium); position: relative; z-index: 2; }


        /* Blog Preview Section */
        .blog-preview { background-color: var(--white); }
        .blog-preview .shape-divider.top svg path { fill: var(--light-color); /* Matches Pricing BG (assuming it used --light-color based gradient) */ }
        .blog-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; position: relative; z-index: 2; }
        .blog-card { background: var(--white); border-radius: var(--border-radius-medium); box-shadow: var(--shadow-soft); overflow: hidden; transition: var(--transition-smooth); border: 1px solid rgba(0,0,0,0.05); }
        .blog-card:hover { box-shadow: var(--shadow-hard); transform: translateY(-8px); }
        /* <!-- TODO: Replace with REAL blog post images --> */
        .blog-card-image { height: 200px; background-size: cover; background-position: center; }
        .blog-card-content { padding: 25px; }
        .blog-card-content h3 { font-size: 20px; margin-bottom: 10px; }
        .blog-card-content h3 a { color: var(--dark-color); text-decoration: none; transition: color 0.3s ease; }
        .blog-card-content h3 a:hover { color: var(--primary-color); }
        .blog-meta { font-size: 13px; color: var(--text-color-medium); margin-bottom: 15px; }
        .blog-meta span { margin-right: 15px; }
        .blog-meta i { margin-right: 5px; }
        .blog-excerpt { font-size: 15px; margin-bottom: 20px; }
        .blog-read-more { color: var(--secondary-color); font-weight: 600; text-decoration: none; }
        .blog-read-more:hover { text-decoration: underline; }


        /* CTA Section */
        .cta { background: var(--primary-gradient); color: var(--white); text-align: center; padding: 100px 0; position: relative; } /* Increased padding */
        .cta .shape-divider.top svg path { fill: var(--white); /* Matches Blog BG */ }
        .cta h2 { font-size: 40px; margin-bottom: 25px; text-shadow: 0 2px 8px rgba(0,0,0,0.15); color: var(--white); }
        .cta p { font-size: 19px; margin-bottom: 35px; max-width: 650px; margin-left: auto; margin-right: auto; opacity: 0.9; line-height: 1.7; color: rgba(255,255,255,0.9); }
        .cta .btn { background: var(--white); color: var(--primary-color); box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1); padding: 15px 35px; font-size: 17px; }
        .cta .btn:hover, .cta .btn:focus { background: var(--light-color); box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); color: var(--dark-color); transform: translateY(-7px) scale(1.05); }

        /* Footer */
        footer { background-color: var(--dark-color); color: rgba(255,255,255,0.75); padding: 80px 0 40px; text-align: center; font-size: 15px; position: relative; }
        .footer .shape-divider.top svg path { fill: var(--primary-color); /* Approximation of CTA gradient dominant color */ } /* This can be tricky with gradients */
        .footer-content { display: flex; flex-direction: column; align-items: center; gap: 35px; margin-bottom: 40px; }
        .footer-logo .logo-symbol { color: var(--primary-light); }
        .footer-logo .logo-text { background: linear-gradient(135deg, #FF5F6D 0%, #FFC371 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; }
        .footer-logo span { color: var(--secondary-light); }
        .footer-links ul { list-style: none; display: flex; flex-wrap: wrap; justify-content: center; gap: 25px; }
        .footer-links a { color: rgba(255,255,255,0.75); text-decoration: none; transition: color 0.3s ease, letter-spacing 0.3s ease; }
        .footer-links a:hover, .footer-links a:focus { color: var(--white); letter-spacing: 0.5px; outline: none; }
        .social-links a { color: rgba(255,255,255,0.75); font-size: 26px; margin: 0 12px; transition: color 0.3s ease, transform 0.3s ease; display: inline-block; }
        .social-links a:hover, .social-links a:focus { color: var(--primary-color); transform: translateY(-5px) scale(1.1); outline: none; }
        .copyright { font-size: 14px; border-top: 1px solid rgba(255,255,255,0.15); padding-top: 40px; margin-top: 40px; }

        /* Responsive Design Adjustments (largely same as before, ensure shape dividers are not problematic) */
        @media (max-width: 992px) {
            .container { width: 95%; }
            header nav ul { position: absolute; top: 100%; left: 0; right: 0; background: var(--white); flex-direction: column; max-height: 0; overflow: hidden; transition: max-height 0.5s ease-in-out; box-shadow: var(--shadow-hard); border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; }
            header nav.active ul { max-height: 60vh; padding-bottom: 15px; }
            header nav ul li { margin-left: 0; width: 100%; text-align: center; }
            header nav ul li a { display: block; padding: 18px; border-bottom: 1px solid #eee; }
            header nav ul li:last-child a { border-bottom: none; }
            header nav ul li a.btn { margin: 10px auto; display: inline-block; width: auto; }
            .hamburger { display: block; }
            .hero h1 { font-size: 44px; } .hero p { font-size: 19px; }
            .process-timeline { margin-top: 50px; } .process-timeline::before { display: none; }
            .process-step { flex-basis: 100%; max-width: 450px; margin: 0 auto 40px auto; }
            .features-grid, .success-grid, .pricing-grid, .blog-grid { grid-template-columns: 1fr; gap: 30px; }
            .pricing-card.recommended { transform: scale(1); } .pricing-card.recommended:hover { transform: translateY(-10px); }
            .swiper-button-next, .swiper-button-prev { width: 40px; height: 40px; }
            .swiper-button-next::after, .swiper-button-prev::after { font-size: 16px; }
            .swiper-button-prev { left: 5px; } .swiper-button-next { right: 5px; }
            #as-seen-on { padding: 60px 0; }
            .media-logos { gap: 20px 30px; }
            .media-logos img { max-height: 35px; }
            .shape-divider svg { height: 50px; /* Reduce height on smaller screens */ }
            .testimonials, .cta { padding-top: 80px; padding-bottom: 80px; } /* Adjust padding with dividers */
        }
        @media (min-width: 993px) { .process-timeline::before { display: block; } }
        @media (max-width: 768px) {
            h1 { font-size: 38px; } h2, .section-title h2 { font-size: 34px; } h3 { font-size: 22px; }
            .hero h1 { font-size: 38px; } .hero p { font-size: 17px; }
            .hero-buttons { flex-direction: column; align-items: center; gap: 15px; }
            .hero-buttons .btn { width: 90%; max-width: 300px; text-align: center; }
            .section-title .subtitle { font-size: 17px; }
            .testimonial-card { padding: 35px 25px; min-height: auto; }
            .testimonial-author { flex-direction: column; align-items: center; text-align: center; }
            .author-image { margin-right: 0; margin-bottom: 20px; }
            .pricing-tabs { flex-direction: column; gap: 12px; align-items: stretch; width: 90%; padding: 10px; }
            .pricing-tab { border-radius: 50px !important; margin: 0; text-align: center; }
            .footer-content { gap: 25px; }
            .footer-links ul { flex-direction: column; gap: 12px; }
            .social-links a { font-size: 24px; margin: 0 10px; }
            .swiper-button-next, .swiper-button-prev { display: none; }
            .swiper-container { padding-bottom: 40px; }
            .testimonial-slider { overflow: hidden; }
            .media-logos img { max-height: 30px; }
            .shape-divider svg { height: 40px; }
             .testimonials, .cta { padding-top: 60px; padding-bottom: 60px; }
        }

    </style>
</head>
<body>

    <header id="main-header">
        <!-- Header HTML remains the same -->
        <div class="container header-container">
            <a href="#hero" class="logo"> <i class="fas fa-heartbeat logo-symbol"></i>
                <span class="logo-text">Vaiva<span>hik</span></span>
            </a>
            <nav id="main-nav">
                <ul>
                    <li><a href="#hero">Home</a></li>
                    <li><a href="#features">Why Us</a></li>
                    <li><a href="#how-it-works">How It Works</a></li>
                    <li><a href="#success-stories">Success Stories</a></li>
                    <li><a href="#pricing">Plans</a></li>
                    <li><a href="#blog-preview">Blog</a></li>
                    <li><a href="#" class="btn btn-primary btn-small">Join Free</a></li>
                </ul>
            </nav>
            <button class="hamburger" id="hamburger-icon" aria-label="Toggle Menu">
                <div class="line1"></div> <div class="line2"></div> <div class="line3"></div>
            </button>
        </div>
    </header>

    <section id="hero" class="hero">
        <!-- Hero HTML remains the same -->
        <div class="floating-hearts" id="floating-hearts-container"></div>
        <div class="container">
            <div class="hero-content animate__animated" data-animation="animate__fadeInUp">
                <h1>Begin Your Journey to Forever, Together.</h1>
                <p>Discover meaningful connections with Vaivahik, where modern AI meets timeless Indian traditions to help families find the perfect match.</p>
                <div class="hero-buttons">
                    <a href="#pricing" class="btn btn-primary">Explore Membership Plans</a>
                    <a href="#how-it-works" class="btn btn-outline">Discover Your Path to Partnership</a>
                </div>
            </div>
        </div>
    </section>

    <section id="features" class="features">
        <div class="section-content-wrapper">
            <div class="container">
                <div class="section-title">
                    <h2>The Vaivahik Advantage</h2>
                    <p class="subtitle">Blending technology and tradition to create compatible, lasting relationships with trust and understanding.</p>
                </div>
                <div class="features-grid">
                    <div class="feature-card animate__animated" data-animation="animate__fadeInUp">
                        <!-- TODO: Replace with YOUR OWN custom icon for "Intelligent Matching" -->
                        <img src="https://placehold.co/80x80/FF5F6D/FFFFFF/svg?text=AI%0AMatch&font=montserrat" alt="Intelligent Compatibility Matching Visual" class="feature-visual">
                        <h3>Intelligent Compatibility Matching</h3>
                        <p>Our AI delves deeper than surface-level details, analyzing core values, lifestyle compatibility, and long-term goals for truly meaningful matches. We consider personality traits, life aspirations, and family values to ensure deeper connection.</p>
                    </div>
                    <div class="feature-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.1s">
                         <!-- TODO: Replace with YOUR OWN custom icon for "Verified Profiles" -->
                        <img src="https://placehold.co/80x80/8A2BE2/FFFFFF/svg?text=Verified%0ASecure&font=montserrat" alt="Verified Profile Visual" class="feature-visual">
                        <h3>Verified Profiles, Secure Platform</h3>
                        <p>Your safety is paramount. We ensure profile authenticity through rigorous verification processes within a secure, privacy-focused environment using SSL encryption and data protection measures.</p>
                         <div class="security-privacy-info">
                            <i class="fas fa-shield-alt"></i><span>SSL Encrypted</span>
                            <i class="fas fa-lock"></i><span>Data Protected</span>
                        </div>
                    </div>
                    <div class="feature-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.2s">
                         <!-- TODO: Replace with YOUR OWN custom icon for "Family Focus" -->
                         <img src="https://placehold.co/80x80/FFC371/2D3047/svg?text=Family%0AConnect&font=montserrat" alt="Family Focused Visual" class="feature-visual">
                        <h3>Family-Centric Approach</h3>
                        <p>Understanding the importance of family in Indian culture, our platform facilitates respectful communication and provides tools for involving loved ones in the journey, if you choose.</p>
                    </div>
                     <div class="feature-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.3s">
                          <!-- TODO: Replace with YOUR OWN custom icon for "Privacy Control" -->
                          <img src="https://placehold.co/80x80/4CAF50/FFFFFF/svg?text=Privacy%0AControl&font=montserrat" alt="Privacy Control Visual" class="feature-visual">
                         <h3>Complete Privacy Control</h3>
                         <p>Manage your visibility with granular privacy settings. You decide who sees your profile details, photos, and when you wish to share contact information. Your search, your terms.</p>
                     </div>
                </div>
            </div>
        </div>
    </section>

    <section id="how-it-works" class="how-it-works">
        <!-- TODO: Customize this SVG to be a gentle wave or curve. Fill color should match the BG of the previous section (Features). -->
        <div class="shape-divider top">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
            </svg>
        </div>
        <div class="section-content-wrapper">
            <div class="container">
                <div class="section-title">
                    <h2>Your Path to Partnership: Simple & Clear</h2>
                    <p class="subtitle">Follow these easy steps, supported by our technology and team, to find your ideal life partner.</p>
                </div>
                <div class="process-timeline">
                    <!-- Process Steps HTML remains the same -->
                    <div class="process-step animate__animated" data-animation="animate__fadeInUp"><div class="step-visual"><i class="fas fa-user-edit step-icon"></i><div class="step-number">1</div></div><h3>Craft Your Story</h3><p>Register and create a comprehensive profile that truly reflects your personality, aspirations, and partner preferences.</p></div>
                    <div class="process-step animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.1s"><div class="step-visual"><i class="fas fa-atom step-icon"></i><div class="step-number">2</div></div><h3>Discover Matches</h3><p>Receive carefully curated, AI-driven match suggestions based on deep compatibility analysis of values and lifestyle.</p></div>
                    <div class="process-step animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.2s"><div class="step-visual"><i class="fas fa-comments step-icon"></i><div class="step-number">3</div></div><h3>Connect Confidently</h3><p>Express interest in profiles that resonate with you and initiate conversations through our secure messaging platform.</p></div>
                    <div class="process-step animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.3s"><div class="step-visual"><i class="fas fa-handshake step-icon"></i><div class="step-number">4</div></div><h3>Build Your Future</h3><p>Nurture connections, involve family members respectfully when the time is right, and make informed decisions together.</p></div>
                </div>
            </div>
        </div>
    </section>

    <section id="testimonials" class="testimonials">
        <!-- TODO: Customize this SVG. Fill color should match BG of previous section (How It Works). -->
        <div class="shape-divider top">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.39-16.72-168.19-17.73-250.45-.39-55.85,11.57-111.91,31.79-172.44,42.59A271.42,271.42,0,0,1,122.47,53.37C58.64,53.37,0,0,0,0V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" class="shape-fill"></path>
            </svg>
        </div>
        <div class="section-content-wrapper">
            <div class="container">
                <div class="section-title">
                    <h2>Real Couples, Real Connections</h2>
                    <p class="subtitle">Don't just take our word for it. Hear from members who found happiness with Vaivahik.</p>
                </div>
                <div class="testimonial-slider swiper-container animate__animated" data-animation="animate__zoomIn">
                    <div class="swiper-wrapper">
                        <!-- Testimonial cards HTML remains the same -->
                        <div class="swiper-slide"><div class="testimonial-card"><p class="testimonial-text">"Finding someone who truly understood our family's values felt impossible online... Vaivahik's AI matching was incredibly insightful... Happily married now!"</p><div class="testimonial-author"><div class="author-image" style="background-image: url('https://picsum.photos/seed/testiA/75/75')"></div><div class="author-info"><h4>Priya & Rohan S.</h4><p>Found Each Other: 2024</p></div></div></div></div>
                        <div class="swiper-slide"><div class="testimonial-card"><p class="testimonial-text">"As busy professionals... Vaivahik's verified profiles and focused matches saved us so much time and led us to a wonderful connection..."</p><div class="testimonial-author"><div class="author-image" style="background-image: url('https://picsum.photos/seed/testiB/75/75')"></div><div class="author-info"><h4>Amit & Kavita M.</h4><p>Found Each Other: 2025</p></div></div></div></div>
                        <div class="swiper-slide"><div class="testimonial-card"><p class="testimonial-text">"The emphasis on privacy and the ability to involve family comfortably made all the difference... perfect blend of modern technology and traditional values..."</p><div class="testimonial-author"><div class="author-image" style="background-image: url('https://picsum.photos/seed/testiC/75/75')"></div><div class="author-info"><h4>Sunita & Naveen P.</h4><p>Found Each Other: 2024</p></div></div></div></div>
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </div>
            </div>
        </div>
        <!-- TODO: Customize this SVG. Fill color should match BG of next section (Success Stories). -->
        <div class="shape-divider bottom">
             <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.39-16.72-168.19-17.73-250.45-.39-55.85,11.57-111.91,31.79-172.44,42.59A271.42,271.42,0,0,1,122.47,53.37C58.64,53.37,0,0,0,0V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z" class="shape-fill"></path>
            </svg>
        </div>
    </section>

    <section id="success-stories" class="success-stories">
        <!-- Top shape divider is handled by Testimonials bottom shape -->
        <div class="section-content-wrapper">
            <div class="container">
                <div class="section-title">
                    <h2>Celebrating New Beginnings</h2>
                    <p class="subtitle">Inspiring stories of love and partnership fostered through the Vaivahik platform.</p>
                </div>
                <div class="success-grid">
                    <!-- Success cards HTML remains the same -->
                    <div class="success-card animate__animated" data-animation="animate__fadeInUp"><div class="success-image" style="background-image: url('https://picsum.photos/seed/successA/400/260');"><div class="success-overlay"><h3 class="success-names">Anika & Sameer</h3></div></div><div class="success-content"><div class="success-meta"><span class="meta-item"><i class="fas fa-calendar-check"></i> Connected: Aug 2024</span><span class="meta-item"><i class="fas fa-ring"></i> Married: Feb 2025</span></div><p class="success-story">Despite living miles apart, Vaivahik's platform brought us together. The detailed profiles and compatibility insights were key...</p><a href="#" class="read-more">Read Their Full Story <i class="fas fa-arrow-right"></i></a></div></div>
                    <div class="success-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.1s"><div class="success-image" style="background-image: url('https://picsum.photos/seed/successB/400/260');"><div class="success-overlay"><h3 class="success-names">Vikram & Meera</h3></div></div><div class="success-content"><div class="success-meta"><span class="meta-item"><i class="fas fa-calendar-check"></i> Connected: Sep 2024</span><span class="meta-item"><i class="fas fa-user-friends"></i> Families Met: Dec 2024</span></div><p class="success-story">Finding someone who shared not just interests but core life values felt serendipitous. Vaivahik facilitated our journey beautifully.</p><a href="#" class="read-more">Read Their Full Story <i class="fas fa-arrow-right"></i></a></div></div>
                    <div class="success-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.2s"><div class="success-image" style="background-image: url('https://picsum.photos/seed/successC/400/260');"><div class="success-overlay"><h3 class="success-names">Deepa & Arjun</h3></div></div><div class="success-content"><div class="success-meta"><span class="meta-item"><i class="fas fa-calendar-check"></i> Connected: Jul 2024</span><span class="meta-item"><i class="fas fa-ring"></i> Married: Mar 2025</span></div><p class="success-story">The platform's respect for family involvement and privacy controls allowed us to navigate the process comfortably and confidently.</p><a href="#" class="read-more">Read Their Full Story <i class="fas fa-arrow-right"></i></a></div></div>
                </div>
            </div>
        </div>
        <!-- TODO: Customize this SVG. Fill color should match BG of next section (As Seen On). -->
        <div class="shape-divider bottom">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.3-35.83,69.26-2.52,138.3,13.18,207.4,35.83s136.33,33.31,206.3,35.83c70.05,2.52,138.3-13.18,207.4-35.83s136.33-33.31,206.3-35.83L1200,0V120H0Z" class="shape-fill"></path>
            </svg>
        </div>
    </section>

    <section id="as-seen-on">
        <!-- Top shape divider handled by Success Stories bottom shape -->
        <div class="section-content-wrapper">
            <div class="container">
                <h3>Trusted by Families & Featured In</h3>
                <div class="media-logos">
                    <!-- TODO: Replace with actual media SVG logos -->
                    <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+1&font=montserrat" alt="Media Partner 1 Logo">
                    <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+2&font=montserrat" alt="Media Partner 2 Logo">
                    <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+3&font=montserrat" alt="Media Partner 3 Logo">
                    <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+4&font=montserrat" alt="Media Partner 4 Logo">
                    <img src="https://placehold.co/150x45/cccccc/999999/svg?text=Media+5&font=montserrat" alt="Media Partner 5 Logo">
                </div>
            </div>
        </div>
    </section>

    <section id="pricing" class="pricing">
        <!-- TODO: Customize this SVG. Fill color should match BG of previous section (As Seen On). -->
        <div class="shape-divider top">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                 <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.3-35.83,69.26-2.52,138.3,13.18,207.4,35.83s136.33,33.31,206.3,35.83c70.05,2.52,138.3-13.18,207.4-35.83s136.33-33.31,206.3-35.83L1200,0V120H0Z" class="shape-fill"></path>
            </svg>
        </div>
        <div class="section-content-wrapper">
            <div class="container">
                <div class="section-title">
                    <h2>Invest in Your Future Happiness</h2>
                    <p class="subtitle">Choose a plan that empowers your search for a compatible life partner on Vaivahik.</p>
                </div>
                <div class="pricing-tabs">
                    <span class="pricing-tab active" data-plan="monthly">Monthly Billing</span>
                    <span class="pricing-tab" data-plan="yearly">Annual Billing (Save 25%)</span>
                </div>
                <div class="pricing-grid">
                    <!-- Pricing cards HTML remains the same -->
                    <div class="pricing-card animate__animated" data-animation="animate__fadeInUp"><div class="pricing-header"><h3>Discover</h3><p class="plan-description">Start exploring and see potential matches.</p><p class="price">Free</p></div><ul class="pricing-features"><li><i class="fas fa-check"></i> Create Your Profile</li><li><i class="fas fa-check"></i> Receive AI Match Suggestions</li><li><i class="fas fa-check"></i> Browse Limited Profiles</li><li class="disabled"><i class="fas fa-times"></i> Send Unlimited Interests</li><li class="disabled"><i class="fas fa-times"></i> View Contact Information</li><li class="disabled"><i class="fas fa-times"></i> Enhanced Privacy Controls</li></ul><a href="#" class="btn btn-outline">Register for Free</a></div>
                    <div class="pricing-card recommended animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.1s"><div class="popular-badge">Most Popular</div><div class="pricing-header"><h3>Connect</h3><p class="plan-description">Ideal for actively searching and making connections.</p><p class="price">$49<span>/mo*</span></p></div><ul class="pricing-features"><li><i class="fas fa-check"></i> All Discover Features</li><li><i class="fas fa-check"></i> Send Unlimited Interests</li><li><i class="fas fa-check"></i> Initiate Secure Chats</li><li><i class="fas fa-check"></i> View Verified Contact Details</li><li><i class="fas fa-check"></i> Enhanced Privacy Options</li><li><i class="fas fa-check"></i> Profile Performance Insights</li></ul><a href="#" class="btn btn-primary">Choose Connect Plan</a></div>
                    <div class="pricing-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.2s"><div class="pricing-header"><h3>Thrive</h3><p class="plan-description">For personalized guidance and maximum visibility.</p><p class="price">$99<span>/mo*</span></p></div><ul class="pricing-features"><li><i class="fas fa-check"></i> All Connect Features</li><li><i class="fas fa-star"></i> Dedicated Relationship Advisor</li><li><i class="fas fa-star"></i> Profile Highlighting & Boost</li><li><i class="fas fa-star"></i> Advanced Compatibility Reports</li><li><i class="fas fa-star"></i> Priority Customer Support</li><li><i class="fas fa-star"></i> Early Access to New Features</li></ul><a href="#" class="btn btn-secondary">Choose Thrive Plan</a></div>
                </div>
                <p class="pricing-card-footer-note"> *Prices shown for monthly billing. Choose Annual Billing tab for discounted rates. </p>
            </div>
        </div>
    </section>

    <section id="blog-preview" class="blog-preview">
        <!-- TODO: Customize this SVG. Fill color should match BG of previous section (Pricing). -->
        <div class="shape-divider top">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M602.45,3.86h0S572.9,116.24,281.94,120H923C632,116.24,602.45,3.86,602.45,3.86Z" class="shape-fill"></path> <!-- Example simple curve -->
            </svg>
        </div>
        <div class="section-content-wrapper">
            <div class="container">
                <div class="section-title">
                    <h2>Insights & Stories</h2>
                    <p class="subtitle">Explore our latest articles on relationships, matchmaking, and Vaivahik journeys.</p>
                </div>
                <div class="blog-grid">
                    <!-- Blog cards HTML remains the same -->
                    <div class="blog-card animate__animated" data-animation="animate__fadeInUp"><div class="blog-card-image" style="background-image: url('https://picsum.photos/seed/blogA/400/200');"></div><div class="blog-card-content"><div class="blog-meta"><span><i class="fas fa-calendar-alt"></i> May 3, 2024</span><span><i class="fas fa-user"></i> By Vaivahik Team</span></div><h3><a href="#">Navigating Family Introductions: A Vaivahik Guide</a></h3><p class="blog-excerpt">Meeting the family is a significant step. Here are some tips to make it a smooth and positive experience...</p><a href="#" class="read-more blog-read-more">Read More →</a></div></div>
                    <div class="blog-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.1s"><div class="blog-card-image" style="background-image: url('https://picsum.photos/seed/blogB/400/200');"></div><div class="blog-card-content"><div class="blog-meta"><span><i class="fas fa-calendar-alt"></i> April 25, 2024</span><span><i class="fas fa-user"></i> By Dr. Relationship Expert</span></div><h3><a href="#">The Science of AI in Modern Matchmaking</a></h3><p class="blog-excerpt">How can algorithms help find love? We delve into the technology behind Vaivahik's smart matching...</p><a href="#" class="read-more blog-read-more">Read More →</a></div></div>
                    <div class="blog-card animate__animated" data-animation="animate__fadeInUp" data-wow-delay="0.2s"><div class="blog-card-image" style="background-image: url('https://picsum.photos/seed/blogC/400/200');"></div><div class="blog-card-content"><div class="blog-meta"><span><i class="fas fa-calendar-alt"></i> April 18, 2024</span><span><i class="fas fa-ring"></i> Success Story</span></div><h3><a href="#">From AI Match to "I Do": Anika & Sameer's Journey</a></h3><p class="blog-excerpt">Read the in-depth story of how Anika and Sameer found their happily ever after through Vaivahik...</p><a href="#" class="read-more blog-read-more">Read More →</a></div></div>
                </div>
            </div>
        </div>
    </section>

    <section class="cta">
        <!-- TODO: Customize this SVG. Fill color should match BG of previous section (Blog Preview). -->
        <div class="shape-divider top">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                <path d="M1200 120L0 16.48 0 0 1200 0 1200 120z" class="shape-fill"></path> <!-- Simple Angle -->
            </svg>
        </div>
        <div class="section-content-wrapper">
            <div class="container">
                <h2 class="animate__animated" data-animation="animate__pulse">Your Search for a Life Partner Starts Here.</h2>
                <p class="animate__animated" data-animation="animate__fadeInUp">Join the Vaivahik community today. Create your profile for free and discover how our unique blend of technology and tradition can help you find lasting happiness.</p>
                <a href="#pricing" class="btn btn-secondary animate__animated" data-animation="animate__swing">Create Your Free Profile Now</a>
            </div>
        </div>
    </section>

    <footer id="footer"> <!-- Added ID for potential targeting -->
        <!-- TODO: Customize this SVG. The fill color here is tricky because CTA is a gradient.
             You might use a solid color from the gradient (e.g., var(--primary-color)) or var(--dark-color) itself.
             Or, make the footer overlap the CTA slightly if the shapes allow. -->
        <div class="shape-divider top">
            <svg data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none">
                 <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" class="shape-fill"></path>
            </svg>
        </div>
         <div class="section-content-wrapper">
            <div class="container">
                <div class="footer-content">
                    <!-- Footer content HTML remains the same -->
                    <a href="#hero" class="logo footer-logo"> <i class="fas fa-heartbeat logo-symbol"></i> <span class="logo-text">Vaiva<span>hik</span></span> </a> <div class="footer-links"> <ul> <li><a href="#">About Vaivahik</a></li><li><a href="#">Contact Us</a></li><li><a href="#">Privacy Policy</a></li><li><a href="#">Terms of Service</a></li><li><a href="#">Help & FAQ</a></li><li><a href="#blog-preview">Blog</a></li> </ul> </div> <div class="social-links"> <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a> <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a> <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a> <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a> </div>
                </div>
                <div class="copyright">
                    © <span id="current-year"></span> Vaivahik Matrimony Services. All Rights Reserved. <br class="d-block d-sm-none"> Built with <i class="fas fa-heart" style="color: var(--primary-color);"></i> for meaningful connections.
                </div>
            </div>
        </div>
     </footer>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        // Javascript remains the same as the previous version
        document.addEventListener('DOMContentLoaded', function() {
            const header = document.getElementById('main-header');
            const nav = document.getElementById('main-nav');
            const hamburger = document.getElementById('hamburger-icon');

            window.addEventListener('scroll', () => { header.classList.toggle('scrolled', window.scrollY > 50); });
            if (hamburger && nav) { hamburger.addEventListener('click', () => { hamburger.classList.toggle('active'); nav.classList.toggle('active'); }); }
            const navLinks = nav?.querySelectorAll('ul li a');
            navLinks?.forEach(link => { link.addEventListener('click', (e) => { if ((link.getAttribute('href').startsWith('#') || !link.getAttribute('href').startsWith('#') && link.closest('nav.active') ) && nav.classList.contains('active')) { hamburger?.classList.remove('active'); nav.classList.remove('active'); } }); });
            const heartsContainer = document.getElementById('floating-hearts-container');
            if (heartsContainer) { const n = 15; for (let i = 0; i < n; i++) createHeart(); }
            function createHeart() { const h = document.createElement('div'); h.classList.add('heart'); h.style.left = Math.random()*100+'vw'; h.style.animationDuration = (Math.random()*10+10)+'s'; h.style.animationDelay = Math.random()*5+'s'; const s=Math.random()*.5+.5; h.style.width=(30*s)+'px'; h.style.height=(30*s)+'px'; h.style.opacity=Math.random()*.4+.3; heartsContainer?.appendChild(h); h.addEventListener('animationend', () => { h.remove(); if (heartsContainer && heartsContainer.children.length<20) createHeart(); }, { once: true }); }
            const yearSpan = document.getElementById('current-year');
            if (yearSpan) yearSpan.textContent = new Date().getFullYear();
            const pricingTabs = document.querySelectorAll('.pricing-tab');
            const pricingCardsPriceEls = document.querySelectorAll('.pricing-card .price');
            const monthlyPrices = { discover: 'Free', connect: '$49<span>/mo*</span>', thrive: '$99<span>/mo*</span>' };
            const yearlyPrices = { discover: 'Free', connect: '$441<span>/yr*</span>', thrive: '$891<span>/yr*</span>' };
            const planKeys = ['discover', 'connect', 'thrive'];
            pricingTabs.forEach(tab => { tab.addEventListener('click', () => { pricingTabs.forEach(t => t.classList.remove('active')); tab.classList.add('active'); const p = tab.getAttribute('data-plan') || 'monthly'; const cP = (p === 'yearly') ? yearlyPrices : monthlyPrices; pricingCardsPriceEls.forEach((el, i) => { const pk = planKeys[i]; if (cP[pk] !== undefined) el.innerHTML = cP[pk]; }); }); });
            pricingCardsPriceEls.forEach((el, i) => { const pk = planKeys[i]; if (monthlyPrices[pk] !== undefined) el.innerHTML = monthlyPrices[pk]; });
            if (typeof Swiper !== 'undefined') { const swiper = new Swiper('.swiper-container', { loop: true, slidesPerView: 1, spaceBetween: 30, autoplay: { delay: 6500, disableOnInteraction: false }, pagination: { el: '.swiper-pagination', clickable: true }, navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' }, grabCursor: true, effect: 'fade', fadeEffect: { crossFade: true }, breakpoints: { 768: { spaceBetween: 40 }, 992: { spaceBetween: 50 } } }); } else console.error("Swiper library not loaded.");
            const animatedElements = document.querySelectorAll('.animate__animated');
            const observer = new IntersectionObserver((entries, obs) => { entries.forEach(entry => { if (entry.isIntersecting) { const a = entry.target.getAttribute('data-animation')||'animate__fadeIn'; if (!entry.target.classList.contains(a.split(' ')[0])) { entry.target.classList.add(a); } obs.unobserve(entry.target); } }); }, { threshold: 0.1 }); /* Lowered threshold slightly for earlier trigger */
            animatedElements.forEach(el => { const i=el.getAttribute('data-animation'); if(i) el.classList.remove(i.split(' ')[0]); observer.observe(el); });
        });
    </script>

</body>
</html>
