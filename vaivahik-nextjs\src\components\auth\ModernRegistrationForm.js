import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Grid,
  TextField,
  Button,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Divider,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  styled,
  InputAdornment,
  IconButton,
  Autocomplete
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker, TimePicker } from '@mui/x-date-pickers';
import { format, subYears, isValid, differenceInYears } from 'date-fns';
import { enIN } from 'date-fns/locale';
import {
  Phone as PhoneIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Cake as CakeIcon,
  Height as HeightIcon,
  School as SchoolIcon,
  Work as WorkIcon,
  LocationOn as LocationIcon,
  Search as SearchIcon,
  PhotoCamera as PhotoCameraIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import { initPlacesAutocomplete } from '@/utils/googleMapsLoader';

// Constants for form validation
const MIN_AGE_FEMALE = 18;
const MIN_AGE_MALE = 21;
const MAX_AGE = 70;
const MIN_HEIGHT = 4.5;
const MAX_HEIGHT = 6.5;

// Options for select fields
const PROFILE_FOR_OPTIONS = ['Self', 'Son', 'Daughter', 'Brother', 'Sister', 'Relative', 'Friend'];
const MARITAL_STATUS_OPTIONS = ['Never Married', 'Divorced', 'Widowed', 'Awaiting Divorce'];
const BLOOD_GROUPS = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Don\'t Know'];
const EDUCATION_LEVELS = [
  'High School',
  'Diploma',
  'Bachelor\'s',
  'Master\'s',
  'Doctorate',
  'Professional Degree',
  'Other'
];
const OCCUPATION_TYPES = [
  'Private Sector',
  'Government/Public Sector',
  'Business/Self Employed',
  'Civil Services',
  'Defence',
  'Not Working',
  'Student',
  'Other'
];
const INCOME_RANGES = [
  'Below 3 Lakhs',
  '3-5 Lakhs',
  '5-7 Lakhs',
  '7-10 Lakhs',
  '10-15 Lakhs',
  '15-20 Lakhs',
  '20-30 Lakhs',
  '30-50 Lakhs',
  '50 Lakhs-1 Crore',
  'Above 1 Crore'
];
const FAMILY_TYPES = ['Nuclear', 'Joint', 'Extended'];
const FAMILY_VALUES = ['Traditional', 'Moderate', 'Liberal'];
const PARENT_STATUS = ['Alive', 'Passed Away'];
const SIBLING_COUNT = ['0', '1', '2', '3', '4', '5+'];
const SUBCASTES = [
  'Kunbi',
  'Kshatriya',
  'Deshmukh',
  '96 Kuli',
  'Maratha-Deshmukh',
  'Maratha-Kshatriya',
  'Other'
];

// Custom styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
  background: 'linear-gradient(to bottom, #ffffff, #f8f9fa)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '5px',
    background: 'var(--primary-gradient)',
  }
}));

const StyledStepper = styled(Stepper)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  '& .MuiStepLabel-root .Mui-completed': {
    color: 'var(--primary-color)',
  },
  '& .MuiStepLabel-root .Mui-active': {
    color: 'var(--primary-color)',
  },
  '& .MuiStepConnector-line': {
    borderColor: 'var(--light-color-alt)',
  },
  '& .MuiStepConnector-root.Mui-active .MuiStepConnector-line': {
    borderColor: 'var(--primary-color)',
  },
  '& .MuiStepConnector-root.Mui-completed .MuiStepConnector-line': {
    borderColor: 'var(--primary-color)',
  },
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 12,
    transition: 'all 0.3s ease',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

const StyledSelect = styled(Select)(({ theme }) => ({
  borderRadius: 12,
  '& .MuiOutlinedInput-notchedOutline': {
    transition: 'all 0.3s ease',
  },
  '&:hover .MuiOutlinedInput-notchedOutline': {
    borderColor: 'var(--primary-color)',
  },
  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
    borderColor: 'var(--primary-color)',
    borderWidth: 2,
    boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 50,
  padding: '12px 30px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: '0 4px 14px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)',
  },
  '&.MuiButton-contained': {
    background: 'var(--primary-gradient)',
  },
  '&.MuiButton-outlined': {
    borderWidth: 2,
    '&:hover': {
      borderWidth: 2,
    },
  },
}));

const StyledRadio = styled(Radio)(({ theme }) => ({
  '&.Mui-checked': {
    color: 'var(--primary-color)',
  },
}));

const StyledFormControlLabel = styled(FormControlLabel)(({ theme }) => ({
  '& .MuiFormControlLabel-label': {
    fontSize: '1rem',
  },
}));

const StyledFormLabel = styled(FormLabel)(({ theme }) => ({
  color: 'var(--text-color-dark)',
  fontWeight: 500,
  fontSize: '1rem',
  marginBottom: theme.spacing(1),
  display: 'block',
}));

const StyledDatePicker = styled(DatePicker)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 12,
    transition: 'all 0.3s ease',
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: 'var(--primary-color)',
      borderWidth: 2,
      boxShadow: '0 0 0 3px rgba(255, 95, 109, 0.2)',
    },
  },
}));

const StyledDivider = styled(Divider)(({ theme }) => ({
  margin: theme.spacing(4, 0),
  '&::before, &::after': {
    borderColor: 'rgba(0, 0, 0, 0.06)',
  },
}));

const StyledStepTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.5rem',
  fontWeight: 600,
  marginBottom: theme.spacing(3),
  color: 'var(--primary-color)',
  textAlign: 'center',
}));

const FloatingElement = styled(Box)(({ theme, position }) => ({
  position: 'absolute',
  width: '200px',
  height: '200px',
  borderRadius: '50%',
  background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.05) 0%, rgba(255, 195, 113, 0.07) 100%)',
  zIndex: 0,
  ...(position === 'top-right' && {
    top: '-100px',
    right: '-100px',
  }),
  ...(position === 'bottom-left' && {
    bottom: '-100px',
    left: '-100px',
  }),
}));

// Steps for the registration process
const steps = ['Phone Verification', 'Basic Details', 'Personal Information', 'Education & Career', 'Location & Birth Details', 'Profile & About Me'];

// Constants for form validation

const ModernRegistrationForm = ({ onRegister, loading = false, error = '', dataSource = 'mock' }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    // Phone Verification (Step 0)
    phone: '',
    otp: '',

    // Basic Details (Step 1)
    fullName: '',
    email: '',
    gender: '',
    profileFor: 'Self',
    dateOfBirth: null,
    maritalStatus: 'Never Married',

    // Personal Information (Step 2)
    heightFeet: '',
    heightInches: '',
    height: '',
    religion: 'Hindu',
    caste: 'Maratha',
    subCaste: '',
    bloodGroup: '',

    // Education & Career (Step 3)
    education: '',
    educationField: '',
    occupation: '',
    workingWith: '',
    incomeRange: '',

    // Location & Birth Details (Step 4)
    city: '',
    state: '',
    pincode: '',
    birthPlace: '',
    birthTime: null,
    gotra: '',

    // Profile Photo & About Me (Step 5)
    profilePhoto: null,
    aboutMe: ''
  });

  // Google Places API refs
  const cityInputRef = useRef(null);
  const birthPlaceInputRef = useRef(null);

  // State for OTP verification
  const [otpSent, setOtpSent] = useState(false);
  const [otpVerified, setOtpVerified] = useState(false);
  const [otpResendTimer, setOtpResendTimer] = useState(0);

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [success, setSuccess] = useState('');

  // OTP resend timer
  useEffect(() => {
    if (otpResendTimer > 0) {
      const timer = setTimeout(() => {
        setOtpResendTimer(otpResendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [otpResendTimer]);

  // Handle OTP request
  const handleRequestOTP = async () => {
    if (!formData.phone || !/^[0-9]{10}$/.test(formData.phone)) {
      setErrors({
        ...errors,
        phone: 'Please enter a valid 10-digit phone number'
      });
      return;
    }

    try {
      // Call OTP API
      const response = await fetch('/api/users/request-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ phone: formData.phone })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send OTP');
      }

      setOtpSent(true);
      setSuccess('OTP sent successfully. Please check your phone.');
      setOtpResendTimer(30); // 30 seconds cooldown

      // For development/testing, if using mock data, show the OTP
      if (data.source === 'mock' && data.otp) {
        setSuccess(`OTP sent successfully (mock). Use OTP: ${data.otp}`);
      }
    } catch (err) {
      setErrors({
        ...errors,
        phone: err.message || 'An error occurred while sending OTP'
      });
    }
  };

  // Handle OTP verification
  const handleVerifyOTP = async () => {
    if (!formData.otp || !/^[0-9]{4,6}$/.test(formData.otp)) {
      setErrors({
        ...errors,
        otp: 'Please enter a valid OTP'
      });
      return;
    }

    try {
      // Call verify OTP API
      const response = await fetch('/api/users/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phone: formData.phone,
          otp: formData.otp
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Invalid OTP');
      }

      setOtpVerified(true);
      setSuccess('Phone number verified successfully.');

      // Move to next step after OTP verification
      setTimeout(() => {
        handleNext();
      }, 1000);
    } catch (err) {
      setErrors({
        ...errors,
        otp: err.message || 'An error occurred during OTP verification'
      });
    }
  };

  // Initialize Google Places Autocomplete
  useEffect(() => {
    if (activeStep === 4) { // Location & Birth Details step
      // Initialize autocomplete for city
      const initCityAutocomplete = async () => {
        if (cityInputRef.current) {
          const options = {
            types: ['(cities)'],
            componentRestrictions: { country: 'in' }
          };

          const handleCitySelected = (place) => {
            if (place && place.address_components) {
              let city = '';
              let state = '';
              let pincode = '';

              place.address_components.forEach(component => {
                if (component.types.includes('locality')) {
                  city = component.long_name;
                }
                if (component.types.includes('administrative_area_level_1')) {
                  state = component.long_name;
                }
                if (component.types.includes('postal_code')) {
                  pincode = component.long_name;
                }
              });

              setFormData(prev => ({
                ...prev,
                city: city || place.name || '',
                state: state || '',
                pincode: pincode || ''
              }));
            }
          };

          await initPlacesAutocomplete(cityInputRef.current, options, handleCitySelected);
        }
      };

      // Initialize autocomplete for birth place
      const initBirthPlaceAutocomplete = async () => {
        if (birthPlaceInputRef.current) {
          const options = {
            types: ['(cities)'],
            componentRestrictions: { country: 'in' }
          };

          const handleBirthPlaceSelected = (place) => {
            if (place) {
              setFormData(prev => ({
                ...prev,
                birthPlace: place.name || '',
              }));
            }
          };

          await initPlacesAutocomplete(birthPlaceInputRef.current, options, handleBirthPlaceSelected);
        }
      };

      // Initialize both autocompletes
      initCityAutocomplete();
      initBirthPlaceAutocomplete();
    }
  }, [activeStep]);

  // Check if all required fields are filled for current step
  const isStepComplete = (step) => {
    switch (step) {
      case 0:
        return formData.phone && otpVerified;
      case 1:
        return formData.fullName && formData.email && formData.gender && formData.dateOfBirth;
      case 2:
        return formData.heightFeet && formData.heightInches && formData.bloodGroup;
      case 3:
        return formData.education && formData.occupation && formData.incomeRange;
      case 4:
        return formData.city && formData.state && formData.pincode && formData.birthPlace;
      case 5:
        return formData.aboutMe && formData.aboutMe.length >= 50;
      default:
        return false;
    }
  };

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Mark field as touched
    setTouched({ ...touched, [name]: true });

    // Clear error for this field if it exists
    if (errors[name]) {
      const { [name]: _, ...restErrors } = errors;
      setErrors(restErrors);
    }

    // Clear submit error if it exists
    if (errors.submit) {
      const { submit: _, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };

  // Handle date change
  const handleDateChange = (date, fieldName) => {
    setFormData({ ...formData, [fieldName]: date });
    setTouched({ ...touched, [fieldName]: true });

    // Clear error for this field if it exists
    if (errors[fieldName]) {
      const { [fieldName]: _, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };

  // Validate current step
  const validateStep = (step) => {
    let isValid = true;
    const newErrors = {};

    // Clear previous errors for this step
    Object.keys(errors).forEach(key => {
      if (key !== 'submit') {
        delete errors[key];
      }
    });

    switch (step) {
      case 0: // Phone Verification
        if (!formData.phone) {
          newErrors.phone = 'Phone number is required';
          isValid = false;
        } else if (!/^[0-9]{10}$/.test(formData.phone)) {
          newErrors.phone = 'Please enter a valid 10-digit phone number';
          isValid = false;
        }

        if (!otpVerified) {
          if (!formData.otp) {
            newErrors.otp = 'OTP is required';
            isValid = false;
          } else if (!/^[0-9]{4,6}$/.test(formData.otp)) {
            newErrors.otp = 'Please enter a valid OTP';
            isValid = false;
          }

          if (!otpSent) {
            newErrors.phone = 'Please request OTP first';
            isValid = false;
          }

          isValid = false;
        }
        break;

      case 1: // Basic Details
        if (!formData.fullName) {
          newErrors.fullName = 'Full name is required';
          isValid = false;
        } else if (formData.fullName.length < 3) {
          newErrors.fullName = 'Full name must be at least 3 characters';
          isValid = false;
        }

        if (!formData.email) {
          newErrors.email = 'Email is required';
          isValid = false;
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          newErrors.email = 'Please enter a valid email address';
          isValid = false;
        }

        if (!formData.gender) {
          newErrors.gender = 'Gender is required';
          isValid = false;
        }

        if (!formData.dateOfBirth) {
          newErrors.dateOfBirth = 'Date of birth is required';
          isValid = false;
        } else {
          // Validate age based on gender
          const today = new Date();
          const birthDate = new Date(formData.dateOfBirth);
          const age = differenceInYears(today, birthDate);
          const minAge = formData.gender === 'Female' ? MIN_AGE_FEMALE : MIN_AGE_MALE;

          if (age < minAge) {
            newErrors.dateOfBirth = `Minimum age for ${formData.gender.toLowerCase()} is ${minAge} years`;
            isValid = false;
          } else if (age > MAX_AGE) {
            newErrors.dateOfBirth = `Maximum age is ${MAX_AGE} years`;
            isValid = false;
          }
        }

        if (!formData.maritalStatus) {
          newErrors.maritalStatus = 'Marital status is required';
          isValid = false;
        }
        break;

      case 2: // Personal Information
        if (!formData.heightFeet || !formData.heightInches) {
          newErrors.height = 'Height is required';
          isValid = false;
        } else {
          const heightFeet = parseFloat(formData.heightFeet) || 0;
          const heightInches = parseFloat(formData.heightInches) || 0;
          const totalHeight = heightFeet + (heightInches / 12);

          if (totalHeight < MIN_HEIGHT || totalHeight > MAX_HEIGHT) {
            newErrors.height = `Height must be between ${MIN_HEIGHT} and ${MAX_HEIGHT} feet`;
            isValid = false;
          }
        }

        if (!formData.religion) {
          newErrors.religion = 'Religion is required';
          isValid = false;
        }

        if (!formData.caste) {
          newErrors.caste = 'Caste is required';
          isValid = false;
        }

        if (!formData.bloodGroup) {
          newErrors.bloodGroup = 'Blood group is required';
          isValid = false;
        }
        break;

      case 3: // Education & Career
        if (!formData.education) {
          newErrors.education = 'Education is required';
          isValid = false;
        }

        if (!formData.occupation) {
          newErrors.occupation = 'Occupation is required';
          isValid = false;
        }

        if (!formData.incomeRange) {
          newErrors.incomeRange = 'Income range is required';
          isValid = false;
        }
        break;

      case 4: // Location & Birth Details
        if (!formData.city) {
          newErrors.city = 'City is required';
          isValid = false;
        }

        if (!formData.state) {
          newErrors.state = 'State is required';
          isValid = false;
        }

        if (!formData.pincode) {
          newErrors.pincode = 'Pincode is required';
          isValid = false;
        } else if (!/^[0-9]{6}$/.test(formData.pincode)) {
          newErrors.pincode = 'Please enter a valid 6-digit pincode';
          isValid = false;
        }

        if (!formData.birthPlace) {
          newErrors.birthPlace = 'Birth place is required';
          isValid = false;
        }
        break;

      case 5: // Profile Photo & About Me
        if (!formData.aboutMe) {
          newErrors.aboutMe = 'Please write something about yourself (required)';
          isValid = false;
        } else if (formData.aboutMe.length < 50) {
          newErrors.aboutMe = `About me should be at least 50 characters (currently ${formData.aboutMe.length})`;
          isValid = false;
        }
        break;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Handle form submission
  const handleSubmit = () => {
    // Clear any existing errors first
    setErrors({});
    setSuccess('');

    // Validate all steps from 0 to current step
    let allValid = true;
    let firstInvalidStep = -1;
    const allErrors = {};

    // Check all steps up to current step
    for (let i = 0; i <= activeStep; i++) {
      const stepValid = validateStep(i);
      if (!stepValid) {
        allValid = false;
        if (firstInvalidStep === -1) {
          firstInvalidStep = i;
        }
      }
    }

    // Additional comprehensive validation
    const requiredFields = {
      0: { phone: formData.phone, otp: formData.otp, otpVerified },
      1: { fullName: formData.fullName, email: formData.email, gender: formData.gender, dateOfBirth: formData.dateOfBirth },
      2: { heightFeet: formData.heightFeet, heightInches: formData.heightInches, bloodGroup: formData.bloodGroup },
      3: { education: formData.education, occupation: formData.occupation, incomeRange: formData.incomeRange },
      4: { city: formData.city, state: formData.state, pincode: formData.pincode, birthPlace: formData.birthPlace },
      5: { aboutMe: formData.aboutMe }
    };

    // Check for missing required fields
    const missingFields = [];
    for (let step = 0; step <= activeStep; step++) {
      const stepFields = requiredFields[step];
      Object.entries(stepFields).forEach(([field, value]) => {
        if (!value || (field === 'aboutMe' && value.length < 50)) {
          missingFields.push({ step: step + 1, field, stepName: steps[step] });
        }
      });
    }

    if (missingFields.length > 0) {
      const firstMissing = missingFields[0];
      setErrors({
        submit: `Missing required field "${firstMissing.field}" in step ${firstMissing.step}: ${firstMissing.stepName}. Please complete all required fields before submitting.`,
        [firstMissing.field]: `This field is required`
      });
      setActiveStep(firstMissing.step - 1);
      return;
    }

    if (!allValid) {
      // Show error message and redirect to first invalid step
      setErrors(prev => ({
        ...prev,
        submit: `Please complete all required fields in step ${firstInvalidStep + 1}: ${steps[firstInvalidStep]}`
      }));
      setActiveStep(firstInvalidStep);
      return;
    }

    // Final validation for current step (step 5)
    if (!validateStep(activeStep)) {
      return;
    }

    // Call the onRegister prop with the form data
    onRegister(formData);
  };

  // Render step content
  const getStepContent = (step) => {
    switch (step) {
      case 0: // Phone Verification
        return (
          <Box>
            <StyledStepTitle>Phone Verification</StyledStepTitle>
            <Alert severity="info" sx={{ mb: 3 }}>
              We'll send a one-time password (OTP) to verify your phone number.
            </Alert>

            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.phone}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PhoneIcon fontSize="small" sx={{ mr: 1 }} />
                      Phone Number*
                    </Box>
                  </StyledFormLabel>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                    <StyledTextField
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="Enter your 10-digit phone number"
                      inputProps={{ maxLength: 10 }}
                      error={!!errors.phone}
                      helperText={errors.phone}
                      disabled={otpSent && otpVerified}
                      sx={{ flexGrow: 1 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Typography>+91</Typography>
                          </InputAdornment>
                        )
                      }}
                    />
                    <StyledButton
                      onClick={handleRequestOTP}
                      disabled={!formData.phone || otpVerified || otpResendTimer > 0}
                      variant="contained"
                      sx={{ ml: 1, minWidth: '120px', height: '56px' }}
                    >
                      {otpSent ? (otpResendTimer > 0 ? `Resend (${otpResendTimer}s)` : 'Resend OTP') : 'Get OTP'}
                    </StyledButton>
                  </Box>
                </FormControl>
              </Grid>

              {otpSent && (
                <Grid item xs={12}>
                  <FormControl fullWidth error={!!errors.otp}>
                    <StyledFormLabel>Enter OTP*</StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <StyledTextField
                        name="otp"
                        value={formData.otp}
                        onChange={handleChange}
                        placeholder="Enter the 6-digit OTP"
                        inputProps={{ maxLength: 6 }}
                        error={!!errors.otp}
                        helperText={errors.otp}
                        disabled={otpVerified}
                        sx={{ flexGrow: 1 }}
                      />
                      <StyledButton
                        onClick={handleVerifyOTP}
                        disabled={!formData.otp || otpVerified}
                        variant="contained"
                        sx={{ ml: 1, minWidth: '120px', height: '56px' }}
                      >
                        Verify OTP
                      </StyledButton>
                    </Box>
                  </FormControl>
                </Grid>
              )}

              {otpVerified && (
                <Grid item xs={12}>
                  <Alert severity="success">
                    Phone number verified successfully! You can proceed to the next step.
                  </Alert>
                </Grid>
              )}
            </Grid>
          </Box>
        );

      case 1: // Basic Details
        return (
          <Box>
            <StyledStepTitle>Basic Details</StyledStepTitle>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.fullName}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                      Full Name*
                    </Box>
                  </StyledFormLabel>
                  <StyledTextField
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleChange}
                    placeholder="Enter your full name"
                    error={!!errors.fullName}
                    helperText={errors.fullName}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.email}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <EmailIcon fontSize="small" sx={{ mr: 1 }} />
                      Email Address*
                    </Box>
                  </StyledFormLabel>
                  <StyledTextField
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="Enter your email address"
                    error={!!errors.email}
                    helperText={errors.email}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.gender}>
                  <StyledFormLabel>Gender*</StyledFormLabel>
                  <RadioGroup
                    name="gender"
                    value={formData.gender}
                    onChange={handleChange}
                    row
                  >
                    <StyledFormControlLabel value="Female" control={<StyledRadio />} label="Female" />
                    <StyledFormControlLabel value="Male" control={<StyledRadio />} label="Male" />
                  </RadioGroup>
                  {errors.gender && <FormHelperText error>{errors.gender}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Profile For*</StyledFormLabel>
                  <StyledSelect
                    name="profileFor"
                    value={formData.profileFor}
                    onChange={handleChange}
                  >
                    {PROFILE_FOR_OPTIONS.map((option) => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </StyledSelect>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.dateOfBirth}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CakeIcon fontSize="small" sx={{ mr: 1 }} />
                      Date of Birth*
                    </Box>
                  </StyledFormLabel>
                  <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enIN}>
                    <StyledDatePicker
                      value={formData.dateOfBirth}
                      onChange={(date) => handleDateChange(date, 'dateOfBirth')}
                      disableFuture
                      format="dd/MM/yyyy"
                      slotProps={{
                        textField: {
                          placeholder: "DD/MM/YYYY",
                          error: !!errors.dateOfBirth,
                          helperText: errors.dateOfBirth,
                          fullWidth: true
                        }
                      }}
                    />
                  </LocalizationProvider>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Marital Status*</StyledFormLabel>
                  <StyledSelect
                    name="maritalStatus"
                    value={formData.maritalStatus}
                    onChange={handleChange}
                  >
                    {MARITAL_STATUS_OPTIONS.map((option) => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </StyledSelect>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      case 2: // Personal Information
        return (
          <Box>
            <StyledStepTitle>Personal Information</StyledStepTitle>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.height}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <HeightIcon fontSize="small" sx={{ mr: 1 }} />
                      Height*
                    </Box>
                  </StyledFormLabel>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <StyledTextField
                        name="heightFeet"
                        value={formData.heightFeet}
                        onChange={handleChange}
                        placeholder="Feet"
                        error={!!errors.height}
                        InputProps={{
                          endAdornment: <InputAdornment position="end">ft</InputAdornment>
                        }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <StyledTextField
                        name="heightInches"
                        value={formData.heightInches}
                        onChange={handleChange}
                        placeholder="Inches"
                        error={!!errors.height}
                        InputProps={{
                          endAdornment: <InputAdornment position="end">in</InputAdornment>
                        }}
                      />
                    </Grid>
                  </Grid>
                  {errors.height && <FormHelperText error>{errors.height}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.bloodGroup}>
                  <StyledFormLabel>Blood Group*</StyledFormLabel>
                  <StyledSelect
                    name="bloodGroup"
                    value={formData.bloodGroup}
                    onChange={handleChange}
                    error={!!errors.bloodGroup}
                  >
                    <MenuItem value="">Select Blood Group</MenuItem>
                    {BLOOD_GROUPS.map((option) => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </StyledSelect>
                  {errors.bloodGroup && <FormHelperText error>{errors.bloodGroup}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Religion*</StyledFormLabel>
                  <StyledSelect
                    name="religion"
                    value={formData.religion}
                    onChange={handleChange}
                  >
                    <MenuItem value="Hindu">Hindu</MenuItem>
                    <MenuItem value="Muslim">Muslim</MenuItem>
                    <MenuItem value="Christian">Christian</MenuItem>
                    <MenuItem value="Sikh">Sikh</MenuItem>
                    <MenuItem value="Jain">Jain</MenuItem>
                    <MenuItem value="Buddhist">Buddhist</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </StyledSelect>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.caste}>
                  <StyledFormLabel>Caste*</StyledFormLabel>
                  <StyledTextField
                    name="caste"
                    value={formData.caste}
                    onChange={handleChange}
                    placeholder="e.g., Maratha"
                    error={!!errors.caste}
                    helperText={errors.caste}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Sub-caste (Optional)</StyledFormLabel>
                  <StyledSelect
                    name="subCaste"
                    value={formData.subCaste}
                    onChange={handleChange}
                  >
                    <MenuItem value="">Select Sub-caste</MenuItem>
                    {SUBCASTES.map((option) => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </StyledSelect>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      case 3: // Education & Career
        return (
          <Box>
            <StyledStepTitle>Education & Career</StyledStepTitle>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.education}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <SchoolIcon fontSize="small" sx={{ mr: 1 }} />
                      Highest Education*
                    </Box>
                  </StyledFormLabel>
                  <StyledSelect
                    name="education"
                    value={formData.education}
                    onChange={handleChange}
                    error={!!errors.education}
                  >
                    <MenuItem value="">Select Education</MenuItem>
                    {EDUCATION_LEVELS.map((option) => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </StyledSelect>
                  {errors.education && <FormHelperText error>{errors.education}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Field of Study (Optional)</StyledFormLabel>
                  <StyledTextField
                    name="educationField"
                    value={formData.educationField}
                    onChange={handleChange}
                    placeholder="e.g., Computer Science, Medicine"
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.occupation}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <WorkIcon fontSize="small" sx={{ mr: 1 }} />
                      Occupation*
                    </Box>
                  </StyledFormLabel>
                  <StyledSelect
                    name="occupation"
                    value={formData.occupation}
                    onChange={handleChange}
                    error={!!errors.occupation}
                  >
                    <MenuItem value="">Select Occupation</MenuItem>
                    {OCCUPATION_TYPES.map((option) => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </StyledSelect>
                  {errors.occupation && <FormHelperText error>{errors.occupation}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Company/Organization (Optional)</StyledFormLabel>
                  <StyledTextField
                    name="workingWith"
                    value={formData.workingWith}
                    onChange={handleChange}
                    placeholder="Where do you work?"
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.incomeRange}>
                  <StyledFormLabel>Annual Income*</StyledFormLabel>
                  <StyledSelect
                    name="incomeRange"
                    value={formData.incomeRange}
                    onChange={handleChange}
                    error={!!errors.incomeRange}
                  >
                    <MenuItem value="">Select Income Range</MenuItem>
                    {INCOME_RANGES.map((option) => (
                      <MenuItem key={option} value={option}>{option}</MenuItem>
                    ))}
                  </StyledSelect>
                  {errors.incomeRange && <FormHelperText error>{errors.incomeRange}</FormHelperText>}
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      case 4: // Location & Birth Details
        return (
          <Box>
            <StyledStepTitle>Location & Birth Details</StyledStepTitle>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.city}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <LocationIcon fontSize="small" sx={{ mr: 1 }} />
                      City*
                    </Box>
                  </StyledFormLabel>
                  <StyledTextField
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    placeholder="Search for your city"
                    error={!!errors.city}
                    helperText={errors.city || "Use Google Places to search for your city"}
                    inputRef={cityInputRef}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.state}>
                  <StyledFormLabel>State*</StyledFormLabel>
                  <StyledTextField
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    placeholder="Your state"
                    error={!!errors.state}
                    helperText={errors.state}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.pincode}>
                  <StyledFormLabel>Pincode*</StyledFormLabel>
                  <StyledTextField
                    name="pincode"
                    value={formData.pincode}
                    onChange={handleChange}
                    placeholder="6-digit pincode"
                    inputProps={{ maxLength: 6 }}
                    error={!!errors.pincode}
                    helperText={errors.pincode}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, fontWeight: 600 }}>
                  Birth Details
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.birthPlace}>
                  <StyledFormLabel>Birth Place*</StyledFormLabel>
                  <StyledTextField
                    name="birthPlace"
                    value={formData.birthPlace}
                    onChange={handleChange}
                    placeholder="City/Town where you were born"
                    error={!!errors.birthPlace}
                    helperText={errors.birthPlace || "Use Google Places to search for your birth place"}
                    inputRef={birthPlaceInputRef}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Birth Time (Optional)</StyledFormLabel>
                  <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enIN}>
                    <TimePicker
                      value={formData.birthTime || null}
                      onChange={(time) => handleDateChange(time, 'birthTime')}
                      slotProps={{
                        textField: {
                          placeholder: "HH:MM",
                          fullWidth: true,
                          sx: {
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 12,
                            }
                          }
                        }
                      }}
                    />
                  </LocalizationProvider>
                  <FormHelperText>
                    Helpful for horoscope matching
                  </FormHelperText>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <StyledFormLabel>Gotra (Optional)</StyledFormLabel>
                  <StyledTextField
                    name="gotra"
                    value={formData.gotra}
                    onChange={handleChange}
                    placeholder="Enter your gotra"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      case 5: // Profile Photo & About Me
        return (
          <Box>
            <StyledStepTitle>Profile Photo & About Me</StyledStepTitle>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box
                  sx={{
                    border: '2px dashed var(--light-color-alt)',
                    borderRadius: 3,
                    p: 3,
                    textAlign: 'center',
                    mb: 2,
                    background: 'rgba(255, 95, 109, 0.03)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      borderColor: 'var(--primary-color)',
                      background: 'rgba(255, 95, 109, 0.05)',
                    }
                  }}
                >
                  <PhotoCameraIcon sx={{ fontSize: 40, color: 'var(--primary-color)', mb: 1, opacity: 0.7 }} />
                  <Typography variant="subtitle1" gutterBottom fontWeight={600}>
                    Upload Profile Photo
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Profiles with photos get 10x more responses
                  </Typography>
                  <StyledButton
                    variant="outlined"
                    component="label"
                    sx={{ mt: 2 }}
                  >
                    Choose Photo
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={(e) => {
                        if (e.target.files && e.target.files[0]) {
                          setFormData({ ...formData, profilePhoto: e.target.files[0] });
                        }
                      }}
                    />
                  </StyledButton>
                  {formData.profilePhoto && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Selected: {formData.profilePhoto.name}
                    </Typography>
                  )}
                </Box>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth error={!!errors.aboutMe}>
                  <StyledFormLabel>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <DescriptionIcon fontSize="small" sx={{ mr: 1 }} />
                      About Me*
                    </Box>
                  </StyledFormLabel>
                  <StyledTextField
                    name="aboutMe"
                    value={formData.aboutMe}
                    onChange={handleChange}
                    placeholder="Write a brief description about yourself, your interests, and what you're looking for in a partner"
                    multiline
                    rows={4}
                    error={!!errors.aboutMe}
                    helperText={
                      errors.aboutMe ||
                      `${formData.aboutMe.length}/50 characters minimum (${Math.max(0, 50 - formData.aboutMe.length)} more needed)`
                    }
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      default:
        return 'Unknown step';
    }
  };

  return (
    <Box sx={{ width: '100%', maxWidth: '900px', p: { xs: 2, md: 3 } }}>
      <StyledPaper>
        <FloatingElement position="top-right" />
        <FloatingElement position="bottom-left" />

        <Box position="relative" zIndex={1}>
          <Typography variant="h4" align="center" gutterBottom
            sx={{
              fontFamily: 'var(--font-secondary)',
              background: 'var(--primary-gradient)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              fontWeight: 700,
              mb: 1
            }}>
            Create Your Profile
          </Typography>

          <Typography variant="body1" align="center" paragraph sx={{ mb: 4, color: 'var(--text-color-medium)' }}>
            Join Vaivahik and find your perfect match from the Maratha community
          </Typography>

          {/* Data source indicator with toggle - DEVELOPMENT ONLY */}
          {dataSource === 'mock' && process.env.NODE_ENV === 'development' && (
            <Alert
              severity="info"
              sx={{ mb: 3 }}
              action={
                <Button
                  color="inherit"
                  size="small"
                  onClick={() => {
                    const { toggleBackendMode } = require('@/utils/featureFlags');
                    toggleBackendMode();
                    window.location.reload();
                  }}
                >
                  Switch to Real Data
                </Button>
              }
            >
              Using mock data for development. Your information will not be stored permanently.
              <div style={{ marginTop: '8px', fontSize: '0.875rem' }}>
                <strong>Development Mode:</strong> Click "Switch to Real Data" to use actual backend API.
              </div>
            </Alert>
          )}

          {/* Production warning if somehow mock data is used */}
          {dataSource === 'mock' && process.env.NODE_ENV === 'production' && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              ⚠️ System Notice: Please contact support if you see this message.
            </Alert>
          )}

          {dataSource === 'real' && process.env.NODE_ENV === 'development' && (
            <Alert
              severity="success"
              sx={{ mb: 3 }}
              action={
                <Button
                  color="inherit"
                  size="small"
                  onClick={() => {
                    const { toggleBackendMode } = require('@/utils/featureFlags');
                    toggleBackendMode();
                    window.location.reload();
                  }}
                >
                  Switch to Mock Data
                </Button>
              }
            >
              Using real backend API. Your information will be stored permanently.
            </Alert>
          )}

          {/* Error message */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Submit error message */}
          {errors.submit && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {errors.submit}
            </Alert>
          )}

          {/* Success message */}
          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          {/* Stepper */}
          <StyledStepper activeStep={activeStep} alternativeLabel>
            {steps.map((label, index) => (
              <Step key={label} completed={index < activeStep || isStepComplete(index)}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </StyledStepper>

          {/* Progress indicator */}
          {activeStep === steps.length - 1 && (
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                <strong>Final Step:</strong> Complete your profile by writing about yourself (minimum 50 characters) to submit your registration.
              </Typography>
            </Alert>
          )}

          {/* Form content */}
          <Box sx={{ mt: 4, mb: 4 }}>
            {getStepContent(activeStep)}
          </Box>

          {/* Navigation buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            <StyledButton
              disabled={activeStep === 0 || loading}
              onClick={handleBack}
              variant="outlined"
            >
              Back
            </StyledButton>

            {activeStep === steps.length - 1 ? (
              <StyledButton
                variant="contained"
                onClick={handleSubmit}
                disabled={loading || (!formData.aboutMe || formData.aboutMe.length < 50)}
                startIcon={loading && <CircularProgress size={20} color="inherit" />}
                sx={{
                  opacity: loading || (!formData.aboutMe || formData.aboutMe.length < 50) ? 0.6 : 1,
                  cursor: loading || (!formData.aboutMe || formData.aboutMe.length < 50) ? 'not-allowed' : 'pointer'
                }}
              >
                {loading ? 'Submitting...' : 'Submit Registration'}
              </StyledButton>
            ) : (
              <StyledButton
                variant="contained"
                onClick={activeStep === 0 && !otpVerified ? handleVerifyOTP : handleNext}
                disabled={(activeStep === 0 && !otpVerified && (!formData.otp || !otpSent)) || loading}
              >
                {activeStep === 0 && !otpVerified ? 'Verify & Continue' : 'Next'}
              </StyledButton>
            )}
          </Box>
        </Box>
      </StyledPaper>
    </Box>
  );
};

export default ModernRegistrationForm;
