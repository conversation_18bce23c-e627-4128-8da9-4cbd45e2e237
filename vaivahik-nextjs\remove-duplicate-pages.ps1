# Script to remove duplicate Next.js pages
# This script creates backups of the files before removing them

# Create backup directory
$backupDir = "duplicate-pages-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
New-Item -ItemType Directory -Path $backupDir

# List of files to remove (with backup)
$filesToRemove = @(
    "src/pages/admin/blog-posts.js",
    "src/pages/api/admin/algorithm-settings.js",
    "src/pages/api/admin/success-analytics.js"
)

# Backup and remove each file
foreach ($file in $filesToRemove) {
    if (Test-Path -Path $file) {
        Write-Host "Backing up $file..."
        $backupPath = Join-Path -Path $backupDir -ChildPath $file.Replace("/", "_")
        Copy-Item -Path $file -Destination $backupPath
        
        Write-Host "Removing $file..."
        Remove-Item -Path $file
        
        Write-Host "File $file has been backed up and removed."
    } else {
        Write-Host "File $file not found, skipping."
    }
}

Write-Host "Duplicate pages have been removed. Backups are stored in $backupDir"
Write-Host "Please restart your Next.js development server to verify the warnings are gone."
