// src/routes/admin/blog.routes.js
const express = require('express');
const router = express.Router();
const blogController = require('../../controllers/admin/blog.controller');
const authenticateAdmin = require('../../middleware/adminAuth.middleware.js');

// Blog Posts Routes
router.get('/', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        blogController.getBlogPosts(req, res, next);
    });
});

router.post('/', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        blogController.createBlogPost(req, res, next);
    });
});

router.get('/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        blogController.getBlogPost(req, res, next);
    });
});

router.put('/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        blogController.updateBlogPost(req, res, next);
    });
});

router.delete('/:id', function(req, res, next) {
    authenticateAdmin(req, res, function() {
        blogController.deleteBlogPost(req, res, next);
    });
});

module.exports = router;
