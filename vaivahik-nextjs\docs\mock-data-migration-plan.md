# Mock Data Migration Plan

This document outlines the plan for standardizing the mock data file structure and removing duplicate files.

## Current Issues

1. **Inconsistent file paths**: Mock data files are stored in multiple formats:
   - `/mock-data/api/[endpoint]/index.json`
   - `/mock-data/api/[endpoint].json`
   - `/mock-data/api/api/[endpoint].json`
   - `/mock-data/api/api/[endpoint]/index.json`

2. **Duplicate files**: The same mock data is duplicated across multiple files, making maintenance difficult.

3. **Transition to live**: The current approach makes it difficult to identify which endpoints need to be replaced when transitioning to live data.

## Standardized File Structure

We've decided to standardize on the following file structure:

```
/mock-data/api/[endpoint]/index.json
```

For example:
- `/mock-data/api/admin/users/index.json`
- `/mock-data/api/admin/dashboard/index.json`

## Migration Steps

### 1. Update the mockDataService.js

We've already updated the `mockDataService.js` to:
- Better handle path normalization
- Use a standardized file path format
- Include a fallback mechanism to handle alternative paths during the transition

### 2. Identify Duplicate Files

Run the identification script to list all duplicate files:

```bash
node scripts/identify-duplicate-mock-data.js
```

This will generate a list of canonical files and duplicate files that can be removed.

### 3. Remove Duplicate Files

After verifying that the application works correctly with the updated `mockDataService.js`, remove the duplicate files using the generated migration script.

### 4. Update Components (if needed)

If any components are using hardcoded paths to mock data files, update them to use the standardized API endpoints instead.

### 5. Document the Canonical API Endpoints

Create a document listing all the canonical API endpoints used in the application. This will be useful when transitioning to live data.

## Transition to Live Data

When transitioning to live data:

1. **Feature Flag**: Use the existing feature flag system to toggle between mock and real data.

2. **Gradual Transition**: Implement real API endpoints one by one, using the canonical endpoint list as a guide.

3. **Fallback Mechanism**: Keep the mock data as a fallback for endpoints that haven't been implemented yet.

4. **Testing**: Test each endpoint with both mock and real data to ensure consistency.

## Benefits of This Approach

1. **Single Source of Truth**: Each API endpoint has exactly one mock data file.

2. **Easier Maintenance**: When mock data needs to be updated, there's only one file to change.

3. **Clearer Transition Path**: The canonical endpoint list provides a clear roadmap for implementing real API endpoints.

4. **Backward Compatibility**: The fallback mechanism ensures that the application continues to work during the transition.

## Conclusion

By standardizing the mock data file structure and removing duplicate files, we'll make the codebase more maintainable and the transition to live data smoother.

The updated `mockDataService.js` provides a robust solution that handles both the standardized file structure and backward compatibility with existing code.
