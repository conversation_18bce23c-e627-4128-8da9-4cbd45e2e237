import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import { dashboardApi } from '@/services/adminApiService';
import chartDataService from '@/services/chartDataService';
import UserGrowthChart from '@/components/admin/charts/UserGrowthChart';
import MatchRateChart from '@/components/admin/charts/MatchRateChart';
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Typography
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';

export default function Dashboard() {
  // Basic stats for dashboard widgets
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalUsersGrowth: 0,
    newRegistrations: 0,
    newRegistrationsGrowth: 0,
    successfulMatches: 0,
    successfulMatchesGrowth: 0,
    pendingVerifications: 0,
    pendingVerificationsGrowth: 0,
    reportedProfiles: 0,
    premiumUsers: 0,
    premiumUsersGrowth: 0,
    revenue: 0,
    revenueGrowth: 0
  });

  // Detailed stats for advanced analytics
  const [detailedStats, setDetailedStats] = useState({
    userStats: {
      total: 0,
      premium: 0,
      premiumPercentage: "0",
      verified: 0,
      verifiedPercentage: "0",
      newLast7Days: 0,
      genderDistribution: {
        male: 0,
        female: 0,
        other: 0
      }
    },
    matchStats: {
      total: 0,
      accepted: 0,
      successRate: "0"
    },
    reportStats: {
      total: 0,
      pending: 0
    },
    subscriptionStats: {
      total: 0,
      active: 0,
      activeRate: "0",
      totalRevenue: 0
    }
  });

  // Cache metadata
  const [cacheInfo, setCacheInfo] = useState({
    dashboardStats: {
      hit: false,
      lastUpdated: null,
      stale: false
    },
    userGrowthChart: {
      hit: false,
      lastUpdated: null,
      stale: false
    },
    matchRateChart: {
      hit: false,
      lastUpdated: null,
      stale: false
    }
  });

  // Auto-refresh settings
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(60); // seconds
  const [loading, setLoading] = useState(true);
  const [chartPeriod, setChartPeriod] = useState('month'); // week, month, year
  const [userGrowthData, setUserGrowthData] = useState([]);
  const [matchRateData, setMatchRateData] = useState({});
  const [chartLoading, setChartLoading] = useState(false);
  const [activities, setActivities] = useState([]);
  const [recentUsers, setRecentUsers] = useState([]);
  const [showUserModal, setShowUserModal] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [userPage, setUserPage] = useState(1);
  const [userLimit, setUserLimit] = useState(10);
  const [userTotal, setUserTotal] = useState(0);
  const [userFilter, setUserFilter] = useState('');
  const [activityFilter, setActivityFilter] = useState('');
  const [activityLimit, setActivityLimit] = useState(10);

  useEffect(() => {
    // Fetch dashboard stats
    fetchDashboardStats();
    fetchRecentActivity();
    fetchRecentUsers();
  }, [userPage, userLimit, userFilter, activityFilter, activityLimit]);

  // Fetch chart data when period changes
  useEffect(() => {
    fetchChartData();
  }, [chartPeriod]);

  // Set up auto-refresh
  useEffect(() => {
    let refreshTimer = null;

    if (autoRefresh) {
      console.log(`Setting up auto-refresh every ${refreshInterval} seconds`);

      // Initial refresh
      fetchDashboardStats(true);
      fetchChartData(true);

      // Set up interval for auto-refresh
      refreshTimer = setInterval(() => {
        console.log('Auto-refreshing dashboard data...');
        fetchDashboardStats(true);
        fetchChartData(true);
      }, refreshInterval * 1000);
    }

    // Clean up timer on unmount or when autoRefresh changes
    return () => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    };
  }, [autoRefresh, refreshInterval]);

  // Function to fetch chart data
  const fetchChartData = async (forceRefresh = false) => {
    setChartLoading(true);
    try {
      // Add refresh parameter if forcing refresh
      const params = forceRefresh ? { refresh: 'true' } : {};

      // Fetch user growth data
      const userGrowthResponse = await chartDataService.getUserGrowthData(chartPeriod, params);
      if (userGrowthResponse && userGrowthResponse.success) {
        setUserGrowthData(userGrowthResponse.data || []);

        // Update cache metadata if available
        if (userGrowthResponse._cache) {
          setCacheInfo(prevState => ({
            ...prevState,
            userGrowthChart: {
              hit: userGrowthResponse._cache.hit || false,
              lastUpdated: userGrowthResponse._cache.lastUpdated || new Date().toISOString(),
              stale: userGrowthResponse._cache.stale || false,
              period: userGrowthResponse._cache.period || chartPeriod
            }
          }));

          // Log cache status
          console.log('User growth chart cache status:', {
            hit: userGrowthResponse._cache.hit || false,
            period: userGrowthResponse._cache.period || chartPeriod,
            stale: userGrowthResponse._cache.stale || false
          });
        }
      }

      // Fetch match rate data
      const matchRateResponse = await chartDataService.getMatchRateData(chartPeriod, params);
      if (matchRateResponse && matchRateResponse.success) {
        setMatchRateData(matchRateResponse.data || {});

        // Update cache metadata if available
        if (matchRateResponse._cache) {
          setCacheInfo(prevState => ({
            ...prevState,
            matchRateChart: {
              hit: matchRateResponse._cache.hit || false,
              lastUpdated: matchRateResponse._cache.lastUpdated || new Date().toISOString(),
              stale: matchRateResponse._cache.stale || false,
              period: matchRateResponse._cache.period || chartPeriod
            }
          }));

          // Log cache status
          console.log('Match rate chart cache status:', {
            hit: matchRateResponse._cache.hit || false,
            period: matchRateResponse._cache.period || chartPeriod,
            stale: matchRateResponse._cache.stale || false
          });
        }
      }
    } catch (error) {
      console.error('Error fetching chart data:', error);
    } finally {
      setChartLoading(false);
    }
  };

  const fetchDashboardStats = async (forceRefresh = false) => {
    setLoading(true);
    try {
      // Add refresh parameter if forcing refresh
      const params = forceRefresh ? { refresh: 'true' } : {};
      const response = await dashboardApi.getDashboardStats(params);

      if (response && response.success) {
        // Set basic stats for dashboard widgets
        if (response.stats) {
          setStats(response.stats);
        }

        // Set detailed stats for advanced analytics
        const detailedData = {
          userStats: response.userStats || {},
          matchStats: response.matchStats || {},
          reportStats: response.reportStats || {},
          subscriptionStats: response.subscriptionStats || {}
        };

        setDetailedStats(detailedData);

        // Update cache metadata if available
        if (response._cache) {
          setCacheInfo(prevState => ({
            ...prevState,
            dashboardStats: {
              hit: response._cache.hit || false,
              lastUpdated: response._cache.lastUpdated || new Date().toISOString(),
              stale: response._cache.stale || false
            }
          }));

          // Log cache status
          console.log('Dashboard stats cache status:', {
            hit: response._cache.hit || false,
            lastUpdated: response._cache.lastUpdated || 'unknown',
            stale: response._cache.stale || false
          });
        }

        // Log available data for debugging
        console.log('Dashboard data received:', {
          basicStats: response.stats ? 'Available' : 'Not available',
          detailedStats: response.userStats ? 'Available' : 'Not available',
          fromCache: response._cache?.hit || false
        });
      } else {
        console.warn('Dashboard API returned unsuccessful response:', response);
        // Use default mock data
        setStats({
          totalUsers: 1250,
          totalUsersGrowth: 15,
          newRegistrations: 48,
          newRegistrationsGrowth: 12,
          successfulMatches: 32,
          successfulMatchesGrowth: 8,
          pendingVerifications: 18,
          pendingVerificationsGrowth: -5,
          reportedProfiles: 7,
          premiumUsers: 320,
          premiumUsersGrowth: 22,
          revenue: 125000,
          revenueGrowth: 18
        });

        // Set default detailed stats
        setDetailedStats({
          userStats: {
            total: 1250,
            premium: 320,
            premiumPercentage: "25.6",
            verified: 980,
            verifiedPercentage: "78.4",
            newLast7Days: 48,
            genderDistribution: {
              male: 720,
              female: 510,
              other: 20
            }
          },
          matchStats: {
            total: 850,
            accepted: 32,
            successRate: "3.8"
          },
          reportStats: {
            total: 25,
            pending: 7
          },
          subscriptionStats: {
            total: 450,
            active: 320,
            activeRate: "71.1",
            totalRevenue: 125000
          }
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Use default mock data
      setStats({
        totalUsers: 1250,
        totalUsersGrowth: 15,
        newRegistrations: 48,
        newRegistrationsGrowth: 12,
        successfulMatches: 32,
        successfulMatchesGrowth: 8,
        pendingVerifications: 18,
        pendingVerificationsGrowth: -5,
        reportedProfiles: 7,
        premiumUsers: 320,
        premiumUsersGrowth: 22,
        revenue: 125000,
        revenueGrowth: 18
      });

      // Set default detailed stats
      setDetailedStats({
        userStats: {
          total: 1250,
          premium: 320,
          premiumPercentage: "25.6",
          verified: 980,
          verifiedPercentage: "78.4",
          newLast7Days: 48,
          genderDistribution: {
            male: 720,
            female: 510,
            other: 20
          }
        },
        matchStats: {
          total: 850,
          accepted: 32,
          successRate: "3.8"
        },
        reportStats: {
          total: 25,
          pending: 7
        },
        subscriptionStats: {
          total: 450,
          active: 320,
          activeRate: "71.1",
          totalRevenue: 125000
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchRecentActivity = async () => {
    try {
      const params = {
        limit: activityLimit
      };

      if (activityFilter) {
        params.types = activityFilter;
      }

      const response = await dashboardApi.getRecentActivity(params);

      if (response && response.success) {
        setActivities(response.activities || []);
      } else {
        console.warn('Recent activity API returned unsuccessful response:', response);
        // Use mock data
        setActivities([
          {
            id: 1,
            type: 'registration',
            user: 'Rahul Sharma',
            details: 'New user registered',
            time: '10 minutes ago',
            icon: '👤',
            actionable: true,
            userId: 'user-123'
          },
          {
            id: 2,
            type: 'subscription',
            user: 'Priya Patel',
            details: 'Upgraded to Premium plan',
            time: '2 hours ago',
            icon: '💎',
            actionable: true,
            userId: 'user-456'
          },
          {
            id: 3,
            type: 'verification',
            user: 'Amit Kumar',
            details: 'Submitted verification documents',
            time: '3 hours ago',
            icon: '📄',
            actionable: true,
            userId: 'user-789'
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      // Set mock activities array if API fails
      setActivities([
        {
          id: 1,
          type: 'registration',
          user: 'Rahul Sharma',
          details: 'New user registered',
          time: '10 minutes ago',
          icon: '👤',
          actionable: true,
          userId: 'user-123'
        },
        {
          id: 2,
          type: 'subscription',
          user: 'Priya Patel',
          details: 'Upgraded to Premium plan',
          time: '2 hours ago',
          icon: '💎',
          actionable: true,
          userId: 'user-456'
        },
        {
          id: 3,
          type: 'verification',
          user: 'Amit Kumar',
          details: 'Submitted verification documents',
          time: '3 hours ago',
          icon: '📄',
          actionable: true,
          userId: 'user-789'
        }
      ]);
    }
  };

  const fetchRecentUsers = async () => {
    try {
      const params = {
        page: userPage,
        limit: userLimit
      };

      if (userFilter) {
        params.filter = userFilter;
      }

      const response = await dashboardApi.getRecentUsers(params);

      if (response && response.success) {
        setRecentUsers(response.users || []);
        setUserTotal(response.pagination?.totalUsers || 0);
      } else {
        console.warn('Recent users API returned unsuccessful response:', response);
        // Use mock data
        const mockUsers = [
          {
            id: 1,
            name: 'Rahul Sharma',
            age: 28,
            location: 'Mumbai',
            occupation: 'Software Engineer',
            verified: true,
            premium: true,
            registeredOn: '2023-05-15T10:30:00Z'
          },
          {
            id: 2,
            name: 'Priya Patel',
            age: 26,
            location: 'Pune',
            occupation: 'Doctor',
            verified: true,
            premium: false,
            registeredOn: '2023-05-14T14:45:00Z'
          },
          {
            id: 3,
            name: 'Amit Kumar',
            age: 30,
            location: 'Delhi',
            occupation: 'Business Analyst',
            verified: false,
            premium: false,
            registeredOn: '2023-05-13T09:15:00Z'
          }
        ];
        setRecentUsers(mockUsers);
        setUserTotal(mockUsers.length);
      }
    } catch (error) {
      console.error('Error fetching recent users:', error);
      // Set mock users array if API fails
      const mockUsers = [
        {
          id: 1,
          name: 'Rahul Sharma',
          age: 28,
          location: 'Mumbai',
          occupation: 'Software Engineer',
          verified: true,
          premium: true,
          registeredOn: '2023-05-15T10:30:00Z'
        },
        {
          id: 2,
          name: 'Priya Patel',
          age: 26,
          location: 'Pune',
          occupation: 'Doctor',
          verified: true,
          premium: false,
          registeredOn: '2023-05-14T14:45:00Z'
        },
        {
          id: 3,
          name: 'Amit Kumar',
          age: 30,
          location: 'Delhi',
          occupation: 'Business Analyst',
          verified: false,
          premium: false,
          registeredOn: '2023-05-13T09:15:00Z'
        }
      ];
      setRecentUsers(mockUsers);
      setUserTotal(mockUsers.length);
    }
  };

  // Handle viewing a user profile
  const handleViewUser = (user) => {
    setCurrentUser(user);
    setShowUserModal(true);
  };

  // Handle editing a user
  const handleEditUser = (user) => {
    // In a real implementation, this would navigate to the edit page
    // For now, we'll just show an alert
    alert(`Edit user: ${user.name}`);
  };

  // Handle deleting a user
  const handleDeleteUser = (user) => {
    setCurrentUser(user);
    setShowConfirmModal(true);
  };

  // Confirm user deletion
  const confirmDeleteUser = () => {
    // In a real implementation, this would call the API to delete the user
    // For now, we'll just remove the user from the local state
    setRecentUsers(recentUsers.filter(u => u.id !== currentUser.id));
    setShowConfirmModal(false);

    // Show success notification
    alert(`User ${currentUser.name} has been deleted`);
  };

  // Handle activity action
  const handleActivityAction = (activity) => {
    // Navigate to the appropriate page based on activity type
    switch (activity.type) {
      case 'registration':
        // Navigate to user profile
        handleViewUser({ id: activity.userId, name: activity.user });
        break;
      case 'subscription':
        // Navigate to subscription management
        window.location.href = `/admin/subscriptions?userId=${activity.userId}`;
        break;
      case 'verification':
        // Navigate to verification queue
        window.location.href = `/admin/verification-queue?userId=${activity.userId}`;
        break;
      case 'report':
        // Navigate to reported profiles
        window.location.href = `/admin/reported-profiles?reportId=${activity.reportId}`;
        break;
      case 'match':
        // Navigate to matches
        window.location.href = `/admin/matches?matchId=${activity.matchId}`;
        break;
      default:
        console.log('No action defined for this activity type');
    }
  };

  return (
    <EnhancedAdminLayout title="Dashboard">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Dashboard
          </Typography>
          <div className="dashboard-actions">
            {/* Refresh Controls */}
            <div className="refresh-controls">
              <FormControl variant="outlined" size="small" sx={{ minWidth: 120, mr: 1 }}>
                <InputLabel>Auto-refresh</InputLabel>
                <Select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  label="Auto-refresh"
                >
                  <MenuItem value={30}>30 seconds</MenuItem>
                  <MenuItem value={60}>1 minute</MenuItem>
                  <MenuItem value={300}>5 minutes</MenuItem>
                  <MenuItem value={600}>10 minutes</MenuItem>
                </Select>
              </FormControl>
              <Button
                variant="outlined"
                color={autoRefresh ? "error" : "success"}
                onClick={() => setAutoRefresh(!autoRefresh)}
                sx={{ mr: 2 }}
              >
                {autoRefresh ? "Stop Auto-refresh" : "Start Auto-refresh"}
              </Button>
              <Button
                variant="outlined"
                color="primary"
                onClick={() => {
                  fetchDashboardStats(true);
                  fetchChartData(true);
                }}
                startIcon={<span>↻</span>}
                sx={{ mr: 2 }}
              >
                Refresh Now
              </Button>
            </div>
            <Button
              variant="contained"
              color="primary"
              startIcon={<span>↓</span>}
            >
              Export Reports
            </Button>
          </div>
        </Box>

        {loading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading dashboard data...</p>
          </div>
        ) : (
          <>
            {/* Cache Status Indicator */}
            {cacheInfo.dashboardStats.lastUpdated && (
              <div className="cache-status">
                <div className={`cache-indicator ${cacheInfo.dashboardStats.stale ? 'stale' : (cacheInfo.dashboardStats.hit ? 'cached' : 'fresh')}`}></div>
                <span className="cache-text">
                  Data {cacheInfo.dashboardStats.stale ? 'stale' : (cacheInfo.dashboardStats.hit ? 'from cache' : 'refreshed')} at {' '}
                  {new Date(cacheInfo.dashboardStats.lastUpdated).toLocaleTimeString()}
                </span>
                {autoRefresh && (
                  <span className="auto-refresh-indicator">
                    Auto-refreshing every {refreshInterval} seconds
                  </span>
                )}
              </div>
            )}

            {/* Widget Container */}
            <div className="widget-container">
              <div className="widget">
                <div className="widget-stat">
                  <div className="stat-content">
                    <h3>Total Users</h3>
                    <div className="stat-value">{stats.totalUsers.toLocaleString()}</div>
                    <div className={`stat-change ${stats.totalUsersGrowth >= 0 ? 'change-up' : 'change-down'}`}>
                      <span>{stats.totalUsersGrowth >= 0 ? '↑' : '↓'} {Math.abs(stats.totalUsersGrowth)}%</span>
                      <span>this month</span>
                    </div>
                  </div>
                  <div className="stat-icon bg-purple">👥</div>
                </div>
              </div>

              <div className="widget">
                <div className="widget-stat">
                  <div className="stat-content">
                    <h3>New Registrations</h3>
                    <div className="stat-value">{stats.newRegistrations.toLocaleString()}</div>
                    <div className={`stat-change ${stats.newRegistrationsGrowth >= 0 ? 'change-up' : 'change-down'}`}>
                      <span>{stats.newRegistrationsGrowth >= 0 ? '↑' : '↓'} {Math.abs(stats.newRegistrationsGrowth)}%</span>
                      <span>this week</span>
                    </div>
                  </div>
                  <div className="stat-icon bg-orange">📝</div>
                </div>
              </div>

              <div className="widget">
                <div className="widget-stat">
                  <div className="stat-content">
                    <h3>Successful Matches</h3>
                    <div className="stat-value">{stats.successfulMatches.toLocaleString()}</div>
                    <div className={`stat-change ${stats.successfulMatchesGrowth >= 0 ? 'change-up' : 'change-down'}`}>
                      <span>{stats.successfulMatchesGrowth >= 0 ? '↑' : '↓'} {Math.abs(stats.successfulMatchesGrowth)}%</span>
                      <span>this month</span>
                    </div>
                  </div>
                  <div className="stat-icon bg-green">💑</div>
                </div>
              </div>

              <div className="widget">
                <div className="widget-stat">
                  <div className="stat-content">
                    <h3>Premium Users</h3>
                    <div className="stat-value">{stats.premiumUsers.toLocaleString()}</div>
                    <div className={`stat-change ${stats.premiumUsersGrowth >= 0 ? 'change-up' : 'change-down'}`}>
                      <span>{stats.premiumUsersGrowth >= 0 ? '↑' : '↓'} {Math.abs(stats.premiumUsersGrowth)}%</span>
                      <span>this month</span>
                    </div>
                  </div>
                  <div className="stat-icon bg-purple-light">💎</div>
                </div>
              </div>

              <div className="widget">
                <div className="widget-stat">
                  <div className="stat-content">
                    <h3>Pending Verifications</h3>
                    <div className="stat-value">{stats.pendingVerifications.toLocaleString()}</div>
                    <div className={`stat-change ${stats.pendingVerificationsGrowth >= 0 ? 'change-up' : 'change-down'}`}>
                      <span>{stats.pendingVerificationsGrowth >= 0 ? '↑' : '↓'} {Math.abs(stats.pendingVerificationsGrowth)}%</span>
                      <span>this week</span>
                    </div>
                  </div>
                  <div className="stat-icon bg-blue">⏳</div>
                </div>
              </div>

              <div className="widget">
                <div className="widget-stat">
                  <div className="stat-content">
                    <h3>Total Revenue</h3>
                    <div className="stat-value">₹{stats.revenue.toLocaleString()}</div>
                    <div className={`stat-change ${stats.revenueGrowth >= 0 ? 'change-up' : 'change-down'}`}>
                      <span>{stats.revenueGrowth >= 0 ? '↑' : '↓'} {Math.abs(stats.revenueGrowth)}%</span>
                      <span>this month</span>
                    </div>
                  </div>
                  <div className="stat-icon bg-red">💰</div>
                </div>
              </div>
            </div>

            {/* Charts Grid */}
            <div className="charts-grid">
              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">User Growth</h3>
                  <div className="chart-actions">
                    <button
                      className={`chart-action ${chartPeriod === 'week' ? 'active' : ''}`}
                      onClick={() => {
                        setChartPeriod('week');
                        setChartLoading(true);
                      }}
                      aria-label="Show weekly data"
                    >
                      Week
                    </button>
                    <button
                      className={`chart-action ${chartPeriod === 'month' ? 'active' : ''}`}
                      onClick={() => {
                        setChartPeriod('month');
                        setChartLoading(true);
                      }}
                      aria-label="Show monthly data"
                    >
                      Month
                    </button>
                    <button
                      className={`chart-action ${chartPeriod === 'year' ? 'active' : ''}`}
                      onClick={() => {
                        setChartPeriod('year');
                        setChartLoading(true);
                      }}
                      aria-label="Show yearly data"
                    >
                      Year
                    </button>
                  </div>
                </div>
                <div className="chart" id="userGrowthChart">
                  <UserGrowthChart
                    period={chartPeriod}
                    data={userGrowthData}
                    loading={chartLoading}
                    onRefresh={fetchChartData}
                  />
                </div>
              </div>

              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">Match Success Rate</h3>
                  <div className="chart-actions">
                    <button
                      className={`chart-action ${chartPeriod === 'week' ? 'active' : ''}`}
                      onClick={() => {
                        setChartPeriod('week');
                        setChartLoading(true);
                      }}
                      aria-label="Show weekly data"
                    >
                      Week
                    </button>
                    <button
                      className={`chart-action ${chartPeriod === 'month' ? 'active' : ''}`}
                      onClick={() => {
                        setChartPeriod('month');
                        setChartLoading(true);
                      }}
                      aria-label="Show monthly data"
                    >
                      Month
                    </button>
                    <button
                      className={`chart-action ${chartPeriod === 'year' ? 'active' : ''}`}
                      onClick={() => {
                        setChartPeriod('year');
                        setChartLoading(true);
                      }}
                      aria-label="Show yearly data"
                    >
                      Year
                    </button>
                  </div>
                </div>
                <div className="chart" id="matchRateChart">
                  <MatchRateChart
                    period={chartPeriod}
                    data={matchRateData}
                    loading={chartLoading}
                    onRefresh={fetchChartData}
                  />
                </div>
              </div>
            </div>

            {/* Detailed Statistics Section */}
            <div className="detailed-stats-section">
              <div className="section-header">
                <h3 className="section-title">Detailed Statistics</h3>
                <div className="section-actions">
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => window.print()}
                    startIcon={<span>📄</span>}
                  >
                    Print Report
                  </Button>
                </div>
              </div>

              <div className="detailed-stats-grid">
                {/* User Statistics */}
                <div className="detailed-stats-card">
                  <div className="detailed-stats-header">
                    <h4>User Statistics</h4>
                  </div>
                  <div className="detailed-stats-content">
                    <div className="stat-row">
                      <div className="stat-label">Total Users</div>
                      <div className="stat-value">{detailedStats.userStats.total?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Premium Users</div>
                      <div className="stat-value">
                        {detailedStats.userStats.premium?.toLocaleString() || 0}
                        <span className="stat-percentage">({detailedStats.userStats.premiumPercentage || 0}%)</span>
                      </div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Verified Users</div>
                      <div className="stat-value">
                        {detailedStats.userStats.verified?.toLocaleString() || 0}
                        <span className="stat-percentage">({detailedStats.userStats.verifiedPercentage || 0}%)</span>
                      </div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">New Users (Last 7 Days)</div>
                      <div className="stat-value">{detailedStats.userStats.newLast7Days?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-divider"></div>
                    <div className="stat-row">
                      <div className="stat-label">Gender Distribution</div>
                      <div className="stat-value"></div>
                    </div>
                    <div className="gender-distribution">
                      <div className="gender-bar">
                        <div
                          className="gender-segment male"
                          style={{
                            width: `${detailedStats.userStats.genderDistribution?.male / detailedStats.userStats.total * 100}%`
                          }}
                        ></div>
                        <div
                          className="gender-segment female"
                          style={{
                            width: `${detailedStats.userStats.genderDistribution?.female / detailedStats.userStats.total * 100}%`
                          }}
                        ></div>
                        <div
                          className="gender-segment other"
                          style={{
                            width: `${detailedStats.userStats.genderDistribution?.other / detailedStats.userStats.total * 100}%`
                          }}
                        ></div>
                      </div>
                      <div className="gender-legend">
                        <div className="legend-item">
                          <div className="legend-color male"></div>
                          <div className="legend-label">Male ({Math.round(detailedStats.userStats.genderDistribution?.male / detailedStats.userStats.total * 100 || 0)}%)</div>
                        </div>
                        <div className="legend-item">
                          <div className="legend-color female"></div>
                          <div className="legend-label">Female ({Math.round(detailedStats.userStats.genderDistribution?.female / detailedStats.userStats.total * 100 || 0)}%)</div>
                        </div>
                        <div className="legend-item">
                          <div className="legend-color other"></div>
                          <div className="legend-label">Other ({Math.round(detailedStats.userStats.genderDistribution?.other / detailedStats.userStats.total * 100 || 0)}%)</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Match Statistics */}
                <div className="detailed-stats-card">
                  <div className="detailed-stats-header">
                    <h4>Match Statistics</h4>
                  </div>
                  <div className="detailed-stats-content">
                    <div className="stat-row">
                      <div className="stat-label">Total Matches</div>
                      <div className="stat-value">{detailedStats.matchStats.total?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Accepted Matches</div>
                      <div className="stat-value">{detailedStats.matchStats.accepted?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Success Rate</div>
                      <div className="stat-value">{detailedStats.matchStats.successRate || 0}%</div>
                    </div>
                    <div className="match-success-visual">
                      <div className="success-rate-circle">
                        <svg viewBox="0 0 36 36">
                          <path
                            d="M18 2.0845
                              a 15.9155 15.9155 0 0 1 0 31.831
                              a 15.9155 15.9155 0 0 1 0 -31.831"
                            fill="none"
                            stroke="#eee"
                            strokeWidth="3"
                          />
                          <path
                            d="M18 2.0845
                              a 15.9155 15.9155 0 0 1 0 31.831
                              a 15.9155 15.9155 0 0 1 0 -31.831"
                            fill="none"
                            stroke="#4caf50"
                            strokeWidth="3"
                            strokeDasharray={`${parseFloat(detailedStats.matchStats.successRate || 0)}, 100`}
                          />
                          <text x="18" y="20.5" textAnchor="middle" fontSize="8" fill="#333">
                            {detailedStats.matchStats.successRate || 0}%
                          </text>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Report Statistics */}
                <div className="detailed-stats-card">
                  <div className="detailed-stats-header">
                    <h4>Report Statistics</h4>
                  </div>
                  <div className="detailed-stats-content">
                    <div className="stat-row">
                      <div className="stat-label">Total Reports</div>
                      <div className="stat-value">{detailedStats.reportStats.total?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Pending Reports</div>
                      <div className="stat-value">{detailedStats.reportStats.pending?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Resolved Reports</div>
                      <div className="stat-value">
                        {(detailedStats.reportStats.total - detailedStats.reportStats.pending)?.toLocaleString() || 0}
                      </div>
                    </div>
                    <div className="report-status-visual">
                      <div className="status-bar">
                        <div
                          className="status-segment pending"
                          style={{
                            width: `${detailedStats.reportStats.pending / detailedStats.reportStats.total * 100}%`
                          }}
                        ></div>
                        <div
                          className="status-segment resolved"
                          style={{
                            width: `${(detailedStats.reportStats.total - detailedStats.reportStats.pending) / detailedStats.reportStats.total * 100}%`
                          }}
                        ></div>
                      </div>
                      <div className="status-legend">
                        <div className="legend-item">
                          <div className="legend-color pending"></div>
                          <div className="legend-label">Pending</div>
                        </div>
                        <div className="legend-item">
                          <div className="legend-color resolved"></div>
                          <div className="legend-label">Resolved</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Subscription Statistics */}
                <div className="detailed-stats-card">
                  <div className="detailed-stats-header">
                    <h4>Subscription Statistics</h4>
                  </div>
                  <div className="detailed-stats-content">
                    <div className="stat-row">
                      <div className="stat-label">Total Subscriptions</div>
                      <div className="stat-value">{detailedStats.subscriptionStats.total?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Active Subscriptions</div>
                      <div className="stat-value">
                        {detailedStats.subscriptionStats.active?.toLocaleString() || 0}
                        <span className="stat-percentage">({detailedStats.subscriptionStats.activeRate || 0}%)</span>
                      </div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Total Revenue</div>
                      <div className="stat-value">₹{detailedStats.subscriptionStats.totalRevenue?.toLocaleString() || 0}</div>
                    </div>
                    <div className="stat-row">
                      <div className="stat-label">Avg. Revenue Per User</div>
                      <div className="stat-value">
                        ₹{Math.round(detailedStats.subscriptionStats.totalRevenue / detailedStats.subscriptionStats.total).toLocaleString() || 0}
                      </div>
                    </div>
                    <div className="subscription-status-visual">
                      <div className="status-bar">
                        <div
                          className="status-segment active"
                          style={{
                            width: `${detailedStats.subscriptionStats.active / detailedStats.subscriptionStats.total * 100}%`
                          }}
                        ></div>
                        <div
                          className="status-segment inactive"
                          style={{
                            width: `${(detailedStats.subscriptionStats.total - detailedStats.subscriptionStats.active) / detailedStats.subscriptionStats.total * 100}%`
                          }}
                        ></div>
                      </div>
                      <div className="status-legend">
                        <div className="legend-item">
                          <div className="legend-color active"></div>
                          <div className="legend-label">Active</div>
                        </div>
                        <div className="legend-item">
                          <div className="legend-color inactive"></div>
                          <div className="legend-label">Inactive</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Users Section */}
            <div className="recent-users-section">
              <div className="section-header">
                <h3>Recently Registered Users</h3>
                <div className="section-actions">
                  <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
                    <InputLabel>Filter</InputLabel>
                    <Select
                      value={userFilter}
                      onChange={(e) => {
                        setUserFilter(e.target.value);
                        setUserPage(1); // Reset to first page when filter changes
                      }}
                      label="Filter"
                    >
                      <MenuItem value="">All Users</MenuItem>
                      <MenuItem value="verified">Verified</MenuItem>
                      <MenuItem value="premium">Premium</MenuItem>
                      <MenuItem value="basic">Basic</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControl variant="outlined" size="small" sx={{ minWidth: 100, mr: 2 }}>
                    <InputLabel>Show</InputLabel>
                    <Select
                      value={userLimit}
                      onChange={(e) => {
                        setUserLimit(e.target.value);
                        setUserPage(1); // Reset to first page when limit changes
                      }}
                      label="Show"
                    >
                      <MenuItem value={10}>10</MenuItem>
                      <MenuItem value={25}>25</MenuItem>
                      <MenuItem value={50}>50</MenuItem>
                      <MenuItem value={100}>100</MenuItem>
                    </Select>
                  </FormControl>
                  <Button
                    variant="contained"
                    color="primary"
                    component="a"
                    href="/admin/users"
                    size="small"
                  >
                    View All Users
                  </Button>
                </div>
              </div>

              <div className="users-data-grid">
                <DataGrid
                  rows={recentUsers}
                  columns={[
                  {
                    field: 'user',
                    headerName: 'User',
                    flex: 2,
                    minWidth: 200,
                    renderCell: (params) => (
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <Box sx={{
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          bgcolor: 'primary.light',
                          color: 'white',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 2,
                          flexShrink: 0,
                          overflow: 'hidden'
                        }}>
                          {params.row.photo ? (
                            <img src={params.row.photo} alt={params.row.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                          ) : (
                            params.row.name.charAt(0).toUpperCase()
                          )}
                        </Box>
                        <Box sx={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'medium', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{params.row.name}</Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{params.row.age} years</Typography>
                        </Box>
                      </Box>
                    )
                  },
                  {
                    field: 'location',
                    headerName: 'Location',
                    flex: 1,
                    minWidth: 120,
                    renderCell: (params) => (
                      <Typography sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {params.value}
                      </Typography>
                    )
                  },
                  {
                    field: 'occupation',
                    headerName: 'Occupation',
                    flex: 1,
                    minWidth: 150,
                    renderCell: (params) => (
                      <Typography sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        {params.value}
                      </Typography>
                    )
                  },
                  {
                    field: 'status',
                    headerName: 'Status',
                    flex: 1,
                    minWidth: 150,
                    renderCell: (params) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {params.row.verified && (
                          <Chip label="Verified" color="primary" size="small" />
                        )}
                        {params.row.premium && (
                          <Chip label="Premium" color="secondary" size="small" />
                        )}
                        {!params.row.verified && !params.row.premium && (
                          <Chip label="Basic" variant="outlined" size="small" />
                        )}
                      </Box>
                    )
                  },
                  {
                    field: 'actions',
                    headerName: 'Actions',
                    width: 120,
                    sortable: false,
                    filterable: false,
                    renderCell: (params) => (
                      <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                        <IconButton
                          color="primary"
                          size="small"
                          onClick={() => handleViewUser(params.row)}
                          title="View Profile"
                          sx={{ mx: 0.5 }}
                        >
                          👁️
                        </IconButton>
                        <IconButton
                          color="secondary"
                          size="small"
                          onClick={() => handleEditUser(params.row)}
                          title="Edit User"
                          sx={{ mx: 0.5 }}
                        >
                          ✏️
                        </IconButton>
                        <IconButton
                          color="error"
                          size="small"
                          onClick={() => handleDeleteUser(params.row)}
                          title="Delete User"
                          sx={{ mx: 0.5 }}
                        >
                          🗑️
                        </IconButton>
                      </Box>
                    )
                  }
                ]}
                style={{ height: 500 }}
                pageSize={userLimit}
                rowCount={userTotal}
                pagination
                paginationMode="server"
                onPageChange={(newPage) => setUserPage(newPage + 1)}
                page={userPage - 1}
                rowsPerPageOptions={[10, 25, 50, 100]}
                onPageSizeChange={(newPageSize) => setUserLimit(newPageSize)}
                disableSelectionOnClick
                loading={loading}
                sx={{
                  '& .MuiDataGrid-columnHeaders': {
                    backgroundColor: '#f5f5f5',
                    borderBottom: '1px solid #e0e0e0'
                  },
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #f0f0f0'
                  },
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: '#f9f9f9'
                  },
                  border: 'none',
                  '& .MuiDataGrid-cell:focus': {
                    outline: 'none'
                  },
                  '& .MuiDataGrid-columnHeader:focus': {
                    outline: 'none'
                  }
                }}
              />
              </div>
            </div>

            {/* Recent Activity */}
            <div className="activity-feed">
              <div className="section-header">
                <h3>Recent Activity</h3>
                <div className="section-actions">
                  <FormControl variant="outlined" size="small" sx={{ minWidth: 150, mr: 2 }}>
                    <InputLabel>Filter</InputLabel>
                    <Select
                      value={activityFilter}
                      onChange={(e) => setActivityFilter(e.target.value)}
                      label="Filter"
                    >
                      <MenuItem value="">All Activities</MenuItem>
                      <MenuItem value="registration">Registrations</MenuItem>
                      <MenuItem value="subscription">Subscriptions</MenuItem>
                      <MenuItem value="verification">Verifications</MenuItem>
                      <MenuItem value="report">Reports</MenuItem>
                      <MenuItem value="match">Matches</MenuItem>
                    </Select>
                  </FormControl>
                  <FormControl variant="outlined" size="small" sx={{ minWidth: 100, mr: 2 }}>
                    <InputLabel>Show</InputLabel>
                    <Select
                      value={activityLimit}
                      onChange={(e) => setActivityLimit(e.target.value)}
                      label="Show"
                    >
                      <MenuItem value={5}>5</MenuItem>
                      <MenuItem value={10}>10</MenuItem>
                      <MenuItem value={20}>20</MenuItem>
                      <MenuItem value={50}>50</MenuItem>
                    </Select>
                  </FormControl>
                  <Button
                    variant="outlined"
                    size="small"
                    color="primary"
                    onClick={() => {
                      setActivityFilter('');
                      setActivityLimit(10);
                    }}
                    disabled={!activityFilter && activityLimit === 10}
                  >
                    Reset Filters
                  </Button>
                </div>
              </div>

              {loading ? (
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <p>Loading activity data...</p>
                </div>
              ) : activities.length > 0 ? (
                <ul className="activity-list">
                  {activities.map(activity => (
                    <li key={activity.id} className="activity-item">
                      <div className="activity-icon">{activity.icon}</div>
                      <div className="activity-content">
                        <div className="activity-title">
                          {activity.type === 'registration' && 'New User Registration'}
                          {activity.type === 'subscription' && 'New Premium Subscription'}
                          {activity.type === 'verification' && 'Verification Request'}
                          {activity.type === 'report' && 'Profile Reported'}
                          {activity.type === 'match' && 'Match Success'}
                        </div>
                        <div className="activity-details">
                          <strong>{activity.user}</strong> - {activity.details}
                        </div>
                        <div className="activity-time">{activity.time}</div>
                        {activity.actionable && (
                          <div className="activity-actions">
                            <Button
                              variant="contained"
                              size="small"
                              color="primary"
                              onClick={() => handleActivityAction(activity)}
                            >
                              {activity.actionText || 'View'}
                            </Button>
                          </div>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="empty-state">
                  <p>No recent activities found</p>
                  {activityFilter && (
                    <Button
                      variant="contained"
                      size="small"
                      color="primary"
                      onClick={() => setActivityFilter('')}
                    >
                      Clear Filter
                    </Button>
                  )}
                </div>
              )}
            </div>

            {/* User Profile Modal */}
            <Dialog
              open={showUserModal}
              onClose={() => setShowUserModal(false)}
              maxWidth="md"
              fullWidth
            >
              {currentUser && (
                <>
                  <DialogTitle>
                    User Profile
                    <IconButton
                      aria-label="close"
                      onClick={() => setShowUserModal(false)}
                      sx={{ position: 'absolute', right: 8, top: 8 }}
                    >
                      &times;
                    </IconButton>
                  </DialogTitle>
                  <DialogContent>
                    <Box sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', mb: 3 }}>
                        <Box
                          sx={{
                            width: 80,
                            height: 80,
                            borderRadius: '50%',
                            bgcolor: 'primary.main',
                            color: 'white',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '2rem',
                            mr: 2
                          }}
                        >
                          {currentUser.photo ? (
                            <img
                              src={currentUser.photo}
                              alt={currentUser.name}
                              style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }}
                            />
                          ) : (
                            currentUser.name.charAt(0)
                          )}
                        </Box>
                        <Box>
                          <Typography variant="h5" component="h2">{currentUser.name}</Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, color: 'text.secondary' }}>
                            <Typography variant="body2">{currentUser.age} years</Typography>
                            <Box component="span" sx={{ mx: 0.5 }}>•</Box>
                            <Typography variant="body2">{currentUser.location}</Typography>
                            <Box component="span" sx={{ mx: 0.5 }}>•</Box>
                            <Typography variant="body2">{currentUser.occupation}</Typography>
                          </Box>
                          <Box sx={{ mt: 1 }}>
                            {currentUser.verified && (
                              <Chip label="Verified" color="primary" size="small" sx={{ mr: 1 }} />
                            )}
                            {currentUser.premium && (
                              <Chip label="Premium" color="secondary" size="small" />
                            )}
                          </Box>
                        </Box>
                      </Box>

                      <Typography variant="h6" sx={{ mb: 2 }}>Personal Information</Typography>
                      <Box sx={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(2, 1fr)',
                        gap: 2,
                        '& .detail-item': {
                          p: 1.5,
                          borderRadius: 1,
                          bgcolor: 'background.paper',
                          border: '1px solid',
                          borderColor: 'divider'
                        }
                      }}>
                        <Box className="detail-item">
                          <Typography variant="caption" color="text.secondary">Full Name</Typography>
                          <Typography variant="body1">{currentUser.name}</Typography>
                        </Box>
                        <Box className="detail-item">
                          <Typography variant="caption" color="text.secondary">Age</Typography>
                          <Typography variant="body1">{currentUser.age} years</Typography>
                        </Box>
                        <Box className="detail-item">
                          <Typography variant="caption" color="text.secondary">Location</Typography>
                          <Typography variant="body1">{currentUser.location}</Typography>
                        </Box>
                        <Box className="detail-item">
                          <Typography variant="caption" color="text.secondary">Occupation</Typography>
                          <Typography variant="body1">{currentUser.occupation}</Typography>
                        </Box>
                        <Box className="detail-item">
                          <Typography variant="caption" color="text.secondary">Registered On</Typography>
                          <Typography variant="body1">
                            {new Date(currentUser.registeredOn).toLocaleDateString()}
                          </Typography>
                        </Box>
                        <Box className="detail-item">
                          <Typography variant="caption" color="text.secondary">Status</Typography>
                          <Typography variant="body1">
                            {currentUser.verified ? 'Verified' : 'Unverified'} /
                            {currentUser.premium ? ' Premium' : ' Basic'}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={() => setShowUserModal(false)}>
                      Close
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        setShowUserModal(false);
                        handleEditUser(currentUser);
                      }}
                    >
                      Edit User
                    </Button>
                  </DialogActions>
                </>
              )}
            </Dialog>

            {/* Confirmation Modal */}
            <Dialog
              open={showConfirmModal}
              onClose={() => setShowConfirmModal(false)}
            >
              {currentUser && (
                <>
                  <DialogTitle>
                    Confirm Deletion
                  </DialogTitle>
                  <DialogContent>
                    <Typography variant="body1">
                      Are you sure you want to delete the user <strong>{currentUser.name}</strong>?
                      This action cannot be undone.
                    </Typography>
                  </DialogContent>
                  <DialogActions>
                    <Button onClick={() => setShowConfirmModal(false)}>
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      color="error"
                      onClick={confirmDeleteUser}
                    >
                      Delete
                    </Button>
                  </DialogActions>
                </>
              )}
            </Dialog>
          </>
        )}

        <style jsx>{`
          /* Widget Container */
          .widget-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
          }

          .widget {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }

          .widget-stat {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
          }

          .stat-content {
            flex: 1;
          }

          .stat-content h3 {
            font-size: 1rem;
            font-weight: 500;
            color: #666;
            margin-bottom: 10px;
          }

          .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 5px;
          }

          .stat-change {
            display: flex;
            flex-direction: column;
            font-size: 0.8rem;
          }

          .change-up {
            color: #4caf50;
          }

          .change-down {
            color: #f44336;
          }

          .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
          }

          .bg-purple {
            background-color: #5e35b1;
          }

          .bg-purple-light {
            background-color: #7e57c2;
          }

          .bg-orange {
            background-color: #ff9800;
          }

          .bg-green {
            background-color: #4caf50;
          }

          .bg-blue {
            background-color: #2196f3;
          }

          .bg-red {
            background-color: #f44336;
          }

          /* Charts Grid */
          .charts-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
          }

          .chart-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }

          .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
          }

          .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
          }

          .chart-actions {
            display: flex;
            gap: 5px;
          }

          .chart-action {
            background: none;
            border: none;
            padding: 5px 10px;
            font-size: 0.85rem;
            color: #666;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.2s ease;
          }

          .chart-action:hover {
            background-color: #f5f5f5;
          }

          .chart-action.active {
            background-color: var(--primary);
            color: white;
          }

          .chart {
            padding: 20px;
            height: 300px;
            position: relative;
          }

          /* Chart animation */
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          /* Chart loading overlay */
          .chart-loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
          }

          .chart-loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
          }

          /* Chart refresh button */
          .chart-refresh {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: transparent;
            border: none;
            color: #666;
            cursor: pointer;
            z-index: 5;
            font-size: 16px;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.2s ease;
          }

          .chart-refresh:hover {
            background-color: rgba(0, 0, 0, 0.05);
            transform: rotate(30deg);
          }

          /* Activity Feed */
          .activity-feed {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-top: 30px;
            margin-bottom: 30px;
          }

          .activity-list {
            list-style: none;
            padding: 0;
            margin: 0;
          }

          .activity-item {
            display: flex;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
          }

          .activity-item:hover {
            background-color: #f9f9f9;
          }

          .activity-item:last-child {
            border-bottom: none;
          }

          .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          .activity-content {
            flex: 1;
          }

          .activity-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-size: 1rem;
          }

          .activity-details {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .activity-time {
            font-size: 0.8rem;
            color: #999;
            display: flex;
            align-items: center;
          }

          .activity-time::before {
            content: "🕒";
            margin-right: 5px;
            font-size: 0.9rem;
          }

          .activity-actions {
            margin-top: 12px;
          }

          .empty-state {
            padding: 40px;
            text-align: center;
            color: #666;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin: 20px;
          }

          .empty-state p {
            margin-bottom: 20px;
            font-size: 1.1rem;
          }

          .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 50px;
            color: #666;
          }

          .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          }

          .loading-container p {
            font-size: 1rem;
            color: #555;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          /* Dashboard Actions */
          .dashboard-actions {
            display: flex;
            align-items: center;
          }

          .refresh-controls {
            display: flex;
            align-items: center;
          }

          /* Cache Status Indicator */
          .cache-status {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 8px 12px;
            background-color: #f5f5f5;
            border-radius: 6px;
            font-size: 0.85rem;
            color: #666;
          }

          .cache-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
          }

          .cache-indicator.fresh {
            background-color: #4caf50;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
          }

          .cache-indicator.cached {
            background-color: #2196f3;
            box-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
          }

          .cache-indicator.stale {
            background-color: #ff9800;
            box-shadow: 0 0 5px rgba(255, 152, 0, 0.5);
          }

          .cache-text {
            margin-right: 15px;
          }

          .auto-refresh-indicator {
            margin-left: auto;
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
          }

          /* Dashboard Bottom Section */
          .dashboard-bottom {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
          }

          /* Recent Users Section */
          .recent-users-section {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }

          .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
          }

          .section-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
          }

          .view-all-link {
            color: var(--primary);
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
          }

          .view-all-link:hover {
            text-decoration: underline;
          }

          .users-table-container {
            overflow-x: auto;
          }

          .users-table {
            width: 100%;
            border-collapse: collapse;
          }

          .users-table th,
          .users-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
          }

          .users-table th {
            font-weight: 600;
            color: #555;
            background-color: #f9f9f9;
          }

          .users-table tr:last-child td {
            border-bottom: none;
          }

          .users-table tr:hover {
            background-color: #f5f5f5;
          }

          /* Users Data Grid */
          .users-data-grid {
            padding: 0;
            height: 500px;
            width: 100%;
          }

          .section-actions {
            display: flex;
            align-items: center;
          }

          .user-cell {
            display: flex;
            align-items: center;
            gap: 10px;
          }

          .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-light);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.1rem;
            overflow: hidden;
          }

          .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .user-info {
            display: flex;
            flex-direction: column;
          }

          .user-name {
            font-weight: 600;
            color: var(--text-dark);
          }

          .user-age {
            font-size: 0.8rem;
            color: #666;
          }

          .user-status {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
          }

          .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
          }

          .status-badge.verified {
            background-color: #e8f5e9;
            color: #2e7d32;
          }

          .status-badge.premium {
            background-color: #fff8e1;
            color: #ff8f00;
          }

          .status-badge.basic {
            background-color: #f5f5f5;
            color: #616161;
          }

          .user-actions {
            display: flex;
            gap: 8px;
          }

          .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
          }

          .action-btn:hover {
            background-color: #f0f0f0;
          }

          .view-btn {
            color: var(--primary);
          }

          .edit-btn {
            color: var(--info);
          }

          .delete-btn {
            color: var(--danger);
          }

          /* Detailed Statistics Styles */
          .detailed-stats-section {
            margin-bottom: 30px;
          }

          .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
          }

          .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
          }

          .detailed-stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
          }

          .detailed-stats-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }

          .detailed-stats-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
          }

          .detailed-stats-header h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
          }

          .detailed-stats-content {
            padding: 15px 20px;
          }

          .stat-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          }

          .stat-label {
            font-size: 0.9rem;
            color: #666;
          }

          .stat-value {
            font-weight: 600;
            color: var(--text-dark);
          }

          .stat-percentage {
            font-size: 0.8rem;
            color: #666;
            margin-left: 5px;
          }

          .stat-divider {
            height: 1px;
            background-color: #eee;
            margin: 15px 0;
          }

          /* Gender Distribution */
          .gender-distribution {
            margin-top: 10px;
          }

          .gender-bar {
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            margin-bottom: 10px;
          }

          .gender-segment {
            height: 100%;
          }

          .gender-segment.male {
            background-color: #2196f3;
          }

          .gender-segment.female {
            background-color: #e91e63;
          }

          .gender-segment.other {
            background-color: #9e9e9e;
          }

          .gender-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
          }

          .legend-item {
            display: flex;
            align-items: center;
          }

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 5px;
          }

          .legend-color.male {
            background-color: #2196f3;
          }

          .legend-color.female {
            background-color: #e91e63;
          }

          .legend-color.other {
            background-color: #9e9e9e;
          }

          .legend-color.pending {
            background-color: #ff9800;
          }

          .legend-color.resolved {
            background-color: #4caf50;
          }

          .legend-color.active {
            background-color: #4caf50;
          }

          .legend-color.inactive {
            background-color: #9e9e9e;
          }

          .legend-label {
            font-size: 0.8rem;
            color: #666;
          }

          /* Match Success Visual */
          .match-success-visual {
            display: flex;
            justify-content: center;
            margin-top: 15px;
          }

          .success-rate-circle {
            width: 120px;
            height: 120px;
          }

          /* Report Status Visual */
          .report-status-visual,
          .subscription-status-visual {
            margin-top: 15px;
          }

          .status-bar {
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            margin-bottom: 10px;
          }

          .status-segment {
            height: 100%;
          }

          .status-segment.pending {
            background-color: #ff9800;
          }

          .status-segment.resolved {
            background-color: #4caf50;
          }

          .status-segment.active {
            background-color: #4caf50;
          }

          .status-segment.inactive {
            background-color: #9e9e9e;
          }

          .status-legend {
            display: flex;
            gap: 15px;
          }

          /* Responsive Styles */
          @media (max-width: 1200px) {
            .widget-container {
              grid-template-columns: repeat(2, 1fr);
            }

            .charts-grid {
              grid-template-columns: 1fr;
            }

            .dashboard-bottom {
              grid-template-columns: 1fr;
            }

            .detailed-stats-grid {
              grid-template-columns: 1fr;
            }
          }

          @media (max-width: 768px) {
            .widget-container {
              grid-template-columns: 1fr;
            }

            .widget-stat {
              flex-direction: column;
              text-align: center;
            }

            .stat-content {
              margin-bottom: 15px;
            }

            .gender-legend,
            .status-legend {
              flex-direction: column;
              gap: 5px;
            }

            .stat-icon {
              margin-right: 0;
            }

            .users-table th:nth-child(3),
            .users-table td:nth-child(3) {
              display: none;
            }
          }

          @media (max-width: 576px) {
            .users-table th:nth-child(2),
            .users-table td:nth-child(2) {
              display: none;
            }
          }

          /* User Profile Modal Styles */
          .user-profile {
            display: flex;
            flex-direction: column;
            gap: 20px;
          }

          .profile-header {
            display: flex;
            align-items: center;
            gap: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
          }

          .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-color: var(--primary-light);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 2rem;
            overflow: hidden;
          }

          .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .profile-info {
            flex: 1;
          }

          .profile-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-dark);
          }

          .profile-meta {
            display: flex;
            gap: 8px;
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
          }

          .profile-badges {
            display: flex;
            gap: 8px;
          }

          .profile-details {
            display: flex;
            flex-direction: column;
            gap: 20px;
          }

          .detail-section {
            margin-bottom: 20px;
          }

          .detail-section h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-dark);
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
          }

          .detail-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
          }

          .detail-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
          }

          .detail-label {
            font-size: 0.85rem;
            color: #666;
          }

          .detail-value {
            font-weight: 500;
            color: var(--text-dark);
          }

          .confirmation-modal-content {
            max-width: 450px;
          }
        `}</style>
      </Box>
    </EnhancedAdminLayout>
  );
}