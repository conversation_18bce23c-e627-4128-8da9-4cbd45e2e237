import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  styled
} from '@mui/material';
import {
  Description as BiodataIcon,
  Download as DownloadIcon,
  Visibility as PreviewIcon,
  ShoppingCart as PurchaseIcon,
  Close as CloseIcon,
  Star as StarIcon,
  Male as MaleIcon,
  Female as FemaleIcon
} from '@mui/icons-material';

const TemplateCard = styled(Card)(({ theme }) => ({
  borderRadius: 16,
  overflow: 'hidden',
  cursor: 'pointer',
  transition: 'all 0.3s ease',
  border: '2px solid rgba(255, 95, 109, 0.1)',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 40px rgba(255, 95, 109, 0.2)',
    borderColor: '#FF5F6D'
  }
}));

const PremiumBadge = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: 12,
  right: 12,
  background: 'linear-gradient(135deg, #FFD700, #FFA000)',
  color: '#000',
  fontWeight: 600,
  zIndex: 1
}));

const BiodataTemplatesWidget = ({ userId, userGender = 'male' }) => {
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [filterGender, setFilterGender] = useState('all');

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      // Mock biodata templates
      const mockTemplates = [
        {
          id: 1,
          name: 'Classic Maratha Template',
          description: 'Traditional design with Maratha heritage elements',
          price: 299,
          originalPrice: 399,
          discount: 25,
          isPremium: false,
          targetGender: 'male',
          thumbnail: '/api/placeholder/300/400',
          downloads: 1250,
          rating: 4.8,
          features: ['Traditional Design', 'Maratha Elements', 'Family Tree Section']
        },
        {
          id: 2,
          name: 'Elegant Rose Template',
          description: 'Beautiful floral design perfect for female profiles',
          price: 399,
          originalPrice: 499,
          discount: 20,
          isPremium: true,
          targetGender: 'female',
          thumbnail: '/api/placeholder/300/400',
          downloads: 980,
          rating: 4.9,
          features: ['Floral Design', 'Elegant Layout', 'Photo Collage']
        },
        {
          id: 3,
          name: 'Modern Professional',
          description: 'Clean and modern design for working professionals',
          price: 199,
          originalPrice: 299,
          discount: 33,
          isPremium: false,
          targetGender: 'unisex',
          thumbnail: '/api/placeholder/300/400',
          downloads: 2100,
          rating: 4.7,
          features: ['Modern Design', 'Professional Look', 'Career Highlights']
        },
        {
          id: 4,
          name: 'Royal Heritage',
          description: 'Luxurious design with royal Maratha motifs',
          price: 599,
          originalPrice: 799,
          discount: 25,
          isPremium: true,
          targetGender: 'male',
          thumbnail: '/api/placeholder/300/400',
          downloads: 750,
          rating: 4.9,
          features: ['Royal Design', 'Gold Accents', 'Heritage Motifs']
        }
      ];
      
      setTemplates(mockTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const handlePreview = (template) => {
    setSelectedTemplate(template);
    setPreviewOpen(true);
  };

  const handlePurchase = (template) => {
    console.log('Purchasing template:', template);
    // Implement purchase logic with payment gateway
  };

  const handleDownload = (template) => {
    console.log('Downloading template:', template);
    // Implement download logic
  };

  const filteredTemplates = templates.filter(template => {
    if (filterGender === 'all') return true;
    return template.targetGender === filterGender || template.targetGender === 'unisex';
  });

  const getGenderIcon = (gender) => {
    switch (gender) {
      case 'male':
        return <MaleIcon sx={{ color: '#2196F3' }} />;
      case 'female':
        return <FemaleIcon sx={{ color: '#E91E63' }} />;
      default:
        return <StarIcon sx={{ color: '#FF9800' }} />;
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        mb: 3,
        p: 3,
        background: 'linear-gradient(135deg, rgba(255, 95, 109, 0.1) 0%, rgba(255, 195, 113, 0.1) 100%)',
        borderRadius: 3
      }}>
        <BiodataIcon sx={{ fontSize: 32, color: '#FF5F6D', mr: 2 }} />
        <Box>
          <Typography variant="h5" fontWeight="700" color="#FF5F6D">
            📄 Biodata Templates
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Choose from beautiful, professionally designed biodata templates
          </Typography>
        </Box>
      </Box>

      {/* Filter Buttons */}
      <Box sx={{ display: 'flex', gap: 2, mb: 4, justifyContent: 'center' }}>
        {[
          { key: 'all', label: 'All Templates', icon: <StarIcon /> },
          { key: 'male', label: 'Male Oriented', icon: <MaleIcon /> },
          { key: 'female', label: 'Female Oriented', icon: <FemaleIcon /> }
        ].map((filter) => (
          <Button
            key={filter.key}
            variant={filterGender === filter.key ? 'contained' : 'outlined'}
            startIcon={filter.icon}
            onClick={() => setFilterGender(filter.key)}
            sx={{
              borderRadius: 3,
              px: 3,
              ...(filterGender === filter.key ? {
                background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
              } : {
                borderColor: '#FF5F6D',
                color: '#FF5F6D'
              })
            }}
          >
            {filter.label}
          </Button>
        ))}
      </Box>

      {/* Templates Grid */}
      <Grid container spacing={3}>
        {filteredTemplates.map((template) => (
          <Grid item xs={12} sm={6} md={4} key={template.id}>
            <TemplateCard>
              <Box sx={{ position: 'relative' }}>
                <CardMedia
                  component="img"
                  height="300"
                  image={template.thumbnail}
                  alt={template.name}
                />
                {template.isPremium && (
                  <PremiumBadge label="Premium" size="small" />
                )}
                <Box sx={{
                  position: 'absolute',
                  top: 12,
                  left: 12,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  {getGenderIcon(template.targetGender)}
                  <Chip
                    label={`${template.rating} ⭐`}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                      color: '#333',
                      fontWeight: 600
                    }}
                  />
                </Box>
              </Box>
              
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  {template.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {template.description}
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" fontWeight="700" color="#FF5F6D">
                    ₹{template.price}
                  </Typography>
                  {template.discount && (
                    <>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          textDecoration: 'line-through',
                          color: 'text.secondary',
                          ml: 1
                        }}
                      >
                        ₹{template.originalPrice}
                      </Typography>
                      <Chip
                        label={`${template.discount}% OFF`}
                        size="small"
                        color="success"
                        sx={{ ml: 1 }}
                      />
                    </>
                  )}
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {template.downloads.toLocaleString()} downloads
                </Typography>

                <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                  {template.features.slice(0, 2).map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(255, 95, 109, 0.1)',
                        color: '#FF5F6D',
                        fontSize: '0.75rem'
                      }}
                    />
                  ))}
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<PreviewIcon />}
                    onClick={() => handlePreview(template)}
                    sx={{
                      borderColor: '#FF5F6D',
                      color: '#FF5F6D',
                      borderRadius: 2,
                      flex: 1
                    }}
                  >
                    Preview
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    startIcon={<PurchaseIcon />}
                    onClick={() => handlePurchase(template)}
                    sx={{
                      background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
                      borderRadius: 2,
                      flex: 1
                    }}
                  >
                    Buy Now
                  </Button>
                </Box>
              </CardContent>
            </TemplateCard>
          </Grid>
        ))}
      </Grid>

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center' 
        }}>
          <Typography variant="h6" fontWeight="600">
            {selectedTemplate?.name} - Preview
          </Typography>
          <IconButton onClick={() => setPreviewOpen(false)}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {selectedTemplate && (
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={selectedTemplate.thumbnail}
                alt={selectedTemplate.name}
                style={{ maxWidth: '100%', height: 'auto', borderRadius: 8 }}
              />
              <Typography variant="body1" sx={{ mt: 2 }}>
                {selectedTemplate.description}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            variant="outlined"
            onClick={() => setPreviewOpen(false)}
            sx={{ borderColor: '#FF5F6D', color: '#FF5F6D' }}
          >
            Close
          </Button>
          <Button
            variant="contained"
            startIcon={<PurchaseIcon />}
            onClick={() => {
              handlePurchase(selectedTemplate);
              setPreviewOpen(false);
            }}
            sx={{
              background: 'linear-gradient(135deg, #FF5F6D, #FFC371)',
            }}
          >
            Purchase Template
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BiodataTemplatesWidget;
