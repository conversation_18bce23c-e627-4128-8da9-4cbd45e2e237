/**
 * Mock Data Service
 *
 * This service provides functions for working with mock data.
 * It simulates API calls and returns mock data from JSON files.
 */

// Simulate network delay for more realistic API calls
const MOCK_DELAY = 500; // milliseconds

/**
 * Fetch mock data from a JSON file
 * @param {string} endpoint - The endpoint to fetch data from
 * @param {Object} params - Query parameters
 * @returns {Promise} - Promise with the mock data
 */
export const fetchMockData = async (endpoint, params = {}) => {
  // Add delay to simulate network request
  await new Promise(resolve => setTimeout(resolve, MOCK_DELAY));

  try {
    // Log the original endpoint for debugging
    console.log(`Original endpoint: ${endpoint}`);

    // Special case for admin dashboard - return hardcoded mock data
    if (endpoint.includes('/admin/dashboard') && !endpoint.includes('recent-users') && !endpoint.includes('recent-activity')) {
      console.log('Using hardcoded mock data for admin dashboard');
      return {
        success: true,
        stats: {
          totalUsers: 5842,
          totalUsersGrowth: 12,
          newRegistrations: 245,
          newRegistrationsGrowth: 8,
          successfulMatches: 128,
          successfulMatchesGrowth: 15,
          pendingVerifications: 87,
          pendingVerificationsGrowth: -5,
          reportedProfiles: 23,
          premiumUsers: 1256,
          premiumUsersGrowth: 18,
          revenue: 458750,
          revenueGrowth: 22
        },
        message: "Dashboard stats retrieved successfully"
      };
    }

    // Special case for admin charts - return hardcoded chart data
    if (endpoint.includes('/admin/charts/')) {
      console.log('Using hardcoded mock data for admin charts');
      if (endpoint.includes('user-growth')) {
        return {
          success: true,
          data: {
            labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            datasets: [{
              label: "New Users",
              data: [120, 145, 132, 165, 178, 190, 210, 232, 256, 278, 290, 310],
              borderColor: "rgb(94, 53, 177)",
              backgroundColor: "rgba(94, 53, 177, 0.5)"
            }]
          }
        };
      }
      if (endpoint.includes('match-rate')) {
        return {
          success: true,
          data: {
            labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
            datasets: [{
              label: "Match Success Rate (%)",
              data: [72, 74, 73, 75, 78, 80, 82, 83, 85, 86, 87, 88],
              borderColor: "rgb(255, 152, 0)",
              backgroundColor: "rgba(255, 152, 0, 0.5)"
            }]
          }
        };
      }
    }

    // Special case for recent activity
    if (endpoint.includes('recent-activity')) {
      console.log('Using hardcoded mock data for recent activity');
      return {
        success: true,
        activities: [
          {
            id: 1,
            type: 'user_registration',
            message: 'New user registered: Priya Sharma',
            timestamp: new Date().toISOString(),
            user: { name: 'Priya Sharma', id: 'USR001' }
          },
          {
            id: 2,
            type: 'match_success',
            message: 'Successful match between Rahul and Anita',
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            user: { name: 'System', id: 'SYS' }
          }
        ]
      };
    }

    // Handle API_BASE_URL in the endpoint
    let processedEndpoint = endpoint;
    if (endpoint.includes('http://localhost:5000/api')) {
      processedEndpoint = endpoint.replace('http://localhost:5000/api', '');
    } else if (endpoint.includes('http://localhost:3001/api')) {
      processedEndpoint = endpoint.replace('http://localhost:3001/api', '');
    }

    // Normalize endpoint
    const normalizedEndpoint = processedEndpoint.startsWith('/') ? processedEndpoint : `/${processedEndpoint}`;

    // Log the normalized endpoint for debugging
    console.log(`Normalized endpoint: ${normalizedEndpoint}`);

    // Remove /api/ prefix if present for consistent handling
    const cleanEndpoint = normalizedEndpoint.replace(/^\/api\//, '/');
    console.log(`Clean endpoint: ${cleanEndpoint}`);

    // Construct the canonical file path - always use the /mock-data/api/[endpoint]/index.json pattern
    // This is our standardized format going forward
    let filePath;

    // Remove trailing slash if present
    const pathWithoutTrailingSlash = cleanEndpoint.endsWith('/')
      ? cleanEndpoint.slice(0, -1)
      : cleanEndpoint;

    // For endpoints with ID parameters like /users/123, extract the base path
    const basePath = pathWithoutTrailingSlash.replace(/\/\d+$/, '');

    // Construct the canonical path
    filePath = `/mock-data/api${basePath}/index.json`;

    // Add debugging for the file path
    console.log(`Canonical file path: ${filePath}`);

    console.log(`Attempting to fetch from: ${filePath}`);

    // Try to fetch from the canonical path first
    let response = await fetch(filePath);

    // If the canonical path doesn't exist, try alternative paths
    if (!response.ok) {
      console.log(`File not found at canonical path: ${filePath}`);

      // Try alternative path formats
      const alternativePaths = [
        // Format: /mock-data/api/endpoint.json
        `/mock-data/api${cleanEndpoint}.json`,

        // Format: /mock-data/api/api/endpoint.json (duplicate api prefix)
        `/mock-data/api/api${cleanEndpoint.replace(/^\/api\//, '/')}.json`,

        // Format: /mock-data/api/endpoint/index.json (with trailing slash)
        `/mock-data/api${cleanEndpoint}/index.json`,

        // Format: /mock-data/api/api/endpoint/index.json (duplicate api prefix with trailing slash)
        `/mock-data/api/api${cleanEndpoint.replace(/^\/api\//, '/')}/index.json`,

        // Also try the admin directory directly
        `/mock-data/admin${cleanEndpoint.replace(/^\/admin\//, '/')}.json`
      ];

      // Try each alternative path
      for (const altPath of alternativePaths) {
        console.log(`Trying alternative path: ${altPath}`);
        response = await fetch(altPath);

        if (response.ok) {
          console.log(`Found mock data at alternative path: ${altPath}`);
          break;
        }
      }

      // If still not found, throw an error
      if (!response.ok) {
        throw new Error(`Failed to fetch mock data for ${endpoint} from ${filePath} or any alternative paths`);
      }
    }

    const data = await response.json();

    // Apply pagination if params include page and limit
    if (params.page && params.limit && data.pagination) {
      const page = parseInt(params.page);
      const limit = parseInt(params.limit);

      // If the data already has pagination, use it
      return data;
    }

    // Apply search filter if params include search
    if (params.search && data.items) {
      const searchTerm = params.search.toLowerCase();
      const filteredItems = data.items.filter(item => {
        return Object.values(item).some(value => {
          if (typeof value === 'string') {
            return value.toLowerCase().includes(searchTerm);
          }
          return false;
        });
      });

      return {
        ...data,
        items: filteredItems,
        pagination: {
          ...data.pagination,
          total: filteredItems.length,
          totalPages: Math.ceil(filteredItems.length / parseInt(params.limit || 10))
        }
      };
    }

    return data;
  } catch (error) {
    console.error(`Error fetching mock data for ${endpoint}:`, error);
    return {
      success: false,
      message: `Failed to fetch mock data for ${endpoint}: ${error.message}`
    };
  }
};

/**
 * Create a mock item
 * @param {string} endpoint - The endpoint to create the item at
 * @param {Object} data - The data to create
 * @returns {Promise} - Promise with the created item
 */
export const createMockItem = async (endpoint, data) => {
  // Add delay to simulate network request
  await new Promise(resolve => setTimeout(resolve, MOCK_DELAY));

  try {
    // Generate a new ID for the item
    const newId = Math.floor(Math.random() * 1000) + 1000;

    // Create a new item with the data and generated ID
    const newItem = {
      id: newId,
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      message: 'Item created successfully',
      item: newItem
    };
  } catch (error) {
    console.error(`Error creating mock item for ${endpoint}:`, error);
    return {
      success: false,
      message: `Failed to create mock item: ${error.message}`
    };
  }
};

/**
 * Update a mock item
 * @param {string} endpoint - The endpoint to update the item at
 * @param {number} id - The ID of the item to update
 * @param {Object} data - The data to update
 * @returns {Promise} - Promise with the updated item
 */
export const updateMockItem = async (endpoint, id, data) => {
  // Add delay to simulate network request
  await new Promise(resolve => setTimeout(resolve, MOCK_DELAY));

  try {
    // Create an updated item with the data
    const updatedItem = {
      id,
      ...data,
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      message: 'Item updated successfully',
      item: updatedItem
    };
  } catch (error) {
    console.error(`Error updating mock item for ${endpoint}:`, error);
    return {
      success: false,
      message: `Failed to update mock item: ${error.message}`
    };
  }
};

/**
 * Delete a mock item
 * @param {string} endpoint - The endpoint to delete the item from
 * @param {number} id - The ID of the item to delete
 * @returns {Promise} - Promise with the result
 */
export const deleteMockItem = async (endpoint, id) => {
  // Add delay to simulate network request
  await new Promise(resolve => setTimeout(resolve, MOCK_DELAY));

  try {
    return {
      success: true,
      message: 'Item deleted successfully'
    };
  } catch (error) {
    console.error(`Error deleting mock item for ${endpoint}:`, error);
    return {
      success: false,
      message: `Failed to delete mock item: ${error.message}`
    };
  }
};

/**
 * Mock authentication service
 */
export const mockAuth = {
  login: async (credentials) => {
    // Add delay to simulate network request
    await new Promise(resolve => setTimeout(resolve, MOCK_DELAY));

    // Check if credentials are valid
    if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
      return {
        success: true,
        token: 'mock-token-123456789',
        admin: {
          id: 1,
          name: 'Super Admin',
          email: '<EMAIL>',
          role: 'SUPER_ADMIN'
        }
      };
    }

    return {
      success: false,
      message: 'Invalid email or password'
    };
  },

  logout: async () => {
    // Add delay to simulate network request
    await new Promise(resolve => setTimeout(resolve, MOCK_DELAY));

    return {
      success: true,
      message: 'Logged out successfully'
    };
  }
};
