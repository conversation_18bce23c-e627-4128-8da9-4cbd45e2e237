/**
 * <PERSON><PERSON><PERSON> to identify duplicate mock data files
 *
 * This script scans the mock-data directory and identifies files that are duplicates
 * based on our standardized file structure.
 *
 * Usage:
 * node scripts/identify-duplicate-mock-data.js
 */

const fs = require('fs');
const path = require('path');

// Base directory for mock data
const MOCK_DATA_DIR = path.join(__dirname, '..', 'public', 'mock-data');

// Canonical format: /mock-data/api/[endpoint]/index.json
const CANONICAL_FORMAT = 'api/[endpoint]/index.json';

// Alternative formats we want to eliminate
const ALTERNATIVE_FORMATS = [
  'api/[endpoint].json',                  // Format: /mock-data/api/endpoint.json
  'api/api/[endpoint].json',              // Format: /mock-data/api/api/endpoint.json
  'api/api/[endpoint]/index.json'         // Format: /mock-data/api/api/endpoint/index.json
];

// Function to recursively scan a directory
function scanDirectory(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      scanDirectory(filePath, fileList);
    } else if (stat.isFile() && file.endsWith('.json')) {
      // Get the relative path from the mock-data directory
      const relativePath = path.relative(MOCK_DATA_DIR, filePath);
      fileList.push(relativePath);
    }
  });

  return fileList;
}

// Function to normalize an endpoint path
function normalizeEndpoint(filePath) {
  // Extract the endpoint part
  let normalized = filePath.replace(/\\/g, '/'); // Convert Windows backslashes to forward slashes

  // Handle different prefixes
  if (normalized.startsWith('api/api/admin/')) {
    // For paths like api/api/admin/users.json
    normalized = 'admin/' + normalized.substring(12);
  } else if (normalized.startsWith('api/admin/')) {
    // For paths like api/admin/users/index.json
    normalized = 'admin/' + normalized.substring(10);
  } else if (normalized.startsWith('admin/')) {
    // For paths like admin/users.json
    normalized = normalized;
  }

  // Handle index.json files
  if (normalized.endsWith('/index.json')) {
    normalized = normalized.replace('/index.json', '');
  } else if (normalized.endsWith('.json')) {
    normalized = normalized.replace('.json', '');
  }

  return normalized;
}

// Function to group files by their normalized endpoint
function groupFilesByEndpoint(files) {
  const groups = {};

  files.forEach(file => {
    const normalized = normalizeEndpoint(file);

    if (!groups[normalized]) {
      groups[normalized] = [];
    }

    groups[normalized].push(file);
  });

  return groups;
}

// Function to identify the canonical file for each group
function identifyCanonicalFiles(groups) {
  const result = {
    canonical: {},
    duplicates: []
  };

  Object.entries(groups).forEach(([endpoint, files]) => {
    if (files.length === 1) {
      // Only one file for this endpoint, consider it canonical
      result.canonical[endpoint] = files[0];
    } else {
      // Multiple files for this endpoint, identify the canonical one

      // Prioritize files in the following order:
      // 1. Files in the admin/ directory
      // 2. Files in the api/admin/ directory with index.json
      // 3. Files in the api/admin/ directory without index.json
      // 4. Files in the api/api/admin/ directory

      // First, check if there's a file in the admin/ directory
      const adminFile = files.find(file => file.replace(/\\/g, '/').startsWith('admin/'));

      if (adminFile) {
        result.canonical[endpoint] = adminFile;
        result.duplicates.push(...files.filter(file => file !== adminFile));
      } else {
        // No admin/ file, check for api/admin/ with index.json
        const apiAdminIndexFile = files.find(file =>
          file.replace(/\\/g, '/').startsWith('api/admin/') &&
          file.replace(/\\/g, '/').endsWith('/index.json')
        );

        if (apiAdminIndexFile) {
          result.canonical[endpoint] = apiAdminIndexFile;
          result.duplicates.push(...files.filter(file => file !== apiAdminIndexFile));
        } else {
          // No api/admin/ with index.json, check for api/admin/ without index.json
          const apiAdminFile = files.find(file =>
            file.replace(/\\/g, '/').startsWith('api/admin/') &&
            !file.replace(/\\/g, '/').endsWith('/index.json')
          );

          if (apiAdminFile) {
            result.canonical[endpoint] = apiAdminFile;
            result.duplicates.push(...files.filter(file => file !== apiAdminFile));
          } else {
            // Use the first file as canonical
            result.canonical[endpoint] = files[0];
            result.duplicates.push(...files.slice(1));
          }
        }
      }
    }
  });

  return result;
}

// Main function
function main() {
  try {
    console.log('Scanning mock data directory...');
    console.log(`Base directory: ${MOCK_DATA_DIR}`);

    // Check if the directory exists
    if (!fs.existsSync(MOCK_DATA_DIR)) {
      console.error(`Directory does not exist: ${MOCK_DATA_DIR}`);
      return;
    }

    const files = scanDirectory(MOCK_DATA_DIR);

    console.log(`\nFound ${files.length} mock data files.`);

    // Log all files with their normalized endpoints for debugging
    console.log('\nAll files with normalized endpoints:');
    files.forEach(file => {
      console.log(`  ${file} => ${normalizeEndpoint(file)}`);
    });

    const groups = groupFilesByEndpoint(files);

    // Log the groups for debugging
    console.log('\nGroups of files by normalized endpoint:');
    Object.entries(groups).forEach(([endpoint, groupFiles]) => {
      console.log(`  ${endpoint}:`);
      groupFiles.forEach(file => {
        console.log(`    - ${file}`);
      });
    });

    const { canonical, duplicates } = identifyCanonicalFiles(groups);

    console.log('\nCanonical files (to keep):');
    Object.entries(canonical).forEach(([endpoint, file]) => {
      console.log(`  ${endpoint}: ${file}`);
    });

    console.log('\nDuplicate files (to remove):');
    duplicates.forEach(file => {
      console.log(`  ${file}`);
    });

    console.log(`\nTotal: ${Object.keys(canonical).length} canonical files, ${duplicates.length} duplicates.`);

    // Generate migration script
    console.log('\nMigration script:');
    console.log('```powershell');
    console.log('# Migration script to remove duplicate mock data files');
    console.log('# Run this script from the project root directory');
    console.log('');

    duplicates.forEach(file => {
      console.log(`Remove-Item -Path "public/mock-data/${file}" -Force`);
    });

    console.log('```');

    // Generate a list of canonical API endpoints
    console.log('\nCanonical API Endpoints:');
    console.log('```');
    Object.keys(canonical).sort().forEach(endpoint => {
      console.log(`/api/${endpoint}`);
    });
    console.log('```');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the script
main();
