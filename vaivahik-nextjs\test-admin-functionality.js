/**
 * Admin Functionality Test Script
 * 
 * This script tests the functionality of admin pages in the Vaivahik application.
 * It uses Puppeteer to automate browser interactions and verify that pages load correctly.
 * 
 * To run this test:
 * 1. Make sure the Next.js development server is running
 * 2. Run: node test-admin-functionality.js
 */

const puppeteer = require('puppeteer');

// Configuration
const config = {
  baseUrl: 'http://localhost:3000',
  adminPages: [
    { path: '/admin/dashboard', name: 'Dash<PERSON>' },
    { path: '/admin/users', name: 'All Users' },
    { path: '/admin/verification-queue', name: 'Verification Queue' },
    { path: '/admin/reported-profiles', name: 'Reported Profiles' },
    { path: '/admin/premium-plans', name: 'Premium Plans' },
    { path: '/admin/success-stories', name: 'Success Stories' },
    { path: '/admin/settings', name: 'Settings' }
  ],
  screenshotDir: './test-screenshots'
};

/**
 * Test a single admin page
 * @param {Object} page - Puppeteer page object
 * @param {string} path - Page path
 * @param {string} name - Page name
 * @returns {Object} Test result
 */
async function testAdminPage(page, path, name) {
  console.log(`Testing ${name} page...`);
  
  try {
    // Navigate to the page
    await page.goto(`${config.baseUrl}${path}`, { waitUntil: 'networkidle0' });
    
    // Wait for the page to load
    await page.waitForSelector('.admin-card, .admin-content-container', { timeout: 5000 });
    
    // Take a screenshot
    await page.screenshot({ path: `${config.screenshotDir}/${path.replace(/\//g, '-')}.png` });
    
    // Check if the page title contains the expected name
    const pageTitle = await page.evaluate(() => {
      const titleElement = document.querySelector('h1, h2.page-title');
      return titleElement ? titleElement.innerText : '';
    });
    
    const hasCorrectTitle = pageTitle.includes(name);
    
    // Check if the page has a table or main content
    const hasContent = await page.evaluate(() => {
      return document.querySelector('.data-table, .admin-table, .admin-card-content') !== null;
    });
    
    return {
      path,
      name,
      success: hasCorrectTitle && hasContent,
      hasCorrectTitle,
      hasContent,
      error: null
    };
  } catch (error) {
    console.error(`Error testing ${name} page:`, error.message);
    return {
      path,
      name,
      success: false,
      hasCorrectTitle: false,
      hasContent: false,
      error: error.message
    };
  }
}

/**
 * Test all admin pages
 */
async function testAllAdminPages() {
  console.log('Starting admin functionality tests...');
  
  // Create screenshots directory if it doesn't exist
  const fs = require('fs');
  if (!fs.existsSync(config.screenshotDir)) {
    fs.mkdirSync(config.screenshotDir, { recursive: true });
  }
  
  // Launch browser
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1280, height: 800 }
  });
  
  const page = await browser.newPage();
  
  // Results array
  const results = [];
  
  // Test each admin page
  for (const adminPage of config.adminPages) {
    const result = await testAdminPage(page, adminPage.path, adminPage.name);
    results.push(result);
  }
  
  // Close browser
  await browser.close();
  
  // Print summary
  console.log('\n=== Test Results Summary ===');
  
  let passCount = 0;
  let failCount = 0;
  
  for (const result of results) {
    if (result.success) {
      console.log(`✅ ${result.name}: Passed`);
      passCount++;
    } else {
      console.log(`❌ ${result.name}: Failed`);
      if (!result.hasCorrectTitle) console.log(`   - Missing or incorrect page title`);
      if (!result.hasContent) console.log(`   - Missing main content`);
      if (result.error) console.log(`   - Error: ${result.error}`);
      failCount++;
    }
  }
  
  console.log(`\nTotal: ${results.length} tests, ${passCount} passed, ${failCount} failed`);
  
  return {
    total: results.length,
    passed: passCount,
    failed: failCount,
    results
  };
}

// Run the tests if this file is executed directly
if (require.main === module) {
  testAllAdminPages()
    .then(summary => {
      console.log('\nTest execution completed.');
      process.exit(summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  testAdminPage,
  testAllAdminPages
};
