import { useState, useEffect, useRef } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Input,
  FormHelperText,
  Grid,
  Select,
  MenuItem,
  Typography,
  Paper,
  Divider,
  CircularProgress,
  Autocomplete,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { format, differenceInYears, isValid } from 'date-fns';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import HeightIcon from '@mui/icons-material/Height';
import Tooltip from '@mui/material/Tooltip';
import HeightSelector from './HeightSelector';
import { validateHeight, formatHeight } from '@/utils/heightUtils';

/**
 * Advanced Birth Details Form Component
 *
 * Features:
 * - Gender-based age validation (18+ for females, 21+ for males)
 * - Date picker with calendar visualization
 * - Time picker with 12/24 hour format options
 * - Birth place autocomplete with Google Places API
 * - Height selector in feet and inches
 * - Responsive design for all screen sizes
 * - Detailed validation and helpful error messages
 */
const BirthDetailsForm = ({
  formData,
  setFormData,
  errors,
  setErrors,
  touched,
  setTouched
}) => {
  // State for location search
  const [locationInput, setLocationInput] = useState('');
  const [locationSuggestions, setLocationSuggestions] = useState([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(false);
  const [timeFormat, setTimeFormat] = useState('12h'); // '12h' or '24h'

  // Parse height from formData
  const [heightFeet, setHeightFeet] = useState(formData.heightFeet || '');
  const [heightInches, setHeightInches] = useState(formData.heightInches || '');

  const autocompleteService = useRef(null);
  const placesService = useRef(null);

  // Initialize Google Maps services
  useEffect(() => {
    if (window.google && window.google.maps) {
      autocompleteService.current = new window.google.maps.places.AutocompleteService();
      placesService.current = new window.google.maps.places.PlacesService(
        document.createElement('div')
      );
    }
  }, []);

  // Handle date change with gender-based age validation
  const handleDateChange = (date) => {
    if (!isValid(date)) return;

    setFormData({ ...formData, dateOfBirth: date });

    // Validate age based on gender
    const age = differenceInYears(new Date(), date);
    const minAge = formData.gender === 'FEMALE' ? 18 : 21;

    if (age < minAge) {
      setErrors({
        ...errors,
        dateOfBirth: `Minimum age for ${formData.gender === 'FEMALE' ? 'females' : 'males'} is ${minAge} years`
      });
    } else {
      // Clear error if valid
      const { dateOfBirth, ...restErrors } = errors;
      setErrors(restErrors);
    }

    // Mark as touched
    setTouched({ ...touched, dateOfBirth: true });
  };

  // Handle time change
  const handleTimeChange = (time) => {
    if (!isValid(time)) return;
    setFormData({ ...formData, birthTime: time });
    setTouched({ ...touched, birthTime: true });
  };

  // Toggle between 12h and 24h time formats
  const toggleTimeFormat = () => {
    setTimeFormat(prev => prev === '12h' ? '24h' : '12h');
  };

  // Handle location input change
  const handleLocationInputChange = (event, value) => {
    setLocationInput(value);

    if (!value || value.length < 3) {
      setLocationSuggestions([]);
      return;
    }

    if (autocompleteService.current) {
      setIsLoadingLocations(true);

      autocompleteService.current.getPlacePredictions(
        {
          input: value,
          types: ['(cities)'],
        },
        (predictions, status) => {
          setIsLoadingLocations(false);

          if (status !== window.google.maps.places.PlacesServiceStatus.OK || !predictions) {
            setLocationSuggestions([]);
            return;
          }

          setLocationSuggestions(predictions);
        }
      );
    }
  };

  // Handle location selection
  const handleLocationSelect = (event, value) => {
    if (!value) return;

    if (placesService.current) {
      placesService.current.getDetails(
        {
          placeId: value.place_id,
          fields: ['name', 'formatted_address', 'geometry']
        },
        (place, status) => {
          if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {
            setFormData({
              ...formData,
              birthPlace: place.formatted_address,
              birthPlaceCoordinates: {
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng()
              }
            });

            // Clear any existing error
            const { birthPlace, ...restErrors } = errors;
            setErrors(restErrors);
          }
        }
      );
    } else {
      // Fallback if Places service isn't available
      setFormData({
        ...formData,
        birthPlace: value.description
      });
    }

    setTouched({ ...touched, birthPlace: true });
  };

  // Handle height change
  const handleHeightChange = (field, value) => {
    if (field === 'feet') {
      setHeightFeet(value);
    } else {
      setHeightInches(value);
    }

    // Update form data
    setFormData({
      ...formData,
      heightFeet: field === 'feet' ? value : heightFeet,
      heightInches: field === 'inches' ? value : heightInches,
      height: formatHeight(
        field === 'feet' ? value : heightFeet,
        field === 'inches' ? value : heightInches
      )
    });

    // Validate height
    const heightValidation = validateHeight(
      field === 'feet' ? value : heightFeet,
      field === 'inches' ? value : heightInches,
      formData.gender
    );

    if (!heightValidation.isValid) {
      setErrors({
        ...errors,
        height: heightValidation.message
      });
    } else {
      // Clear error if valid
      const { height, ...restErrors } = errors;
      setErrors(restErrors);
    }

    setTouched({ ...touched, height: true });
  };

  return (
    <Paper elevation={0} sx={{ p: 3, borderRadius: 2, mb: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        Birth Details & Height
        <Tooltip title="These details are important for horoscope matching" placement="right">
          <IconButton size="small" sx={{ ml: 1 }}>
            <InfoOutlinedIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Grid container spacing={3}>
        {/* Date of Birth Field */}
        <Grid item xs={12} md={4}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <FormControl fullWidth error={!!errors.dateOfBirth && touched.dateOfBirth}>
              <FormLabel
                htmlFor="dateOfBirth"
                sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
              >
                <CalendarTodayIcon fontSize="small" sx={{ mr: 1 }} />
                Date of Birth*
              </FormLabel>
              <DatePicker
                value={formData.dateOfBirth || null}
                onChange={handleDateChange}
                disableFuture
                maxDate={new Date(new Date().setFullYear(new Date().getFullYear() - 18))}
                slotProps={{
                  textField: {
                    id: "dateOfBirth",
                    placeholder: "YYYY-MM-DD",
                    error: !!errors.dateOfBirth && touched.dateOfBirth,
                    fullWidth: true
                  }
                }}
              />
              {errors.dateOfBirth && touched.dateOfBirth && (
                <FormHelperText error>{errors.dateOfBirth}</FormHelperText>
              )}
              <FormHelperText>
                {formData.gender === 'FEMALE' ? 'Minimum age: 18 years' : 'Minimum age: 21 years'}
              </FormHelperText>
            </FormControl>
          </LocalizationProvider>
        </Grid>

        {/* Birth Time Field */}
        <Grid item xs={12} md={4}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <FormControl fullWidth error={!!errors.birthTime && touched.birthTime}>
              <FormLabel
                htmlFor="birthTime"
                sx={{ mb: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AccessTimeIcon fontSize="small" sx={{ mr: 1 }} />
                  Birth Time*
                </Box>
                <Tooltip title={`Switch to ${timeFormat === '12h' ? '24-hour' : '12-hour'} format`}>
                  <Typography
                    variant="caption"
                    sx={{ cursor: 'pointer', color: 'primary.main' }}
                    onClick={toggleTimeFormat}
                  >
                    {timeFormat === '12h' ? '12h' : '24h'}
                  </Typography>
                </Tooltip>
              </FormLabel>
              <TimePicker
                value={formData.birthTime || null}
                onChange={handleTimeChange}
                ampm={timeFormat === '12h'}
                slotProps={{
                  textField: {
                    id: "birthTime",
                    placeholder: timeFormat === '12h' ? "HH:MM AM/PM" : "HH:MM",
                    error: !!errors.birthTime && touched.birthTime,
                    fullWidth: true
                  }
                }}
              />
              {errors.birthTime && touched.birthTime && (
                <FormHelperText error>{errors.birthTime}</FormHelperText>
              )}
              <FormHelperText>
                Enter your birth time as accurately as possible
              </FormHelperText>
            </FormControl>
          </LocalizationProvider>
        </Grid>

        {/* Height Field */}
        <Grid item xs={12} md={4}>
          <HeightSelector
            feet={heightFeet}
            inches={heightInches}
            onChange={handleHeightChange}
            error={errors.height}
            touched={touched.height}
            gender={formData.gender}
          />
        </Grid>

        {/* Birth Place Field */}
        <Grid item xs={12}>
          <FormControl fullWidth error={!!errors.birthPlace && touched.birthPlace}>
            <FormLabel
              htmlFor="birthPlace"
              sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
            >
              <LocationOnIcon fontSize="small" sx={{ mr: 1 }} />
              Birth Place*
            </FormLabel>
            <Autocomplete
              id="birthPlace"
              freeSolo
              options={locationSuggestions}
              getOptionLabel={(option) =>
                typeof option === 'string' ? option : option.description
              }
              inputValue={locationInput}
              onInputChange={handleLocationInputChange}
              onChange={handleLocationSelect}
              loading={isLoadingLocations}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Enter city, state, country"
                  error={!!errors.birthPlace && touched.birthPlace}
                  helperText={touched.birthPlace && errors.birthPlace}
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {isLoadingLocations ? <CircularProgress color="inherit" size={20} /> : null}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                />
              )}
            />
            <FormHelperText>
              This helps in accurate horoscope matching
            </FormHelperText>
          </FormControl>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default BirthDetailsForm;
