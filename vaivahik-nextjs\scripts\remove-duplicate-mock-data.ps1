# Migration script to remove duplicate mock data files
# Run this script from the project root directory

# List of duplicate files to remove
$duplicateFiles = @(
    "public/mock-data/api/admin/biodata-templates/index.json",
    "public/mock-data/api/admin/dashboard/recent-activity/index.json",
    "public/mock-data/api/admin/dashboard/recent-activity.json",
    "public/mock-data/api/admin/dashboard/recent-users/index.json",
    "public/mock-data/api/admin/dashboard/recent-users.json",
    "public/mock-data/api/admin/dashboard/index.json",
    "public/mock-data/api/admin/dashboard.json",
    "public/mock-data/api/admin/users/index.json",
    "public/mock-data/api/admin/verification-queue/index.json"
)

# Function to remove a file with confirmation
function Remove-FileWithConfirmation {
    param (
        [string]$FilePath
    )
    
    # Check if the file exists
    if (Test-Path $FilePath) {
        Write-Host "Removing file: $FilePath"
        Remove-Item -Path $FilePath -Force
        Write-Host "File removed successfully." -ForegroundColor Green
    } else {
        Write-Host "File not found: $FilePath" -ForegroundColor Yellow
    }
}

# Main script
Write-Host "Starting removal of duplicate mock data files..." -ForegroundColor Cyan

# Count of files removed
$removedCount = 0

# Remove each duplicate file
foreach ($file in $duplicateFiles) {
    # Convert path to use correct slashes for the current OS
    $filePath = $file -replace "/", [System.IO.Path]::DirectorySeparatorChar
    
    # Remove the file
    Remove-FileWithConfirmation -FilePath $filePath
    
    # Increment the count
    $removedCount++
}

# Summary
Write-Host "`nRemoval complete." -ForegroundColor Cyan
Write-Host "Removed $removedCount duplicate files." -ForegroundColor Green
Write-Host "The mock data structure has been standardized." -ForegroundColor Green
