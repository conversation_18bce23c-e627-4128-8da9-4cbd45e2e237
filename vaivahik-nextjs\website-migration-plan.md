# Website Component Migration Plan

This document outlines the plan for migrating website-related components to the new website folder structure.

## Current Status

We have successfully moved the following components to the website folder structure:

- ✅ `FilterChips.js` → `website/components/common/FilterChips.js`
- ✅ `InterestsDisplay.js` → `website/components/profile/InterestsDisplay.js`
- ✅ `LifestyleForm.js` → `website/components/profile/LifestyleForm.js`
- ✅ `PartnerPreferencesForm.js` → `website/components/profile/PartnerPreferencesForm.js`

And the following pages:

- ✅ `interests-display.js` → `website/pages/examples/interests-display.js`
- ✅ `lifestyle.js` → `website/pages/profile/edit/lifestyle.js`
- ✅ `preferences.js` → `website/pages/profile/edit/preferences.js`
- ✅ `index.js` → `website/pages/profile/index.js`

## Next Components to Migrate

The following components should be moved next:

### Phase 1: Core User Experience Components

1. **Gamification Components**
   - `src/components/gamification/ProfileGamificationSystem.js` → `website/components/gamification/ProfileGamificationSystem.js`
   - Create a `website/gamification` directory for gamification-related functionality

2. **Shortlist Components**
   - `src/components/shortlist/ShortlistedProfiles.js` → `website/components/shortlist/ShortlistedProfiles.js`
   - Create a `website/shortlist` directory for shortlist-related functionality

3. **Notification Components**
   - `src/components/notification/NotificationCenter.js` → `website/components/notification/NotificationCenter.js`
   - Create a `website/notification` directory for notification-related functionality

### Phase 2: Admin and Configuration Components

4. **Admin Components**
   - `src/components/admin/MockDataToggle.js` → `website/components/admin/MockDataToggle.js`
   - Create a `website/admin` directory for admin-related functionality

### Phase 3: Search Components

5. **Search Components**
   - Move all search-related components to `website/search/components`
   - Update imports in all files that reference these components

## Directory Structure After Migration

```
website/
├── admin/                 # Admin functionality
│   └── components/        # Admin-specific components
├── components/            # Shared website components
│   ├── common/            # Common components used across features
│   └── profile/           # Profile-specific components
├── gamification/          # Gamification functionality
│   └── components/        # Gamification-specific components
├── notification/          # Notification functionality
│   └── components/        # Notification-specific components
├── pages/                 # Website pages
│   ├── examples/          # Example pages
│   └── profile/           # Profile pages
│       └── edit/          # Profile editing pages
├── search/                # Search functionality
│   └── components/        # Search-specific components
├── shortlist/             # Shortlist functionality
│   └── components/        # Shortlist-specific components
└── utils/                 # Website-specific utilities
```

## Migration Process

For each component:

1. **Create the target directory** if it doesn't exist
2. **Copy the component** to the new location
3. **Update imports** in the component to use the new paths
4. **Update references** to the component in other files
5. **Test the component** to ensure it works correctly
6. **Remove the old component** once all references have been updated

## Testing Strategy

1. **Component Testing**: Create a test page for each component to verify it works correctly
2. **Integration Testing**: Test the components in the context of the application
3. **Regression Testing**: Ensure existing functionality continues to work

## Timeline

- **Phase 1**: Complete by end of week
- **Phase 2**: Complete by end of next week
- **Phase 3**: Complete by end of month

## Risks and Mitigations

- **Risk**: Breaking existing functionality
  - **Mitigation**: Thorough testing after each component migration
  
- **Risk**: Missing references to components
  - **Mitigation**: Use codebase search to find all references

- **Risk**: Circular dependencies
  - **Mitigation**: Carefully review import structure and refactor as needed

## Conclusion

This migration plan provides a structured approach to moving website-related components to the new folder structure. By following this plan, we can ensure a smooth transition with minimal disruption to the application.
