// src/controllers/admin/blog.controller.js

/**
 * @description Get all blog posts with pagination
 * @route GET /api/admin/content/blog
 */
exports.getBlogPosts = async (req, res, next) => {
    const prisma = req.prisma;
    const { page = 1, limit = 10, search = '', category = '', status = '' } = req.query;

    try {
        // Build where clause based on filters
        let whereClause = {};
        
        if (search) {
            whereClause.OR = [
                { title: { contains: search, mode: 'insensitive' } },
                { content: { contains: search, mode: 'insensitive' } },
                { excerpt: { contains: search, mode: 'insensitive' } }
            ];
        }
        
        if (category) {
            whereClause.category = category;
        }
        
        if (status) {
            whereClause.status = status;
        }

        // Get total count for pagination
        const totalPosts = await prisma.blogPost.count({
            where: whereClause
        });

        // Get paginated posts
        const posts = await prisma.blogPost.findMany({
            where: whereClause,
            orderBy: {
                createdAt: 'desc'
            },
            skip: (parseInt(page) - 1) * parseInt(limit),
            take: parseInt(limit),
            include: {
                author: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        // Get unique categories for filtering
        const categories = await prisma.blogPost.findMany({
            select: {
                category: true
            },
            distinct: ['category']
        });

        res.status(200).json({
            success: true,
            posts,
            pagination: {
                total: totalPosts,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(totalPosts / parseInt(limit))
            },
            categories: categories.map(c => c.category)
        });
    } catch (error) {
        console.error("Error fetching blog posts:", error);
        next(error);
    }
};

/**
 * @description Get a single blog post by ID
 * @route GET /api/admin/content/blog/:id
 */
exports.getBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        const post = await prisma.blogPost.findUnique({
            where: { id },
            include: {
                author: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        if (!post) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found"
            });
        }

        res.status(200).json({
            success: true,
            post
        });
    } catch (error) {
        console.error("Error fetching blog post:", error);
        next(error);
    }
};

/**
 * @description Create a new blog post
 * @route POST /api/admin/content/blog
 */
exports.createBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { title, slug, excerpt, content, featuredImage, category, tags, status } = req.body;
    const adminId = req.admin.id;

    try {
        // Validate required fields
        if (!title || !slug) {
            return res.status(400).json({
                success: false,
                message: "Title and slug are required"
            });
        }

        // Check if slug is unique
        const existingPost = await prisma.blogPost.findUnique({
            where: { slug }
        });

        if (existingPost) {
            return res.status(400).json({
                success: false,
                message: "A post with this slug already exists"
            });
        }

        // Create the blog post
        const post = await prisma.blogPost.create({
            data: {
                title,
                slug,
                excerpt: excerpt || title,
                content: content || "",
                featuredImage: featuredImage || "",
                category: category || "UNCATEGORIZED",
                tags: tags || [],
                status: status || "DRAFT",
                publishedAt: status === "PUBLISHED" ? new Date() : null,
                author: {
                    connect: { id: adminId }
                }
            }
        });

        res.status(201).json({
            success: true,
            message: "Blog post created successfully",
            post
        });
    } catch (error) {
        console.error("Error creating blog post:", error);
        next(error);
    }
};

/**
 * @description Update a blog post
 * @route PUT /api/admin/content/blog/:id
 */
exports.updateBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;
    const { title, slug, excerpt, content, featuredImage, category, tags, status } = req.body;

    try {
        // Check if post exists
        const existingPost = await prisma.blogPost.findUnique({
            where: { id }
        });

        if (!existingPost) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found"
            });
        }

        // Check if slug is unique (if changed)
        if (slug !== existingPost.slug) {
            const slugExists = await prisma.blogPost.findFirst({
                where: {
                    slug,
                    id: { not: id }
                }
            });

            if (slugExists) {
                return res.status(400).json({
                    success: false,
                    message: "A post with this slug already exists"
                });
            }
        }

        // Update the blog post
        const updatedPost = await prisma.blogPost.update({
            where: { id },
            data: {
                title: title || existingPost.title,
                slug: slug || existingPost.slug,
                excerpt: excerpt || existingPost.excerpt,
                content: content !== undefined ? content : existingPost.content,
                featuredImage: featuredImage !== undefined ? featuredImage : existingPost.featuredImage,
                category: category || existingPost.category,
                tags: tags || existingPost.tags,
                status: status || existingPost.status,
                publishedAt: status === "PUBLISHED" && !existingPost.publishedAt ? new Date() : existingPost.publishedAt
            }
        });

        res.status(200).json({
            success: true,
            message: "Blog post updated successfully",
            post: updatedPost
        });
    } catch (error) {
        console.error("Error updating blog post:", error);
        next(error);
    }
};

/**
 * @description Delete a blog post
 * @route DELETE /api/admin/content/blog/:id
 */
exports.deleteBlogPost = async (req, res, next) => {
    const prisma = req.prisma;
    const { id } = req.params;

    try {
        // Check if post exists
        const existingPost = await prisma.blogPost.findUnique({
            where: { id }
        });

        if (!existingPost) {
            return res.status(404).json({
                success: false,
                message: "Blog post not found"
            });
        }

        // Delete the blog post
        await prisma.blogPost.delete({
            where: { id }
        });

        res.status(200).json({
            success: true,
            message: "Blog post deleted successfully"
        });
    } catch (error) {
        console.error("Error deleting blog post:", error);
        next(error);
    }
};
