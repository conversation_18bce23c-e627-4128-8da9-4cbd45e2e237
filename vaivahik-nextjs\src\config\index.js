/**
 * Application Configuration
 * 
 * This file contains configuration settings for the application.
 */

// Default feature flags
export const FEATURE_FLAGS = {
  // Data source
  useRealBackend: false, // Whether to use real backend API or mock data

  // Feature toggles
  enableBiodataTemplates: true,
  enableSpotlightFeatures: true,
  enablePaymentGateway: false,
  enableNotifications: true,
  enableMatchingAlgorithm: true,
  
  // UI preferences
  enableDarkMode: false,
  enableAnimations: true,
  showMockDataIndicator: true,
  
  // Development
  enableDebugLogging: process.env.NODE_ENV === 'development'
};

// API configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 30000, // 30 seconds
  retryAttempts: 3
};

// Authentication configuration
export const AUTH_CONFIG = {
  tokenKey: 'authToken',
  refreshTokenKey: 'refreshToken',
  tokenExpiry: 60 * 60 * 24 * 7, // 7 days in seconds
  loginPath: '/admin/login',
  dashboardPath: '/admin/dashboard'
};

// Default export for convenience
export default {
  FEATURE_FLAGS,
  API_CONFIG,
  AUTH_CONFIG
};
