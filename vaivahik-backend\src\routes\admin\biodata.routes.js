// src/routes/admin/biodata.routes.js
const express = require('express');
const router = express.Router();
const biodataController = require('../../controllers/admin/biodata.controller');
const { isAdmin } = require('../../middleware/auth.middleware');
const upload = require('../../middleware/upload.middleware');

// Apply admin authentication middleware to all routes
router.use(isAdmin);

// Get all biodata templates
router.get('/templates', biodataController.getBiodataTemplates);

// Get biodata template by ID
router.get('/templates/:id', biodataController.getBiodataTemplateById);

// Create a new biodata template
router.post('/templates', 
    upload.fields([
        { name: 'previewImage', maxCount: 1 },
        { name: 'designFile', maxCount: 1 }
    ]), 
    biodataController.createBiodataTemplate
);

// Update a biodata template
router.put('/templates/:id', 
    upload.fields([
        { name: 'previewImage', maxCount: 1 },
        { name: 'designFile', maxCount: 1 }
    ]), 
    biodataController.updateBiodataTemplate
);

// Delete a biodata template
router.delete('/templates/:id', biodataController.deleteBiodataTemplate);

module.exports = router;
