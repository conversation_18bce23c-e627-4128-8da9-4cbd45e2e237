// src/pages/api/admin/algorithm-settings.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);

  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Handle GET request - fetch algorithm settings
  if (req.method === 'GET') {
    try {
      const includeABTestResults = req.query.includeABTestResults === 'true';
      const includeMetrics = req.query.includeMetrics === 'true';

      // Fetch settings from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/ai/settings`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      });

      if (!response.ok) {
        throw new Error(`Backend API error: ${response.status}`);
      }

      const data = await response.json();

      // Process the data to match the frontend structure
      const settings = processSettingsForFrontend(data.settings);

      // Prepare response
      const responseData = {
        success: true,
        settings
      };

      // Add A/B test results if requested
      if (includeABTestResults) {
        // Fetch A/B test results from backend
        const abTestResponse = await fetch(`${process.env.BACKEND_API_URL}/api/admin/ai/ab-tests`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.accessToken}`
          }
        }).catch(() => null);

        if (abTestResponse && abTestResponse.ok) {
          const abTestData = await abTestResponse.json();
          responseData.abTestResults = abTestData.results;
        } else {
          // Fallback to mock data if backend API fails
          responseData.abTestResults = getMockABTestResults();
        }
      }

      // Add metrics if requested
      if (includeMetrics) {
        // Fetch metrics from backend
        const metricsResponse = await fetch(`${process.env.BACKEND_API_URL}/api/admin/ai/analytics`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.accessToken}`
          }
        }).catch(() => null);

        if (metricsResponse && metricsResponse.ok) {
          const metricsData = await metricsResponse.json();
          responseData.metrics = metricsData.metrics;
        } else {
          // Fallback to mock data if backend API fails
          responseData.metrics = getMockMetrics();
        }
      }

      return res.status(200).json(responseData);
    } catch (error) {
      console.error('Error fetching algorithm settings:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch algorithm settings',
        error: error.message
      });
    }
  }

  // Handle PUT request - update algorithm settings
  if (req.method === 'PUT') {
    try {
      const { settings } = req.body;

      if (!settings) {
        return res.status(400).json({
          success: false,
          message: 'Settings object is required'
        });
      }

      // Convert frontend settings to backend format
      const backendSettings = processSettingsForBackend(settings);

      // Send to backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/ai/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        },
        body: JSON.stringify({ settings: backendSettings })
      });

      if (!response.ok) {
        throw new Error(`Backend API error: ${response.status}`);
      }

      const data = await response.json();

      return res.status(200).json({
        success: true,
        message: 'Algorithm settings updated successfully',
        settings: settings // Return the original settings for now
      });
    } catch (error) {
      console.error('Error updating algorithm settings:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update algorithm settings',
        error: error.message
      });
    }
  }

  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to process settings from backend to frontend format
function processSettingsForFrontend(backendSettings) {
  // If backend API is not available yet, return mock data
  if (!backendSettings) {
    return getMockSettings();
  }

  try {
    // Initialize with default structure
    const frontendSettings = {
      matchingAlgorithmVersion: 'v1.0',
      enableAIMatching: true,
      matchingModel: 'TWO_TOWER',
      weights: {},
      minimumMatchScore: 65,
      highQualityMatchThreshold: 80,
      abTestingEnabled: false,
      abTestingVariant: 'A',
      abTestingDistribution: 50,
      maxDistanceKm: 100,
      maxAgeDifference: 10,
      considerUserActivity: true,
      boostNewProfiles: true,
      boostNewProfilesDays: 7,
      boostVerifiedProfiles: true,
      boostVerifiedProfilesAmount: 10,
      boostPremiumProfiles: true,
      boostPremiumProfilesAmount: 15,
      twoTower: {
        embeddingSize: 128,
        learningRate: 0.001,
        batchSize: 64,
        epochs: 10,
        userTowerLayers: [128, 64],
        matchTowerLayers: [128, 64],
        dropoutRate: 0.2,
        similarityMetric: 'cosine'
      }
    };

    // Map backend settings to frontend format
    if (backendSettings.GENERAL) {
      backendSettings.GENERAL.forEach(setting => {
        if (setting.settingKey === 'matchingAlgorithmVersion') {
          frontendSettings.matchingAlgorithmVersion = setting.settingValue;
        } else if (setting.settingKey === 'enableAIMatching') {
          frontendSettings.enableAIMatching = setting.settingValue;
        } else if (setting.settingKey === 'matchingModel') {
          frontendSettings.matchingModel = setting.settingValue;
        }
      });
    }

    if (backendSettings.WEIGHTS) {
      backendSettings.WEIGHTS.forEach(setting => {
        frontendSettings.weights[setting.settingKey] = setting.settingValue;
      });
    }

    if (backendSettings.THRESHOLDS) {
      backendSettings.THRESHOLDS.forEach(setting => {
        if (setting.settingKey === 'minimumMatchScore') {
          frontendSettings.minimumMatchScore = setting.settingValue;
        } else if (setting.settingKey === 'highQualityMatchThreshold') {
          frontendSettings.highQualityMatchThreshold = setting.settingValue;
        }
      });
    }

    if (backendSettings.AB_TESTING) {
      backendSettings.AB_TESTING.forEach(setting => {
        if (setting.settingKey === 'abTestingEnabled') {
          frontendSettings.abTestingEnabled = setting.settingValue;
        } else if (setting.settingKey === 'abTestingVariant') {
          frontendSettings.abTestingVariant = setting.settingValue;
        } else if (setting.settingKey === 'abTestingDistribution') {
          frontendSettings.abTestingDistribution = setting.settingValue;
        }
      });
    }

    if (backendSettings.ADVANCED) {
      backendSettings.ADVANCED.forEach(setting => {
        if (setting.settingKey === 'maxDistanceKm') {
          frontendSettings.maxDistanceKm = setting.settingValue;
        } else if (setting.settingKey === 'maxAgeDifference') {
          frontendSettings.maxAgeDifference = setting.settingValue;
        } else if (setting.settingKey === 'considerUserActivity') {
          frontendSettings.considerUserActivity = setting.settingValue;
        } else if (setting.settingKey === 'boostNewProfiles') {
          frontendSettings.boostNewProfiles = setting.settingValue;
        } else if (setting.settingKey === 'boostNewProfilesDays') {
          frontendSettings.boostNewProfilesDays = setting.settingValue;
        } else if (setting.settingKey === 'boostVerifiedProfiles') {
          frontendSettings.boostVerifiedProfiles = setting.settingValue;
        } else if (setting.settingKey === 'boostVerifiedProfilesAmount') {
          frontendSettings.boostVerifiedProfilesAmount = setting.settingValue;
        } else if (setting.settingKey === 'boostPremiumProfiles') {
          frontendSettings.boostPremiumProfiles = setting.settingValue;
        } else if (setting.settingKey === 'boostPremiumProfilesAmount') {
          frontendSettings.boostPremiumProfilesAmount = setting.settingValue;
        }
      });
    }

    if (backendSettings.TWO_TOWER) {
      backendSettings.TWO_TOWER.forEach(setting => {
        if (setting.settingKey === 'embeddingSize') {
          frontendSettings.twoTower.embeddingSize = setting.settingValue;
        } else if (setting.settingKey === 'learningRate') {
          frontendSettings.twoTower.learningRate = setting.settingValue;
        } else if (setting.settingKey === 'batchSize') {
          frontendSettings.twoTower.batchSize = setting.settingValue;
        } else if (setting.settingKey === 'epochs') {
          frontendSettings.twoTower.epochs = setting.settingValue;
        } else if (setting.settingKey === 'userTowerLayers') {
          frontendSettings.twoTower.userTowerLayers = setting.settingValue;
        } else if (setting.settingKey === 'matchTowerLayers') {
          frontendSettings.twoTower.matchTowerLayers = setting.settingValue;
        } else if (setting.settingKey === 'dropoutRate') {
          frontendSettings.twoTower.dropoutRate = setting.settingValue;
        } else if (setting.settingKey === 'similarityMetric') {
          frontendSettings.twoTower.similarityMetric = setting.settingValue;
        }
      });
    }

    return frontendSettings;
  } catch (error) {
    console.error('Error processing settings for frontend:', error);
    return getMockSettings();
  }
}

// Helper function to process settings from frontend to backend format
function processSettingsForBackend(frontendSettings) {
  // Convert frontend settings to array format expected by backend
  const backendSettings = [];

  // Process general settings
  backendSettings.push({
    id: 'matchingAlgorithmVersion',
    settingKey: 'matchingAlgorithmVersion',
    settingValue: frontendSettings.matchingAlgorithmVersion,
    category: 'GENERAL',
    dataType: 'STRING'
  });

  backendSettings.push({
    id: 'enableAIMatching',
    settingKey: 'enableAIMatching',
    settingValue: frontendSettings.enableAIMatching,
    category: 'GENERAL',
    dataType: 'BOOLEAN'
  });

  backendSettings.push({
    id: 'matchingModel',
    settingKey: 'matchingModel',
    settingValue: frontendSettings.matchingModel,
    category: 'GENERAL',
    dataType: 'STRING'
  });

  // Process weights
  Object.entries(frontendSettings.weights).forEach(([key, value]) => {
    backendSettings.push({
      id: key,
      settingKey: key,
      settingValue: value,
      category: 'WEIGHTS',
      dataType: 'NUMBER'
    });
  });

  // Process thresholds
  backendSettings.push({
    id: 'minimumMatchScore',
    settingKey: 'minimumMatchScore',
    settingValue: frontendSettings.minimumMatchScore,
    category: 'THRESHOLDS',
    dataType: 'NUMBER'
  });

  backendSettings.push({
    id: 'highQualityMatchThreshold',
    settingKey: 'highQualityMatchThreshold',
    settingValue: frontendSettings.highQualityMatchThreshold,
    category: 'THRESHOLDS',
    dataType: 'NUMBER'
  });

  // Process A/B testing settings
  backendSettings.push({
    id: 'abTestingEnabled',
    settingKey: 'abTestingEnabled',
    settingValue: frontendSettings.abTestingEnabled,
    category: 'AB_TESTING',
    dataType: 'BOOLEAN'
  });

  backendSettings.push({
    id: 'abTestingVariant',
    settingKey: 'abTestingVariant',
    settingValue: frontendSettings.abTestingVariant,
    category: 'AB_TESTING',
    dataType: 'STRING'
  });

  backendSettings.push({
    id: 'abTestingDistribution',
    settingKey: 'abTestingDistribution',
    settingValue: frontendSettings.abTestingDistribution,
    category: 'AB_TESTING',
    dataType: 'NUMBER'
  });

  // Process advanced settings
  backendSettings.push({
    id: 'maxDistanceKm',
    settingKey: 'maxDistanceKm',
    settingValue: frontendSettings.maxDistanceKm,
    category: 'ADVANCED',
    dataType: 'NUMBER'
  });

  backendSettings.push({
    id: 'maxAgeDifference',
    settingKey: 'maxAgeDifference',
    settingValue: frontendSettings.maxAgeDifference,
    category: 'ADVANCED',
    dataType: 'NUMBER'
  });

  backendSettings.push({
    id: 'considerUserActivity',
    settingKey: 'considerUserActivity',
    settingValue: frontendSettings.considerUserActivity,
    category: 'ADVANCED',
    dataType: 'BOOLEAN'
  });

  backendSettings.push({
    id: 'boostNewProfiles',
    settingKey: 'boostNewProfiles',
    settingValue: frontendSettings.boostNewProfiles,
    category: 'ADVANCED',
    dataType: 'BOOLEAN'
  });

  backendSettings.push({
    id: 'boostNewProfilesDays',
    settingKey: 'boostNewProfilesDays',
    settingValue: frontendSettings.boostNewProfilesDays,
    category: 'ADVANCED',
    dataType: 'NUMBER'
  });

  backendSettings.push({
    id: 'boostVerifiedProfiles',
    settingKey: 'boostVerifiedProfiles',
    settingValue: frontendSettings.boostVerifiedProfiles,
    category: 'ADVANCED',
    dataType: 'BOOLEAN'
  });

  backendSettings.push({
    id: 'boostVerifiedProfilesAmount',
    settingKey: 'boostVerifiedProfilesAmount',
    settingValue: frontendSettings.boostVerifiedProfilesAmount,
    category: 'ADVANCED',
    dataType: 'NUMBER'
  });

  backendSettings.push({
    id: 'boostPremiumProfiles',
    settingKey: 'boostPremiumProfiles',
    settingValue: frontendSettings.boostPremiumProfiles,
    category: 'ADVANCED',
    dataType: 'BOOLEAN'
  });

  backendSettings.push({
    id: 'boostPremiumProfilesAmount',
    settingKey: 'boostPremiumProfilesAmount',
    settingValue: frontendSettings.boostPremiumProfilesAmount,
    category: 'ADVANCED',
    dataType: 'NUMBER'
  });

  // Process two-tower model settings
  Object.entries(frontendSettings.twoTower).forEach(([key, value]) => {
    // Handle array values
    const processedValue = Array.isArray(value) ? JSON.stringify(value) : value;
    const dataType = Array.isArray(value) ? 'JSON' : (typeof value === 'number' ? 'NUMBER' : 'STRING');

    backendSettings.push({
      id: key,
      settingKey: key,
      settingValue: processedValue,
      category: 'TWO_TOWER',
      dataType: dataType
    });
  });

  return backendSettings;
}

// Helper function to get mock settings when backend is not available
function getMockSettings() {
  return {
    matchingAlgorithmVersion: 'v1.0',
    enableAIMatching: true,
    matchingModel: 'TWO_TOWER',
    weights: {
      ageWeight: 8,
      heightWeight: 6,
      educationWeight: 7,
      occupationWeight: 7,
      locationWeight: 8,
      casteWeight: 9,
      subCasteWeight: 5,
      gotraWeight: 6,
      incomeWeight: 5,
      lifestyleWeight: 4
    },
    minimumMatchScore: 65,
    highQualityMatchThreshold: 80,
    abTestingEnabled: false,
    abTestingVariant: 'A',
    abTestingDistribution: 50,
    maxDistanceKm: 100,
    maxAgeDifference: 10,
    considerUserActivity: true,
    boostNewProfiles: true,
    boostNewProfilesDays: 7,
    boostVerifiedProfiles: true,
    boostVerifiedProfilesAmount: 10,
    boostPremiumProfiles: true,
    boostPremiumProfilesAmount: 15,
    twoTower: {
      embeddingSize: 128,
      learningRate: 0.001,
      batchSize: 64,
      epochs: 10,
      userTowerLayers: [128, 64],
      matchTowerLayers: [128, 64],
      dropoutRate: 0.2,
      similarityMetric: 'cosine'
    }
  };
}

// Helper function to get mock A/B test results
function getMockABTestResults() {
  return {
    variantA: {
      matches: 245,
      conversations: 156,
      successRate: 63.7
    },
    variantB: {
      matches: 267,
      conversations: 182,
      successRate: 68.2
    }
  };
}

// Helper function to get mock metrics
function getMockMetrics() {
  return {
    totalMatches: 5842,
    successfulMatches: 2156,
    averageMatchScore: 72.4,
    matchDistribution: [12, 18, 25, 30, 15],
    monthlyTrend: [120, 145, 160, 178, 195, 210]
  };
}