import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  CardMedia,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Stack,
  CircularProgress,
  Alert,
  Snackbar,
  Divider,
  Tooltip,
  Pagination
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { styled } from '@mui/material/styles';
import { useRouter } from 'next/router';

// Rich text editor component (placeholder)
const RichTextEditor = ({ value, onChange }) => {
  return (
    <TextField
      fullWidth
      multiline
      rows={10}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder="Write your blog post content here..."
      variant="outlined"
    />
  );
};

// Styled components
const BlogCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.shadows[10],
  },
}));

const BlogCardMedia = styled(CardMedia)(({ theme }) => ({
  paddingTop: '56.25%', // 16:9 aspect ratio
  position: 'relative',
}));

const BlogCardContent = styled(CardContent)({
  flexGrow: 1,
});

const CategoryChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
}));

export default function BlogPosts() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [blogPosts, setBlogPosts] = useState([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState('create'); // 'create' or 'edit'
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    featuredImage: '',
    category: 'SUCCESS_STORY',
    tags: '',
    status: 'DRAFT'
  });

  useEffect(() => {
    fetchBlogPosts();
  }, [page]);

  const fetchBlogPosts = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/blog-posts?page=${page}&limit=6`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setBlogPosts(data.posts);
        setTotalPages(data.totalPages);
      } else {
        throw new Error(data.message || 'Failed to fetch blog posts');
      }
    } catch (err) {
      console.error('Error fetching blog posts:', err);
      setError('Failed to load blog posts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (event, value) => {
    setPage(value);
  };

  const handleOpenCreateDialog = () => {
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      featuredImage: '',
      category: 'SUCCESS_STORY',
      tags: '',
      status: 'DRAFT'
    });
    setDialogMode('create');
    setOpenDialog(true);
  };

  const handleOpenEditDialog = (post) => {
    setFormData({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      featuredImage: post.featuredImage,
      category: post.category,
      tags: Array.isArray(post.tags) ? post.tags.join(', ') : post.tags,
      status: post.status
    });
    setDialogMode('edit');
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleOpenDeleteConfirm = (post) => {
    setSelectedPost(post);
    setDeleteConfirmOpen(true);
  };

  const handleCloseDeleteConfirm = () => {
    setDeleteConfirmOpen(false);
    setSelectedPost(null);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContentChange = (content) => {
    setFormData(prev => ({
      ...prev,
      content
    }));
  };

  const generateSlug = () => {
    const slug = formData.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
    
    setFormData(prev => ({
      ...prev,
      slug
    }));
  };

  const handleSubmit = async () => {
    try {
      const method = dialogMode === 'create' ? 'POST' : 'PUT';
      const url = dialogMode === 'create' 
        ? '/api/admin/blog-posts' 
        : `/api/admin/blog-posts/${formData.id}`;
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setSuccess(dialogMode === 'create' ? 'Blog post created successfully!' : 'Blog post updated successfully!');
        handleCloseDialog();
        fetchBlogPosts();
      } else {
        throw new Error(data.message || 'Failed to save blog post');
      }
    } catch (err) {
      console.error('Error saving blog post:', err);
      setError('Failed to save blog post. Please try again.');
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/admin/blog-posts/${selectedPost.id}`, {
        method: 'DELETE'
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setSuccess('Blog post deleted successfully!');
        handleCloseDeleteConfirm();
        fetchBlogPosts();
      } else {
        throw new Error(data.message || 'Failed to delete blog post');
      }
    } catch (err) {
      console.error('Error deleting blog post:', err);
      setError('Failed to delete blog post. Please try again.');
      handleCloseDeleteConfirm();
    }
  };

  const handlePreview = (post) => {
    // In a real implementation, this would open a preview of the blog post
    router.push(`/admin/blog-posts/preview/${post.id}`);
  };

  const handleCloseSnackbar = () => {
    setSuccess(null);
    setError(null);
  };

  return (
    <EnhancedAdminLayout title="Blog Posts Management">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">Blog Posts Management</Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenCreateDialog}
          >
            Create New Post
          </Button>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        ) : blogPosts.length === 0 ? (
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="h6" color="textSecondary" gutterBottom>
              No blog posts found
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              Create your first blog post to get started.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleOpenCreateDialog}
            >
              Create New Post
            </Button>
          </Paper>
        ) : (
          <>
            <Grid container spacing={3}>
              {blogPosts.map((post) => (
                <Grid item xs={12} sm={6} md={4} key={post.id}>
                  <BlogCard>
                    <BlogCardMedia
                      image={post.featuredImage || '/images/default-blog-image.jpg'}
                      title={post.title}
                    />
                    <BlogCardContent>
                      <Typography gutterBottom variant="h5" component="h2">
                        {post.title}
                      </Typography>
                      <Box sx={{ mb: 1 }}>
                        <Chip
                          size="small"
                          label={post.status}
                          color={post.status === 'PUBLISHED' ? 'success' : 'default'}
                          sx={{ mr: 1 }}
                        />
                        <CategoryChip
                          size="small"
                          label={post.category.replace('_', ' ')}
                          color="primary"
                          variant="outlined"
                        />
                      </Box>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {post.excerpt}
                      </Typography>
                      <Box sx={{ mt: 1 }}>
                        {post.tags && post.tags.map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag}
                            size="small"
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))}
                      </Box>
                    </BlogCardContent>
                    <Divider />
                    <CardActions>
                      <Tooltip title="Preview">
                        <IconButton onClick={() => handlePreview(post)}>
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton onClick={() => handleOpenEditDialog(post)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton onClick={() => handleOpenDeleteConfirm(post)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </CardActions>
                  </BlogCard>
                </Grid>
              ))}
            </Grid>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
              />
            </Box>
          </>
        )}

        {/* Create/Edit Dialog */}
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogMode === 'create' ? 'Create New Blog Post' : 'Edit Blog Post'}
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  onBlur={formData.slug === '' ? generateSlug : undefined}
                  required
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  label="Slug"
                  name="slug"
                  value={formData.slug}
                  onChange={handleInputChange}
                  required
                  margin="normal"
                  helperText="URL-friendly version of the title"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Button
                  variant="outlined"
                  onClick={generateSlug}
                  sx={{ mt: 3 }}
                  fullWidth
                >
                  Generate from Title
                </Button>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Excerpt"
                  name="excerpt"
                  value={formData.excerpt}
                  onChange={handleInputChange}
                  multiline
                  rows={2}
                  margin="normal"
                  helperText="A short summary of the blog post"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Content
                </Typography>
                <RichTextEditor
                  value={formData.content}
                  onChange={handleContentChange}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Featured Image URL"
                  name="featuredImage"
                  value={formData.featuredImage}
                  onChange={handleInputChange}
                  margin="normal"
                  helperText="URL to the featured image"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Category</InputLabel>
                  <Select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    label="Category"
                  >
                    <MenuItem value="SUCCESS_STORY">Success Story</MenuItem>
                    <MenuItem value="RELATIONSHIP_ADVICE">Relationship Advice</MenuItem>
                    <MenuItem value="WEDDING_PLANNING">Wedding Planning</MenuItem>
                    <MenuItem value="MATRIMONY_TIPS">Matrimony Tips</MenuItem>
                    <MenuItem value="COMMUNITY_NEWS">Community News</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    label="Status"
                  >
                    <MenuItem value="DRAFT">Draft</MenuItem>
                    <MenuItem value="PUBLISHED">Published</MenuItem>
                    <MenuItem value="ARCHIVED">Archived</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Tags"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  margin="normal"
                  helperText="Comma-separated list of tags"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancel</Button>
            <Button onClick={handleSubmit} variant="contained" color="primary">
              {dialogMode === 'create' ? 'Create' : 'Update'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteConfirmOpen} onClose={handleCloseDeleteConfirm}>
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete the blog post "{selectedPost?.title}"? This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeleteConfirm}>Cancel</Button>
            <Button onClick={handleDelete} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Success/Error Snackbar */}
        <Snackbar
          open={!!success}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity="success">
            {success}
          </Alert>
        </Snackbar>

        <Snackbar
          open={!!error}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity="error">
            {error}
          </Alert>
        </Snackbar>
      </Box>
    </EnhancedAdminLayout>
  );
}
