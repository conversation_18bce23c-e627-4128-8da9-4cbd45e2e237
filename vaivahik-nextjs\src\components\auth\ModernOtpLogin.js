/**
 * Modern OTP-based Login Component
 * Beautiful, aesthetic login with 6-digit OTP and automatic +91 country code
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Fade,
  Slide,
  useTheme,
  styled,
  InputAdornment,
  IconButton,
  Chip
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckIcon,
  ArrowBack as BackIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import ModernOtpWidget from './ModernOtpWidget';

// Styled components for modern aesthetic
const StyledContainer = styled(Container)(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: `
    linear-gradient(135deg,
      rgba(255, 182, 193, 0.1) 0%,
      rgba(255, 240, 245, 0.2) 25%,
      rgba(248, 249, 250, 0.3) 50%,
      rgba(255, 228, 225, 0.2) 75%,
      rgba(255, 182, 193, 0.1) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 228, 225, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23FFB6C1' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    `,
    pointerEvents: 'none'
  }
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: 24,
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: `
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 32px rgba(255, 182, 193, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.8)
  `,
  maxWidth: 480,
  width: '100%',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    background: 'linear-gradient(90deg, #FFB6C1, #FF69B4, #FFB6C1)',
    borderRadius: '24px 24px 0 0'
  }
}));

const PhoneInput = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    transition: 'all 0.3s ease',
    '&:hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 12px rgba(255, 182, 193, 0.2)'
    },
    '&.Mui-focused': {
      backgroundColor: 'rgba(255, 255, 255, 1)',
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 24px rgba(255, 182, 193, 0.3)'
    }
  },
  '& .MuiInputLabel-root': {
    fontWeight: 500,
    color: '#666'
  }
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: 16,
  padding: theme.spacing(1.5, 3),
  fontWeight: 600,
  fontSize: '1rem',
  textTransform: 'none',
  background: 'linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%)',
  boxShadow: '0 4px 16px rgba(255, 105, 180, 0.3)',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'linear-gradient(135deg, #FF1493 0%, #FF69B4 100%)',
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 24px rgba(255, 105, 180, 0.4)'
  },
  '&:active': {
    transform: 'translateY(0px)'
  },
  '&.Mui-disabled': {
    background: 'linear-gradient(135deg, #ccc 0%, #ddd 100%)',
    color: '#999'
  }
}));

const SecondaryButton = styled(Button)(({ theme }) => ({
  borderRadius: 16,
  padding: theme.spacing(1, 2),
  fontWeight: 500,
  textTransform: 'none',
  color: '#FF69B4',
  border: '2px solid rgba(255, 105, 180, 0.3)',
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: 'rgba(255, 105, 180, 0.1)',
    borderColor: '#FF69B4',
    transform: 'translateY(-1px)'
  }
}));

const CountryCodeChip = styled(Chip)(({ theme }) => ({
  backgroundColor: 'rgba(255, 105, 180, 0.1)',
  color: '#FF1493',
  fontWeight: 600,
  border: '1px solid rgba(255, 105, 180, 0.3)',
  '& .MuiChip-label': {
    fontSize: '0.9rem'
  }
}));

const ModernOtpLogin = ({ onLogin, loading = false, error = '', success = '' }) => {
  const [step, setStep] = useState(1); // 1: Phone input, 2: OTP verification
  const [phone, setPhone] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [otpLoading, setOtpLoading] = useState(false);
  const [otpError, setOtpError] = useState('');
  const [otpSuccess, setOtpSuccess] = useState('');
  const theme = useTheme();

  // Validate Indian phone number
  const validatePhone = (phoneNumber) => {
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    if (!cleanPhone) {
      return 'Phone number is required';
    }
    if (cleanPhone.length !== 10) {
      return 'Please enter a valid 10-digit phone number';
    }
    if (!/^[6-9]/.test(cleanPhone)) {
      return 'Phone number must start with 6, 7, 8, or 9';
    }
    return '';
  };

  // Handle phone input change
  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, ''); // Only digits
    if (value.length <= 10) {
      setPhone(value);
      setPhoneError('');
    }
  };

  // Handle send OTP
  const handleSendOtp = async () => {
    const validation = validatePhone(phone);
    if (validation) {
      setPhoneError(validation);
      return;
    }

    setOtpLoading(true);
    setPhoneError('');

    try {
      // Call your backend API to send OTP
      const response = await fetch('/api/users/request-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mobile: `91${phone}` // Add country code
        }),
      });

      const data = await response.json();

      if (data.success) {
        setStep(2);
        setOtpSuccess('OTP sent successfully! Please check your SMS.');
      } else {
        setPhoneError(data.message || 'Failed to send OTP');
      }
    } catch (err) {
      setPhoneError('Network error. Please try again.');
    } finally {
      setOtpLoading(false);
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async (otp) => {
    setOtpError('');
    setOtpLoading(true);

    try {
      const response = await fetch('/api/users/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mobile: `91${phone}`,
          otp: otp
        }),
      });

      const data = await response.json();

      if (data.success) {
        setOtpSuccess('Login successful! Redirecting...');
        // Call parent login handler with complete data including access token
        onLogin({
          success: true,
          accessToken: data.accessToken,
          userId: data.userId,
          isNewUser: data.isNewUser,
          profileStatus: data.profileStatus,
          message: data.message
        });
      } else {
        setOtpError(data.message || 'Invalid OTP. Please try again.');
      }
    } catch (err) {
      setOtpError('Network error. Please try again.');
    } finally {
      setOtpLoading(false);
    }
  };

  // Handle resend OTP
  const handleResendOtp = async () => {
    setOtpError('');
    setOtpSuccess('');

    try {
      const response = await fetch('/api/users/resend-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mobile: `91${phone}`
        }),
      });

      const data = await response.json();

      if (data.success) {
        setOtpSuccess('OTP resent successfully!');
      } else {
        setOtpError(data.message || 'Failed to resend OTP');
      }
    } catch (err) {
      setOtpError('Network error. Please try again.');
    }
  };

  // Handle change phone number
  const handleChangePhone = () => {
    setStep(1);
    setOtpError('');
    setOtpSuccess('');
  };

  return (
    <StyledContainer maxWidth="sm">
      <Slide direction="up" in={true} mountOnEnter unmountOnExit>
        <StyledPaper elevation={0}>
          {/* Header */}
          <Box textAlign="center" mb={4}>
            <Box
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 16px',
                boxShadow: '0 8px 24px rgba(255, 105, 180, 0.3)'
              }}
            >
              {step === 1 ? (
                <PhoneIcon sx={{ fontSize: 40, color: 'white' }} />
              ) : (
                <SecurityIcon sx={{ fontSize: 40, color: 'white' }} />
              )}
            </Box>

            <Typography variant="h4" fontWeight="700" color="#333" gutterBottom>
              {step === 1 ? 'Welcome Back' : 'Verify OTP'}
            </Typography>

            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 300, mx: 'auto' }}>
              {step === 1
                ? 'Enter your phone number to receive a secure 6-digit OTP'
                : `We've sent a 6-digit code to +91 ${phone}`
              }
            </Typography>
          </Box>

          {/* Step 1: Phone Input */}
          {step === 1 && (
            <Fade in={step === 1}>
              <Box>
                <Box mb={3}>
                  <PhoneInput
                    fullWidth
                    label="Phone Number"
                    value={phone}
                    onChange={handlePhoneChange}
                    error={!!phoneError}
                    helperText={phoneError}
                    placeholder="Enter 10-digit number"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <CountryCodeChip label="+91" size="small" />
                        </InputAdornment>
                      ),
                      endAdornment: phone && (
                        <InputAdornment position="end">
                          <CheckIcon sx={{ color: '#4CAF50' }} />
                        </InputAdornment>
                      )
                    }}
                  />
                </Box>

                {error && (
                  <Alert severity="error" sx={{ mb: 2, borderRadius: 2 }}>
                    {error}
                  </Alert>
                )}

                {success && (
                  <Alert severity="success" sx={{ mb: 2, borderRadius: 2 }}>
                    {success}
                  </Alert>
                )}

                <StyledButton
                  fullWidth
                  size="large"
                  onClick={handleSendOtp}
                  disabled={otpLoading || !phone || phone.length !== 10}
                  startIcon={otpLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                >
                  {otpLoading ? 'Sending OTP...' : 'Send OTP'}
                </StyledButton>

                <Typography variant="caption" color="text.secondary" textAlign="center" sx={{ display: 'block', mt: 3 }}>
                  By continuing, you agree to our Terms of Service and Privacy Policy
                </Typography>
              </Box>
            </Fade>
          )}

          {/* Step 2: OTP Verification */}
          {step === 2 && (
            <Fade in={step === 2}>
              <Box>
                <ModernOtpWidget
                  phone={`+91 ${phone}`}
                  onVerify={handleVerifyOtp}
                  onResendOtp={handleResendOtp}
                  onChangePhone={handleChangePhone}
                  loading={otpLoading}
                  error={otpError}
                  success={otpSuccess}
                />
              </Box>
            </Fade>
          )}
        </StyledPaper>
      </Slide>
    </StyledContainer>
  );
};

export default ModernOtpLogin;
