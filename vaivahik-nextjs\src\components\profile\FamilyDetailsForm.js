import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  FormLabel,
  Select,
  MenuItem,
  InputLabel,
  FormHelperText,
  Divider,
  Alert,
  LinearProgress,
  Paper,
  RadioGroup,
  Radio,
  FormControlLabel,
  Snackbar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Help as HelpIcon,
  CheckCircle as CheckCircleIcon,
  FamilyRestroom as FamilyIcon
} from '@mui/icons-material';
import { useRouter } from 'next/router';

const FamilyDetailsForm = ({ userData, onSave, isLoading }) => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    // Family Type
    familyType: '',
    familyStatus: '',
    
    // Parents
    fatherName: '',
    fatherOccupation: '',
    motherName: '',
    motherOccupation: '',
    
    // Extended Family
    uncleName: '',
    siblings: '',
    familyContact: '',
    
    // Cultural Background
    motherTongue: '',
    marathiProficiency: '',
    kul: '',
    maharashtrianOrigin: false,
    nativePlace: '',
    nativeDistrict: ''
  });
  
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [success, setSuccess] = useState('');
  
  // Family Type Options
  const FAMILY_TYPE_OPTIONS = ['JOINT', 'NUCLEAR', 'EXTENDED'];
  const FAMILY_STATUS_OPTIONS = ['MIDDLE_CLASS', 'UPPER_MIDDLE_CLASS', 'AFFLUENT'];
  const MOTHER_TONGUE_OPTIONS = ['MARATHI', 'HINDI', 'ENGLISH', 'GUJARATI', 'KANNADA', 'TAMIL', 'TELUGU', 'OTHER'];
  const PROFICIENCY_OPTIONS = ['NATIVE', 'FLUENT', 'INTERMEDIATE', 'BASIC', 'NONE'];
  
  // Initialize form with user data if available
  useEffect(() => {
    if (userData && userData.familyDetails) {
      setFormData({
        ...formData,
        ...userData.familyDetails
      });
    }
  }, [userData]);
  
  // Calculate completion percentage
  useEffect(() => {
    const requiredFields = ['familyType', 'fatherName', 'motherName'];
    const optionalFields = [
      'familyStatus', 'fatherOccupation', 'motherOccupation', 
      'uncleName', 'siblings', 'familyContact', 'motherTongue',
      'marathiProficiency', 'kul', 'maharashtrianOrigin',
      'nativePlace', 'nativeDistrict'
    ];
    
    const totalFields = requiredFields.length + optionalFields.length;
    let completedFields = 0;
    
    // Count required fields
    requiredFields.forEach(field => {
      if (formData[field]) completedFields++;
    });
    
    // Count optional fields
    optionalFields.forEach(field => {
      if (formData[field] !== undefined && formData[field] !== '') completedFields++;
    });
    
    const percentage = Math.round((completedFields / totalFields) * 100);
    setCompletionPercentage(percentage);
  }, [formData]);
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Mark field as touched
    setTouched({ ...touched, [name]: true });
    
    // Clear error for this field if it exists
    if (errors[name]) {
      const { [name]: _, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };
  
  // Handle boolean input change
  const handleBooleanChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value === 'true' });
    setTouched({ ...touched, [name]: true });
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    // Required fields
    if (!formData.familyType) {
      newErrors.familyType = 'Family type is required';
    }
    
    if (!formData.fatherName) {
      newErrors.fatherName = 'Father\'s name is required';
    }
    
    if (!formData.motherName) {
      newErrors.motherName = 'Mother\'s name is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    // Call the onSave function with the form data
    onSave(formData);
    setSuccess('Family details saved successfully!');
  };
  
  // Handle back button
  const handleBack = () => {
    router.push('/profile');
  };
  
  return (
    <Box sx={{ mb: 4 }}>
      <Paper elevation={0} sx={{ p: 2, mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText', borderRadius: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FamilyIcon sx={{ mr: 1 }} />
          <Typography variant="h5" component="h1">
            Family Details
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ mt: 1 }}>
          Complete your family information to improve your profile and find better matches.
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
          <Box sx={{ flexGrow: 1, mr: 2 }}>
            <LinearProgress 
              variant="determinate" 
              value={completionPercentage} 
              sx={{ 
                height: 8, 
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.3)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'white'
                }
              }} 
            />
          </Box>
          <Typography variant="body2" fontWeight="bold">
            {completionPercentage}% Complete
          </Typography>
        </Box>
      </Paper>
      
      <form onSubmit={handleSubmit}>
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Family Type
              <Tooltip title="Information about your family structure">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.familyType}>
                  <FormLabel id="familyType-label">Family Type*</FormLabel>
                  <Select
                    labelId="familyType-label"
                    id="familyType"
                    name="familyType"
                    value={formData.familyType}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">Select Family Type</MenuItem>
                    <MenuItem value="JOINT">Joint Family</MenuItem>
                    <MenuItem value="NUCLEAR">Nuclear Family</MenuItem>
                    <MenuItem value="EXTENDED">Extended Family</MenuItem>
                  </Select>
                  {errors.familyType && <FormHelperText error>{errors.familyType}</FormHelperText>}
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel id="familyStatus-label">Family Status</FormLabel>
                  <Select
                    labelId="familyStatus-label"
                    id="familyStatus"
                    name="familyStatus"
                    value={formData.familyStatus}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">Select Family Status</MenuItem>
                    <MenuItem value="MIDDLE_CLASS">Middle Class</MenuItem>
                    <MenuItem value="UPPER_MIDDLE_CLASS">Upper Middle Class</MenuItem>
                    <MenuItem value="AFFLUENT">Affluent</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Parents Information
              <Tooltip title="Information about your parents">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.fatherName}>
                  <FormLabel htmlFor="fatherName">Father's Name*</FormLabel>
                  <TextField
                    id="fatherName"
                    name="fatherName"
                    value={formData.fatherName}
                    onChange={handleInputChange}
                    placeholder="Enter father's name"
                    error={!!errors.fatherName}
                    helperText={errors.fatherName}
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="fatherOccupation">Father's Occupation</FormLabel>
                  <TextField
                    id="fatherOccupation"
                    name="fatherOccupation"
                    value={formData.fatherOccupation}
                    onChange={handleInputChange}
                    placeholder="Enter father's occupation"
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.motherName}>
                  <FormLabel htmlFor="motherName">Mother's Name*</FormLabel>
                  <TextField
                    id="motherName"
                    name="motherName"
                    value={formData.motherName}
                    onChange={handleInputChange}
                    placeholder="Enter mother's name"
                    error={!!errors.motherName}
                    helperText={errors.motherName}
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="motherOccupation">Mother's Occupation</FormLabel>
                  <TextField
                    id="motherOccupation"
                    name="motherOccupation"
                    value={formData.motherOccupation}
                    onChange={handleInputChange}
                    placeholder="Enter mother's occupation"
                    size="small"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Extended Family
              <Tooltip title="Information about your siblings and extended family">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="siblings">Siblings</FormLabel>
                  <TextField
                    id="siblings"
                    name="siblings"
                    value={formData.siblings}
                    onChange={handleInputChange}
                    placeholder="Describe your siblings (e.g., 1 elder brother, 1 younger sister)"
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="uncleName">Uncle's Name (Optional)</FormLabel>
                  <TextField
                    id="uncleName"
                    name="uncleName"
                    value={formData.uncleName}
                    onChange={handleInputChange}
                    placeholder="Enter uncle's name if applicable"
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="familyContact">Family Contact (Optional)</FormLabel>
                  <TextField
                    id="familyContact"
                    name="familyContact"
                    value={formData.familyContact}
                    onChange={handleInputChange}
                    placeholder="Alternative family contact number"
                    size="small"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Card elevation={3} sx={{ mb: 3, borderRadius: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              Cultural Background
              <Tooltip title="Information about your cultural background and native place">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel id="motherTongue-label">Mother Tongue</FormLabel>
                  <Select
                    labelId="motherTongue-label"
                    id="motherTongue"
                    name="motherTongue"
                    value={formData.motherTongue}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">Select Mother Tongue</MenuItem>
                    <MenuItem value="MARATHI">Marathi</MenuItem>
                    <MenuItem value="HINDI">Hindi</MenuItem>
                    <MenuItem value="ENGLISH">English</MenuItem>
                    <MenuItem value="GUJARATI">Gujarati</MenuItem>
                    <MenuItem value="KANNADA">Kannada</MenuItem>
                    <MenuItem value="TAMIL">Tamil</MenuItem>
                    <MenuItem value="TELUGU">Telugu</MenuItem>
                    <MenuItem value="OTHER">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel id="marathiProficiency-label">Marathi Proficiency</FormLabel>
                  <Select
                    labelId="marathiProficiency-label"
                    id="marathiProficiency"
                    name="marathiProficiency"
                    value={formData.marathiProficiency}
                    onChange={handleInputChange}
                    size="small"
                  >
                    <MenuItem value="">Select Proficiency Level</MenuItem>
                    <MenuItem value="NATIVE">Native</MenuItem>
                    <MenuItem value="FLUENT">Fluent</MenuItem>
                    <MenuItem value="INTERMEDIATE">Intermediate</MenuItem>
                    <MenuItem value="BASIC">Basic</MenuItem>
                    <MenuItem value="NONE">None</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="kul">Kul (Family/Clan Name)</FormLabel>
                  <TextField
                    id="kul"
                    name="kul"
                    value={formData.kul}
                    onChange={handleInputChange}
                    placeholder="Enter your kul/clan name"
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel id="maharashtrianOrigin-label">Maharashtrian Origin</FormLabel>
                  <RadioGroup
                    aria-labelledby="maharashtrianOrigin-label"
                    name="maharashtrianOrigin"
                    value={formData.maharashtrianOrigin.toString()}
                    onChange={handleBooleanChange}
                    row
                  >
                    <FormControlLabel value="true" control={<Radio />} label="Yes" />
                    <FormControlLabel value="false" control={<Radio />} label="No" />
                  </RadioGroup>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="nativePlace">Native Place</FormLabel>
                  <TextField
                    id="nativePlace"
                    name="nativePlace"
                    value={formData.nativePlace}
                    onChange={handleInputChange}
                    placeholder="Enter your native place/village"
                    size="small"
                  />
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <FormLabel htmlFor="nativeDistrict">Native District</FormLabel>
                  <TextField
                    id="nativeDistrict"
                    name="nativeDistrict"
                    value={formData.nativeDistrict}
                    onChange={handleInputChange}
                    placeholder="Enter your native district"
                    size="small"
                  />
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Back to Profile
          </Button>
          
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={isLoading ? null : <SaveIcon />}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Family Details'}
          </Button>
        </Box>
      </form>
      
      <Snackbar
        open={!!success}
        autoHideDuration={6000}
        onClose={() => setSuccess('')}
        message={success}
      />
    </Box>
  );
};

export default FamilyDetailsForm;
