// src/pages/api/admin/success-analytics.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

export default async function handler(req, res) {
  // Check if user is authenticated and is an admin
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || !session.user || !session.user.isAdmin) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  // Handle POST request - fetch success analytics with filters
  if (req.method === 'POST') {
    try {
      const { filters } = req.body;
      
      // Fetch analytics from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/ai/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        },
        body: JSON.stringify({ filters })
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        return res.status(200).json({
          success: true,
          analytics: data.metrics
        });
      } else {
        // Fallback to mock data if backend API fails
        return res.status(200).json({
          success: true,
          analytics: getMockAnalytics(filters)
        });
      }
    } catch (error) {
      console.error('Error fetching success analytics:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch success analytics',
        error: error.message
      });
    }
  }
  
  // Handle GET request - fetch success analytics without filters
  if (req.method === 'GET') {
    try {
      // Fetch analytics from backend API
      const response = await fetch(`${process.env.BACKEND_API_URL}/api/admin/ai/analytics`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`
        }
      }).catch(() => null);
      
      if (response && response.ok) {
        const data = await response.json();
        
        // Process data for chart.js format
        const processedData = processAnalyticsData(data.metrics);
        
        return res.status(200).json({
          success: true,
          data: processedData
        });
      } else {
        // Fallback to mock data if backend API fails
        return res.status(200).json({
          success: true,
          data: getMockChartData()
        });
      }
    } catch (error) {
      console.error('Error fetching success analytics:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch success analytics',
        error: error.message
      });
    }
  }
  
  // Handle unsupported methods
  return res.status(405).json({ success: false, message: 'Method not allowed' });
}

// Helper function to process analytics data for chart.js
function processAnalyticsData(metrics) {
  // In a real implementation, this would transform the backend data into the format expected by chart.js
  // For now, return mock data
  return getMockChartData();
}

// Helper function to get mock analytics data
function getMockAnalytics(filters) {
  return {
    overallStats: {
      totalMatches: 5842,
      successfulMatches: 2156,
      successRate: 36.9,
      averageMatchScore: 72.4
    },
    matchQualityDistribution: [
      { quality: '90-100%', count: 523 },
      { quality: '80-89%', count: 892 },
      { quality: '70-79%', count: 1245 },
      { quality: '60-69%', count: 1876 },
      { quality: 'Below 60%', count: 1306 }
    ],
    matchTrends: [
      { month: 'Jan', successRate: 32.1 },
      { month: 'Feb', successRate: 33.5 },
      { month: 'Mar', successRate: 34.2 },
      { month: 'Apr', successRate: 35.7 },
      { month: 'May', successRate: 36.9 },
      { month: 'Jun', successRate: 38.2 }
    ],
    topSuccessFactors: [
      { factor: 'Same Sub-caste', weight: 82 },
      { factor: 'Similar Education', weight: 78 },
      { factor: 'Similar Age', weight: 76 },
      { factor: 'Same City', weight: 72 },
      { factor: 'Similar Income', weight: 68 },
      { factor: 'Similar Occupation', weight: 65 },
      { factor: 'Similar Lifestyle', weight: 62 },
      { factor: 'Similar Interests', weight: 58 }
    ],
    demographicInsights: {
      ageGroups: [
        { group: '18-25', successRate: 68 },
        { group: '26-35', successRate: 76 },
        { group: '36-45', successRate: 64 },
        { group: '46+', successRate: 52 }
      ],
      locations: [
        { location: 'Mumbai', successRate: 78 },
        { location: 'Pune', successRate: 75 },
        { location: 'Nagpur', successRate: 72 },
        { location: 'Nashik', successRate: 70 },
        { location: 'Aurangabad', successRate: 68 }
      ],
      educationLevels: [
        { level: 'Graduate', successRate: 74 },
        { level: 'Post-Graduate', successRate: 79 },
        { level: 'Doctorate', successRate: 82 },
        { level: 'Professional', successRate: 76 }
      ]
    }
  };
}

// Helper function to get mock chart data
function getMockChartData() {
  return {
    summaryMetrics: {
      totalSuccessStories: 2156,
      averageMatchScore: 72.4,
      averageUserSatisfaction: 85.2,
      algorithmAccuracy: 78.6,
      improvementFromLastPeriod: 5.3
    },
    matchSuccessRate: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'Match Success Rate (%)',
          data: [32.1, 33.5, 34.2, 35.7, 36.9, 38.2],
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          fill: true,
          tension: 0.4
        }
      ]
    },
    userSatisfaction: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'User Satisfaction (%)',
          data: [78.3, 79.5, 81.2, 82.7, 84.1, 85.2],
          borderColor: 'rgba(153, 102, 255, 1)',
          backgroundColor: 'rgba(153, 102, 255, 0.2)',
          fill: true,
          tension: 0.4
        }
      ]
    },
    successStories: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'Success Stories',
          data: [312, 345, 367, 389, 412, 431],
          backgroundColor: 'rgba(255, 159, 64, 0.7)'
        }
      ]
    },
    algorithmPerformance: {
      labels: ['Accuracy', 'Precision', 'Recall', 'F1 Score', 'Coverage', 'Diversity'],
      datasets: [
        {
          label: 'Current Algorithm',
          data: [0.78, 0.82, 0.75, 0.79, 0.85, 0.72],
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgba(54, 162, 235, 1)',
          pointBackgroundColor: 'rgba(54, 162, 235, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
        }
      ]
    },
    demographicBreakdown: {
      labels: ['18-25', '26-35', '36-45', '46+'],
      datasets: [
        {
          label: 'Success Rate by Age Group',
          data: [68, 76, 64, 52],
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    }
  };
}
